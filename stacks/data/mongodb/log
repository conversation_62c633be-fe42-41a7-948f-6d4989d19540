{"t":{"$date":"2024-04-09T02:25:11.632+00:00"},"s":"I",  "c":"NETWORK",  "id":4915701, "ctx":"main","msg":"Initialized wire specification","attr":{"spec":{"incomingExternalClient":{"minWireVersion":0,"maxWireVersion":13},"incomingInternalClient":{"minWireVersion":0,"maxWireVersion":13},"outgoing":{"minWireVersion":0,"maxWireVersion":13},"isInternalClient":true}}}
{"t":{"$date":"2024-04-09T02:25:11.633+00:00"},"s":"I",  "c":"CONTROL",  "id":23285,   "ctx":"main","msg":"Automatically disabling TLS 1.0, to force-enable TLS 1.0 specify --sslDisabledProtocols 'none'"}
{"t":{"$date":"2024-04-09T02:25:11.636+00:00"},"s":"W",  "c":"ASIO",     "id":22601,   "ctx":"main","msg":"No TransportLayer configured during NetworkInterface startup"}
{"t":{"$date":"2024-04-09T02:25:11.637+00:00"},"s":"I",  "c":"NETWORK",  "id":4648601, "ctx":"main","msg":"Implicit TCP FastOpen unavailable. If TCP FastOpen is required, set tcpFastOpenServer, tcpFastOpenClient, and tcpFastOpenQueueSize."}
{"t":{"$date":"2024-04-09T02:25:11.723+00:00"},"s":"W",  "c":"ASIO",     "id":22601,   "ctx":"main","msg":"No TransportLayer configured during NetworkInterface startup"}
{"t":{"$date":"2024-04-09T02:25:11.724+00:00"},"s":"W",  "c":"ASIO",     "id":22601,   "ctx":"main","msg":"No TransportLayer configured during NetworkInterface startup"}
{"t":{"$date":"2024-04-09T02:25:11.725+00:00"},"s":"I",  "c":"REPL",     "id":5123008, "ctx":"main","msg":"Successfully registered PrimaryOnlyService","attr":{"service":"TenantMigrationDonorService","ns":"config.tenantMigrationDonors"}}
{"t":{"$date":"2024-04-09T02:25:11.726+00:00"},"s":"I",  "c":"REPL",     "id":5123008, "ctx":"main","msg":"Successfully registered PrimaryOnlyService","attr":{"service":"TenantMigrationRecipientService","ns":"config.tenantMigrationRecipients"}}
{"t":{"$date":"2024-04-09T02:25:11.726+00:00"},"s":"I",  "c":"CONTROL",  "id":5945603, "ctx":"main","msg":"Multi threading initialized"}
{"t":{"$date":"2024-04-09T02:25:11.727+00:00"},"s":"I",  "c":"CONTROL",  "id":4615611, "ctx":"initandlisten","msg":"MongoDB starting","attr":{"pid":174,"port":27017,"dbPath":"/appsmith-stacks/data/mongodb","architecture":"64-bit","host":"f3f425c8c882"}}
{"t":{"$date":"2024-04-09T02:25:11.728+00:00"},"s":"I",  "c":"CONTROL",  "id":23403,   "ctx":"initandlisten","msg":"Build Info","attr":{"buildInfo":{"version":"5.0.26","gitVersion":"0b4f1ea980b5380a66425a90b414106a191365f4","openSSLVersion":"OpenSSL 1.1.1f  31 Mar 2020","modules":[],"allocator":"tcmalloc","environment":{"distmod":"ubuntu2004","distarch":"x86_64","target_arch":"x86_64"}}}}
{"t":{"$date":"2024-04-09T02:25:11.728+00:00"},"s":"I",  "c":"CONTROL",  "id":51765,   "ctx":"initandlisten","msg":"Operating System","attr":{"os":{"name":"Ubuntu","version":"20.04"}}}
{"t":{"$date":"2024-04-09T02:25:11.728+00:00"},"s":"I",  "c":"CONTROL",  "id":21951,   "ctx":"initandlisten","msg":"Options set by command line","attr":{"options":{"net":{"bindIp":"localhost","port":27017},"processManagement":{"fork":true},"replication":{"replSet":"mr1"},"security":{"keyFile":"/tmp/appsmith/mongodb-key"},"storage":{"dbPath":"/appsmith-stacks/data/mongodb"},"systemLog":{"destination":"file","path":"/appsmith-stacks/data/mongodb/log"}}}}
{"t":{"$date":"2024-04-09T02:25:11.738+00:00"},"s":"I",  "c":"STORAGE",  "id":22270,   "ctx":"initandlisten","msg":"Storage engine to use detected by data files","attr":{"dbpath":"/appsmith-stacks/data/mongodb","storageEngine":"wiredTiger"}}
{"t":{"$date":"2024-04-09T02:25:11.741+00:00"},"s":"I",  "c":"STORAGE",  "id":22315,   "ctx":"initandlisten","msg":"Opening WiredTiger","attr":{"config":"create,cache_size=3467M,session_max=33000,eviction=(threads_min=4,threads_max=4),config_base=false,statistics=(fast),log=(enabled=true,archive=true,path=journal,compressor=snappy),builtin_extension_config=(zstd=(compression_level=6)),file_manager=(close_idle_time=600,close_scan_interval=10,close_handle_minimum=2000),statistics_log=(wait=0),verbose=[recovery_progress,checkpoint_progress,compact_progress],"}}
{"t":{"$date":"2024-04-09T02:25:11.957+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"initandlisten","msg":"WiredTiger message","attr":{"message":"[1712629511:956965][174:0x7fbfbfc12c80], txn-recover: [WT_VERB_RECOVERY_PROGRESS] Recovering log 1 through 2"}}
{"t":{"$date":"2024-04-09T02:25:12.738+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"initandlisten","msg":"WiredTiger message","attr":{"message":"[1712629512:738776][174:0x7fbfbfc12c80], txn-recover: [WT_VERB_RECOVERY_PROGRESS] Recovering log 2 through 2"}}
{"t":{"$date":"2024-04-09T02:25:13.785+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"initandlisten","msg":"WiredTiger message","attr":{"message":"[1712629513:785109][174:0x7fbfbfc12c80], txn-recover: [WT_VERB_RECOVERY_ALL] Main recovery loop: starting at 1/31872 to 2/256"}}
{"t":{"$date":"2024-04-09T02:25:13.857+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"initandlisten","msg":"WiredTiger message","attr":{"message":"[1712629513:857047][174:0x7fbfbfc12c80], txn-recover: [WT_VERB_RECOVERY_PROGRESS] Recovering log 1 through 2"}}
{"t":{"$date":"2024-04-09T02:25:13.916+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"initandlisten","msg":"WiredTiger message","attr":{"message":"[1712629513:916294][174:0x7fbfbfc12c80], txn-recover: [WT_VERB_RECOVERY_PROGRESS] Recovering log 2 through 2"}}
{"t":{"$date":"2024-04-09T02:25:13.951+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"initandlisten","msg":"WiredTiger message","attr":{"message":"[1712629513:951904][174:0x7fbfbfc12c80], txn-recover: [WT_VERB_RECOVERY_PROGRESS] recovery log replay has successfully finished and ran for 2000 milliseconds"}}
{"t":{"$date":"2024-04-09T02:25:13.953+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"initandlisten","msg":"WiredTiger message","attr":{"message":"[1712629513:953078][174:0x7fbfbfc12c80], txn-recover: [WT_VERB_RECOVERY_ALL] Set global recovery timestamp: (0, 0)"}}
{"t":{"$date":"2024-04-09T02:25:13.953+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"initandlisten","msg":"WiredTiger message","attr":{"message":"[1712629513:953716][174:0x7fbfbfc12c80], txn-recover: [WT_VERB_RECOVERY_ALL] Set global oldest timestamp: (0, 0)"}}
{"t":{"$date":"2024-04-09T02:25:13.954+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"initandlisten","msg":"WiredTiger message","attr":{"message":"[1712629513:954601][174:0x7fbfbfc12c80], txn-recover: [WT_VERB_RECOVERY_PROGRESS] recovery rollback to stable has successfully finished and ran for 0 milliseconds"}}
{"t":{"$date":"2024-04-09T02:25:13.955+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"initandlisten","msg":"WiredTiger message","attr":{"message":"[1712629513:955784][174:0x7fbfbfc12c80], WT_SESSION.checkpoint: [WT_VERB_CHECKPOINT_PROGRESS] saving checkpoint snapshot min: 1, snapshot max: 1 snapshot count: 0, oldest timestamp: (0, 0) , meta checkpoint timestamp: (0, 0) base write gen: 7"}}
{"t":{"$date":"2024-04-09T02:25:13.964+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"initandlisten","msg":"WiredTiger message","attr":{"message":"[1712629513:964671][174:0x7fbfbfc12c80], txn-recover: [WT_VERB_RECOVERY_PROGRESS] recovery checkpoint has successfully finished and ran for 9 milliseconds"}}
{"t":{"$date":"2024-04-09T02:25:13.967+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"initandlisten","msg":"WiredTiger message","attr":{"message":"[1712629513:967004][174:0x7fbfbfc12c80], txn-recover: [WT_VERB_RECOVERY_PROGRESS] recovery was completed successfully and took 2015ms, including 2000ms for the log replay, 0ms for the rollback to stable, and 9ms for the checkpoint."}}
{"t":{"$date":"2024-04-09T02:25:13.969+00:00"},"s":"I",  "c":"STORAGE",  "id":4795906, "ctx":"initandlisten","msg":"WiredTiger opened","attr":{"durationMillis":2227}}
{"t":{"$date":"2024-04-09T02:25:13.969+00:00"},"s":"I",  "c":"RECOVERY", "id":23987,   "ctx":"initandlisten","msg":"WiredTiger recoveryTimestamp","attr":{"recoveryTimestamp":{"$timestamp":{"t":0,"i":0}}}}
{"t":{"$date":"2024-04-09T02:25:13.982+00:00"},"s":"I",  "c":"STORAGE",  "id":22262,   "ctx":"initandlisten","msg":"Timestamp monitor starting"}
{"t":{"$date":"2024-04-09T02:25:13.987+00:00"},"s":"W",  "c":"CONTROL",  "id":22138,   "ctx":"initandlisten","msg":"You are running this process as the root user, which is not recommended","tags":["startupWarnings"]}
{"t":{"$date":"2024-04-09T02:25:13.999+00:00"},"s":"I",  "c":"NETWORK",  "id":4915702, "ctx":"initandlisten","msg":"Updated wire specification","attr":{"oldSpec":{"incomingExternalClient":{"minWireVersion":0,"maxWireVersion":13},"incomingInternalClient":{"minWireVersion":0,"maxWireVersion":13},"outgoing":{"minWireVersion":0,"maxWireVersion":13},"isInternalClient":true},"newSpec":{"incomingExternalClient":{"minWireVersion":0,"maxWireVersion":13},"incomingInternalClient":{"minWireVersion":13,"maxWireVersion":13},"outgoing":{"minWireVersion":13,"maxWireVersion":13},"isInternalClient":true}}}
{"t":{"$date":"2024-04-09T02:25:14.001+00:00"},"s":"I",  "c":"STORAGE",  "id":5071100, "ctx":"initandlisten","msg":"Clearing temp directory"}
{"t":{"$date":"2024-04-09T02:25:14.006+00:00"},"s":"I",  "c":"CONTROL",  "id":20536,   "ctx":"initandlisten","msg":"Flow Control is enabled on this deployment"}
{"t":{"$date":"2024-04-09T02:25:14.010+00:00"},"s":"I",  "c":"SHARDING", "id":20997,   "ctx":"initandlisten","msg":"Refreshed RWC defaults","attr":{"newDefaults":{}}}
{"t":{"$date":"2024-04-09T02:25:14.013+00:00"},"s":"I",  "c":"FTDC",     "id":20625,   "ctx":"initandlisten","msg":"Initializing full-time diagnostic data capture","attr":{"dataDirectory":"/appsmith-stacks/data/mongodb/diagnostic.data"}}
{"t":{"$date":"2024-04-09T02:25:14.020+00:00"},"s":"I",  "c":"REPL",     "id":6015317, "ctx":"initandlisten","msg":"Setting new configuration state","attr":{"newState":"ConfigStartingUp","oldState":"ConfigPreStart"}}
{"t":{"$date":"2024-04-09T02:25:14.020+00:00"},"s":"I",  "c":"-",        "id":4939300, "ctx":"monitoring-keys-for-HMAC","msg":"Failed to refresh key cache","attr":{"error":"ReadConcernMajorityNotAvailableYet: Read concern majority reads are currently not possible.","nextWakeupMillis":200}}
{"t":{"$date":"2024-04-09T02:25:14.021+00:00"},"s":"I",  "c":"REPL",     "id":4280500, "ctx":"initandlisten","msg":"Attempting to create internal replication collections"}
{"t":{"$date":"2024-04-09T02:25:14.022+00:00"},"s":"I",  "c":"STORAGE",  "id":20320,   "ctx":"initandlisten","msg":"createCollection","attr":{"namespace":"local.replset.oplogTruncateAfterPoint","uuidDisposition":"generated","uuid":{"uuid":{"$uuid":"2de7670a-ea72-439d-b657-7b6743f3e630"}},"options":{}}}
{"t":{"$date":"2024-04-09T02:25:14.049+00:00"},"s":"I",  "c":"INDEX",    "id":20345,   "ctx":"initandlisten","msg":"Index build: done building","attr":{"buildUUID":null,"namespace":"local.replset.oplogTruncateAfterPoint","index":"_id_","commitTimestamp":null}}
{"t":{"$date":"2024-04-09T02:25:14.050+00:00"},"s":"I",  "c":"STORAGE",  "id":20320,   "ctx":"initandlisten","msg":"createCollection","attr":{"namespace":"local.replset.minvalid","uuidDisposition":"generated","uuid":{"uuid":{"$uuid":"73b419b9-fe72-4057-a6cd-9a21e10d9078"}},"options":{}}}
{"t":{"$date":"2024-04-09T02:25:14.065+00:00"},"s":"I",  "c":"INDEX",    "id":20345,   "ctx":"initandlisten","msg":"Index build: done building","attr":{"buildUUID":null,"namespace":"local.replset.minvalid","index":"_id_","commitTimestamp":null}}
{"t":{"$date":"2024-04-09T02:25:14.067+00:00"},"s":"I",  "c":"STORAGE",  "id":20320,   "ctx":"initandlisten","msg":"createCollection","attr":{"namespace":"local.replset.election","uuidDisposition":"generated","uuid":{"uuid":{"$uuid":"13c3ab58-32fc-4c82-8179-1d633d3fc4b4"}},"options":{}}}
{"t":{"$date":"2024-04-09T02:25:14.085+00:00"},"s":"I",  "c":"INDEX",    "id":20345,   "ctx":"initandlisten","msg":"Index build: done building","attr":{"buildUUID":null,"namespace":"local.replset.election","index":"_id_","commitTimestamp":null}}
{"t":{"$date":"2024-04-09T02:25:14.086+00:00"},"s":"I",  "c":"REPL",     "id":4280501, "ctx":"initandlisten","msg":"Attempting to load local voted for document"}
{"t":{"$date":"2024-04-09T02:25:14.087+00:00"},"s":"I",  "c":"REPL",     "id":21311,   "ctx":"initandlisten","msg":"Did not find local initialized voted for document at startup"}
{"t":{"$date":"2024-04-09T02:25:14.088+00:00"},"s":"I",  "c":"REPL",     "id":4280502, "ctx":"initandlisten","msg":"Searching for local Rollback ID document"}
{"t":{"$date":"2024-04-09T02:25:14.088+00:00"},"s":"I",  "c":"REPL",     "id":21312,   "ctx":"initandlisten","msg":"Did not find local Rollback ID document at startup. Creating one"}
{"t":{"$date":"2024-04-09T02:25:14.089+00:00"},"s":"I",  "c":"STORAGE",  "id":20320,   "ctx":"initandlisten","msg":"createCollection","attr":{"namespace":"local.system.rollback.id","uuidDisposition":"generated","uuid":{"uuid":{"$uuid":"24b05624-c156-4bb1-a241-118dcbd5da09"}},"options":{}}}
{"t":{"$date":"2024-04-09T02:25:14.107+00:00"},"s":"I",  "c":"INDEX",    "id":20345,   "ctx":"initandlisten","msg":"Index build: done building","attr":{"buildUUID":null,"namespace":"local.system.rollback.id","index":"_id_","commitTimestamp":null}}
{"t":{"$date":"2024-04-09T02:25:14.108+00:00"},"s":"I",  "c":"REPL",     "id":21531,   "ctx":"initandlisten","msg":"Initialized the rollback ID","attr":{"rbid":1}}
{"t":{"$date":"2024-04-09T02:25:14.109+00:00"},"s":"I",  "c":"REPL",     "id":21313,   "ctx":"initandlisten","msg":"Did not find local replica set configuration document at startup","attr":{"error":{"code":47,"codeName":"NoMatchingDocument","errmsg":"Did not find replica set configuration document in local.system.replset"}}}
{"t":{"$date":"2024-04-09T02:25:14.109+00:00"},"s":"I",  "c":"REPL",     "id":6015317, "ctx":"initandlisten","msg":"Setting new configuration state","attr":{"newState":"ConfigUninitialized","oldState":"ConfigStartingUp"}}
{"t":{"$date":"2024-04-09T02:25:14.110+00:00"},"s":"I",  "c":"STORAGE",  "id":20320,   "ctx":"initandlisten","msg":"createCollection","attr":{"namespace":"local.system.views","uuidDisposition":"generated","uuid":{"uuid":{"$uuid":"2a811611-67de-41fa-b72f-f618cd5679ad"}},"options":{}}}
{"t":{"$date":"2024-04-09T02:25:14.127+00:00"},"s":"I",  "c":"INDEX",    "id":20345,   "ctx":"initandlisten","msg":"Index build: done building","attr":{"buildUUID":null,"namespace":"local.system.views","index":"_id_","commitTimestamp":null}}
{"t":{"$date":"2024-04-09T02:25:14.129+00:00"},"s":"I",  "c":"CONTROL",  "id":20714,   "ctx":"LogicalSessionCacheRefresh","msg":"Failed to refresh session cache, will try again at the next refresh interval","attr":{"error":"NotYetInitialized: Replication has not yet been configured"}}
{"t":{"$date":"2024-04-09T02:25:14.130+00:00"},"s":"I",  "c":"REPL",     "id":40440,   "ctx":"initandlisten","msg":"Starting the TopologyVersionObserver"}
{"t":{"$date":"2024-04-09T02:25:14.131+00:00"},"s":"I",  "c":"CONTROL",  "id":20711,   "ctx":"LogicalSessionCacheReap","msg":"Failed to reap transaction table","attr":{"error":"NotYetInitialized: Replication has not yet been configured"}}
{"t":{"$date":"2024-04-09T02:25:14.131+00:00"},"s":"I",  "c":"REPL",     "id":40445,   "ctx":"TopologyVersionObserver","msg":"Started TopologyVersionObserver"}
{"t":{"$date":"2024-04-09T02:25:14.133+00:00"},"s":"I",  "c":"NETWORK",  "id":23015,   "ctx":"listener","msg":"Listening on","attr":{"address":"/tmp/mongodb-27017.sock"}}
{"t":{"$date":"2024-04-09T02:25:14.133+00:00"},"s":"I",  "c":"NETWORK",  "id":23015,   "ctx":"listener","msg":"Listening on","attr":{"address":"127.0.0.1"}}
{"t":{"$date":"2024-04-09T02:25:14.134+00:00"},"s":"I",  "c":"NETWORK",  "id":23016,   "ctx":"listener","msg":"Waiting for connections","attr":{"port":27017,"ssl":"off"}}
{"t":{"$date":"2024-04-09T02:25:14.223+00:00"},"s":"I",  "c":"-",        "id":4939300, "ctx":"monitoring-keys-for-HMAC","msg":"Failed to refresh key cache","attr":{"error":"ReadConcernMajorityNotAvailableYet: Read concern majority reads are currently not possible.","nextWakeupMillis":400}}
{"t":{"$date":"2024-04-09T02:25:14.627+00:00"},"s":"I",  "c":"-",        "id":4939300, "ctx":"monitoring-keys-for-HMAC","msg":"Failed to refresh key cache","attr":{"error":"ReadConcernMajorityNotAvailableYet: Read concern majority reads are currently not possible.","nextWakeupMillis":600}}
{"t":{"$date":"2024-04-09T02:25:15.233+00:00"},"s":"I",  "c":"-",        "id":4939300, "ctx":"monitoring-keys-for-HMAC","msg":"Failed to refresh key cache","attr":{"error":"ReadConcernMajorityNotAvailableYet: Read concern majority reads are currently not possible.","nextWakeupMillis":800}}
{"t":{"$date":"2024-04-09T02:25:16.037+00:00"},"s":"I",  "c":"-",        "id":4939300, "ctx":"monitoring-keys-for-HMAC","msg":"Failed to refresh key cache","attr":{"error":"ReadConcernMajorityNotAvailableYet: Read concern majority reads are currently not possible.","nextWakeupMillis":1000}}
{"t":{"$date":"2024-04-09T02:25:17.041+00:00"},"s":"I",  "c":"-",        "id":4939300, "ctx":"monitoring-keys-for-HMAC","msg":"Failed to refresh key cache","attr":{"error":"ReadConcernMajorityNotAvailableYet: Read concern majority reads are currently not possible.","nextWakeupMillis":1200}}
{"t":{"$date":"2024-04-09T02:25:18.246+00:00"},"s":"I",  "c":"-",        "id":4939300, "ctx":"monitoring-keys-for-HMAC","msg":"Failed to refresh key cache","attr":{"error":"ReadConcernMajorityNotAvailableYet: Read concern majority reads are currently not possible.","nextWakeupMillis":1400}}
{"t":{"$date":"2024-04-09T02:25:19.649+00:00"},"s":"I",  "c":"-",        "id":4939300, "ctx":"monitoring-keys-for-HMAC","msg":"Failed to refresh key cache","attr":{"error":"ReadConcernMajorityNotAvailableYet: Read concern majority reads are currently not possible.","nextWakeupMillis":1600}}
{"t":{"$date":"2024-04-09T02:25:21.252+00:00"},"s":"I",  "c":"-",        "id":4939300, "ctx":"monitoring-keys-for-HMAC","msg":"Failed to refresh key cache","attr":{"error":"ReadConcernMajorityNotAvailableYet: Read concern majority reads are currently not possible.","nextWakeupMillis":1800}}
{"t":{"$date":"2024-04-09T02:25:23.057+00:00"},"s":"I",  "c":"-",        "id":4939300, "ctx":"monitoring-keys-for-HMAC","msg":"Failed to refresh key cache","attr":{"error":"ReadConcernMajorityNotAvailableYet: Read concern majority reads are currently not possible.","nextWakeupMillis":2000}}
{"t":{"$date":"2024-04-09T02:25:24.458+00:00"},"s":"I",  "c":"NETWORK",  "id":22943,   "ctx":"listener","msg":"Connection accepted","attr":{"remote":"127.0.0.1:35876","uuid":"31a2a8eb-9690-4df0-bd4f-4ce4ed585ddb","connectionId":1,"connectionCount":1}}
{"t":{"$date":"2024-04-09T02:25:24.468+00:00"},"s":"I",  "c":"NETWORK",  "id":51800,   "ctx":"conn1","msg":"client metadata","attr":{"remote":"127.0.0.1:35876","client":"conn1","negotiatedCompressors":[],"doc":{"application":{"name":"mongosh 2.2.2"},"driver":{"name":"nodejs|mongosh","version":"6.5.0|2.2.2"},"platform":"Node.js v20.11.1, LE","os":{"name":"linux","architecture":"x64","version":"3.10.0-327.22.2.el7.x86_64","type":"Linux"},"env":{"container":{"runtime":"docker"}}}}}
{"t":{"$date":"2024-04-09T02:25:24.486+00:00"},"s":"I",  "c":"NETWORK",  "id":22943,   "ctx":"listener","msg":"Connection accepted","attr":{"remote":"127.0.0.1:35886","uuid":"e247a00d-f6c0-4074-86bb-7069fec34ac6","connectionId":2,"connectionCount":2}}
{"t":{"$date":"2024-04-09T02:25:24.488+00:00"},"s":"I",  "c":"NETWORK",  "id":51800,   "ctx":"conn2","msg":"client metadata","attr":{"remote":"127.0.0.1:35886","client":"conn2","negotiatedCompressors":[],"doc":{"application":{"name":"mongosh 2.2.2"},"driver":{"name":"nodejs|mongosh","version":"6.5.0|2.2.2"},"platform":"Node.js v20.11.1, LE","os":{"name":"linux","architecture":"x64","version":"3.10.0-327.22.2.el7.x86_64","type":"Linux"},"env":{"container":{"runtime":"docker"}}}}}
{"t":{"$date":"2024-04-09T02:25:24.505+00:00"},"s":"I",  "c":"ACCESS",   "id":20250,   "ctx":"conn2","msg":"Authentication succeeded","attr":{"mechanism":"SCRAM-SHA-256","speculative":true,"principalName":"appsmith","authenticationDatabase":"appsmith","remote":"127.0.0.1:35886","extraInfo":{}}}
{"t":{"$date":"2024-04-09T02:25:24.521+00:00"},"s":"I",  "c":"NETWORK",  "id":22943,   "ctx":"listener","msg":"Connection accepted","attr":{"remote":"127.0.0.1:35896","uuid":"03da86e4-9c9f-4f9c-a391-1638b4139837","connectionId":3,"connectionCount":3}}
{"t":{"$date":"2024-04-09T02:25:24.522+00:00"},"s":"I",  "c":"NETWORK",  "id":22943,   "ctx":"listener","msg":"Connection accepted","attr":{"remote":"127.0.0.1:35900","uuid":"fd360657-880c-44c1-9a78-8af11bdcbda7","connectionId":4,"connectionCount":4}}
{"t":{"$date":"2024-04-09T02:25:24.530+00:00"},"s":"I",  "c":"NETWORK",  "id":51800,   "ctx":"conn3","msg":"client metadata","attr":{"remote":"127.0.0.1:35896","client":"conn3","negotiatedCompressors":[],"doc":{"application":{"name":"mongosh 2.2.2"},"driver":{"name":"nodejs|mongosh","version":"6.5.0|2.2.2"},"platform":"Node.js v20.11.1, LE","os":{"name":"linux","architecture":"x64","version":"3.10.0-327.22.2.el7.x86_64","type":"Linux"},"env":{"container":{"runtime":"docker"}}}}}
{"t":{"$date":"2024-04-09T02:25:24.531+00:00"},"s":"I",  "c":"NETWORK",  "id":51800,   "ctx":"conn4","msg":"client metadata","attr":{"remote":"127.0.0.1:35900","client":"conn4","negotiatedCompressors":[],"doc":{"application":{"name":"mongosh 2.2.2"},"driver":{"name":"nodejs|mongosh","version":"6.5.0|2.2.2"},"platform":"Node.js v20.11.1, LE","os":{"name":"linux","architecture":"x64","version":"3.10.0-327.22.2.el7.x86_64","type":"Linux"},"env":{"container":{"runtime":"docker"}}}}}
{"t":{"$date":"2024-04-09T02:25:24.533+00:00"},"s":"I",  "c":"ACCESS",   "id":20250,   "ctx":"conn3","msg":"Authentication succeeded","attr":{"mechanism":"SCRAM-SHA-256","speculative":true,"principalName":"appsmith","authenticationDatabase":"appsmith","remote":"127.0.0.1:35896","extraInfo":{}}}
{"t":{"$date":"2024-04-09T02:25:24.534+00:00"},"s":"I",  "c":"ACCESS",   "id":20250,   "ctx":"conn4","msg":"Authentication succeeded","attr":{"mechanism":"SCRAM-SHA-256","speculative":true,"principalName":"appsmith","authenticationDatabase":"appsmith","remote":"127.0.0.1:35900","extraInfo":{}}}
{"t":{"$date":"2024-04-09T02:25:24.599+00:00"},"s":"I",  "c":"COMMAND",  "id":21577,   "ctx":"conn4","msg":"Initiate: no configuration specified. Using a default configuration for the set"}
{"t":{"$date":"2024-04-09T02:25:24.600+00:00"},"s":"I",  "c":"COMMAND",  "id":21578,   "ctx":"conn4","msg":"Created configuration for initiation","attr":{"config":"{ _id: \"mr1\", version: 1, members: [ { _id: 0, host: \"localhost:27017\" } ] }"}}
{"t":{"$date":"2024-04-09T02:25:24.600+00:00"},"s":"I",  "c":"REPL",     "id":21356,   "ctx":"conn4","msg":"replSetInitiate admin command received from client"}
{"t":{"$date":"2024-04-09T02:25:24.601+00:00"},"s":"I",  "c":"REPL",     "id":6015317, "ctx":"conn4","msg":"Setting new configuration state","attr":{"newState":"ConfigInitiating","oldState":"ConfigUninitialized"}}
{"t":{"$date":"2024-04-09T02:25:24.602+00:00"},"s":"I",  "c":"NETWORK",  "id":22943,   "ctx":"listener","msg":"Connection accepted","attr":{"remote":"127.0.0.1:35910","uuid":"73f976e4-d99f-422d-8bf9-40317b2b7e3f","connectionId":6,"connectionCount":5}}
{"t":{"$date":"2024-04-09T02:25:24.604+00:00"},"s":"W",  "c":"COMMAND",  "id":5578800, "ctx":"conn6","msg":"Deprecated operation requested. The client driver may require an upgrade in order to ensure compatibility with future server versions. For more details see https://dochub.mongodb.org/core/legacy-opcode-compatibility","attr":{"op":"query","clientInfo":{"address":"127.0.0.1:35910"}}}
{"t":{"$date":"2024-04-09T02:25:24.665+00:00"},"s":"I",  "c":"ACCESS",   "id":20250,   "ctx":"conn6","msg":"Authentication succeeded","attr":{"mechanism":"SCRAM-SHA-256","speculative":false,"principalName":"__system","authenticationDatabase":"local","remote":"127.0.0.1:35910","extraInfo":{}}}
{"t":{"$date":"2024-04-09T02:25:24.666+00:00"},"s":"I",  "c":"REPL",     "id":21357,   "ctx":"conn4","msg":"replSetInitiate config object parses ok","attr":{"numMembers":1}}
{"t":{"$date":"2024-04-09T02:25:24.666+00:00"},"s":"I",  "c":"NETWORK",  "id":22944,   "ctx":"conn6","msg":"Connection ended","attr":{"remote":"127.0.0.1:35910","uuid":"73f976e4-d99f-422d-8bf9-40317b2b7e3f","connectionId":6,"connectionCount":4}}
{"t":{"$date":"2024-04-09T02:25:24.668+00:00"},"s":"I",  "c":"REPL",     "id":21251,   "ctx":"conn4","msg":"Creating replication oplog","attr":{"oplogSizeMB":3041}}
{"t":{"$date":"2024-04-09T02:25:24.669+00:00"},"s":"I",  "c":"STORAGE",  "id":20320,   "ctx":"conn4","msg":"createCollection","attr":{"namespace":"local.oplog.rs","uuidDisposition":"generated","uuid":{"uuid":{"$uuid":"0db6e017-2b89-4718-adaf-dc35b23adfe9"}},"options":{"capped":true,"size":3188849868,"autoIndexId":false}}}
{"t":{"$date":"2024-04-09T02:25:24.678+00:00"},"s":"I",  "c":"STORAGE",  "id":22383,   "ctx":"conn4","msg":"The size storer reports that the oplog contains","attr":{"numRecords":0,"dataSize":0}}
{"t":{"$date":"2024-04-09T02:25:24.679+00:00"},"s":"I",  "c":"STORAGE",  "id":22382,   "ctx":"conn4","msg":"WiredTiger record store oplog processing finished","attr":{"durationMillis":1}}
{"t":{"$date":"2024-04-09T02:25:24.739+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"conn4","msg":"WiredTiger message","attr":{"message":"[1712629524:739333][174:0x7fbfa90cd700], WT_SESSION.checkpoint: [WT_VERB_CHECKPOINT_PROGRESS] saving checkpoint snapshot min: 56, snapshot max: 56 snapshot count: 0, oldest timestamp: (0, 0) , meta checkpoint timestamp: (0, 0) base write gen: 7"}}
{"t":{"$date":"2024-04-09T02:25:24.751+00:00"},"s":"I",  "c":"STORAGE",  "id":20320,   "ctx":"conn4","msg":"createCollection","attr":{"namespace":"local.system.replset","uuidDisposition":"generated","uuid":{"uuid":{"$uuid":"2c7f7b0a-45de-4c58-9fe0-123e0a6eedee"}},"options":{}}}
{"t":{"$date":"2024-04-09T02:25:24.767+00:00"},"s":"I",  "c":"INDEX",    "id":20345,   "ctx":"conn4","msg":"Index build: done building","attr":{"buildUUID":null,"namespace":"local.system.replset","index":"_id_","commitTimestamp":{"$timestamp":{"t":1712629524,"i":1}}}}
{"t":{"$date":"2024-04-09T02:25:24.776+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"conn4","msg":"WiredTiger message","attr":{"message":"[1712629524:776023][174:0x7fbfa90cd700], WT_SESSION.checkpoint: [WT_VERB_CHECKPOINT_PROGRESS] saving checkpoint snapshot min: 65, snapshot max: 65 snapshot count: 0, oldest timestamp: (0, 0) , meta checkpoint timestamp: (0, 0) base write gen: 7"}}
{"t":{"$date":"2024-04-09T02:25:24.791+00:00"},"s":"I",  "c":"REPL",     "id":5872101, "ctx":"conn4","msg":"Taking a stable checkpoint for replSetInitiate"}
{"t":{"$date":"2024-04-09T02:25:24.792+00:00"},"s":"I",  "c":"REPL",     "id":5872100, "ctx":"conn4","msg":"Updating commit point for initiate","attr":{"_lastCommittedOpTimeAndWallTime":"{ ts: Timestamp(1712629524, 1), t: -1 }, 2024-04-09T02:25:24.766+00:00"}}
{"t":{"$date":"2024-04-09T02:25:24.793+00:00"},"s":"I",  "c":"STORAGE",  "id":22310,   "ctx":"conn4","msg":"Triggering the first stable checkpoint","attr":{"initialDataTimestamp":{"$timestamp":{"t":1712629524,"i":1}},"prevStableTimestamp":{"$timestamp":{"t":0,"i":0}},"currStableTimestamp":{"$timestamp":{"t":1712629524,"i":1}}}}
{"t":{"$date":"2024-04-09T02:25:24.794+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"conn4","msg":"WiredTiger message","attr":{"message":"[1712629524:794811][174:0x7fbfa90cd700], WT_SESSION.checkpoint: [WT_VERB_CHECKPOINT_PROGRESS] saving checkpoint snapshot min: 67, snapshot max: 67 snapshot count: 0, oldest timestamp: (1712629524, 1) , meta checkpoint timestamp: (1712629524, 1) base write gen: 7"}}
{"t":{"$date":"2024-04-09T02:25:24.808+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"Checkpointer","msg":"WiredTiger message","attr":{"message":"[1712629524:808397][174:0x7fbfb73ec700], WT_SESSION.checkpoint: [WT_VERB_CHECKPOINT_PROGRESS] saving checkpoint snapshot min: 69, snapshot max: 69 snapshot count: 0, oldest timestamp: (1712629524, 1) , meta checkpoint timestamp: (1712629524, 1) base write gen: 7"}}
{"t":{"$date":"2024-04-09T02:25:24.808+00:00"},"s":"I",  "c":"REPL",     "id":6015317, "ctx":"conn4","msg":"Setting new configuration state","attr":{"newState":"ConfigSteady","oldState":"ConfigInitiating"}}
{"t":{"$date":"2024-04-09T02:25:24.810+00:00"},"s":"I",  "c":"REPL",     "id":21392,   "ctx":"conn4","msg":"New replica set config in use","attr":{"config":{"_id":"mr1","version":1,"term":0,"members":[{"_id":0,"host":"localhost:27017","arbiterOnly":false,"buildIndexes":true,"hidden":false,"priority":1,"tags":{},"secondaryDelaySecs":0,"votes":1}],"protocolVersion":1,"writeConcernMajorityJournalDefault":true,"settings":{"chainingAllowed":true,"heartbeatIntervalMillis":2000,"heartbeatTimeoutSecs":10,"electionTimeoutMillis":10000,"catchUpTimeoutMillis":-1,"catchUpTakeoverDelayMillis":30000,"getLastErrorModes":{},"getLastErrorDefaults":{"w":1,"wtimeout":0},"replicaSetId":{"$oid":"6614a714bc5de5bfe3a48011"}}}}}
{"t":{"$date":"2024-04-09T02:25:24.812+00:00"},"s":"I",  "c":"REPL",     "id":21393,   "ctx":"conn4","msg":"Found self in config","attr":{"hostAndPort":"localhost:27017"}}
{"t":{"$date":"2024-04-09T02:25:24.813+00:00"},"s":"I",  "c":"REPL",     "id":21358,   "ctx":"conn4","msg":"Replica set state transition","attr":{"newState":"STARTUP2","oldState":"STARTUP"}}
{"t":{"$date":"2024-04-09T02:25:24.815+00:00"},"s":"I",  "c":"REPL",     "id":21306,   "ctx":"conn4","msg":"Starting replication storage threads"}
{"t":{"$date":"2024-04-09T02:25:24.816+00:00"},"s":"I",  "c":"REPL",     "id":4280512, "ctx":"conn4","msg":"No initial sync required. Attempting to begin steady replication"}
{"t":{"$date":"2024-04-09T02:25:24.817+00:00"},"s":"I",  "c":"REPL",     "id":21358,   "ctx":"conn4","msg":"Replica set state transition","attr":{"newState":"RECOVERING","oldState":"STARTUP2"}}
{"t":{"$date":"2024-04-09T02:25:24.818+00:00"},"s":"I",  "c":"STORAGE",  "id":20320,   "ctx":"conn4","msg":"createCollection","attr":{"namespace":"local.replset.initialSyncId","uuidDisposition":"generated","uuid":{"uuid":{"$uuid":"5d978f15-0413-4f27-b550-ee0815eb3551"}},"options":{}}}
{"t":{"$date":"2024-04-09T02:25:24.842+00:00"},"s":"I",  "c":"INDEX",    "id":20345,   "ctx":"conn4","msg":"Index build: done building","attr":{"buildUUID":null,"namespace":"local.replset.initialSyncId","index":"_id_","commitTimestamp":null}}
{"t":{"$date":"2024-04-09T02:25:24.843+00:00"},"s":"I",  "c":"REPL",     "id":21299,   "ctx":"conn4","msg":"Starting replication fetcher thread"}
{"t":{"$date":"2024-04-09T02:25:24.844+00:00"},"s":"I",  "c":"REPL",     "id":21300,   "ctx":"conn4","msg":"Starting replication applier thread"}
{"t":{"$date":"2024-04-09T02:25:24.846+00:00"},"s":"I",  "c":"REPL",     "id":21301,   "ctx":"conn4","msg":"Starting replication reporter thread"}
{"t":{"$date":"2024-04-09T02:25:24.846+00:00"},"s":"I",  "c":"REPL",     "id":21224,   "ctx":"OplogApplier-0","msg":"Starting oplog application"}
{"t":{"$date":"2024-04-09T02:25:24.847+00:00"},"s":"I",  "c":"COMMAND",  "id":51803,   "ctx":"conn4","msg":"Slow query","attr":{"type":"command","ns":"local.system.replset","appName":"mongosh 2.2.2","command":{"replSetInitiate":{},"lsid":{"id":{"$uuid":"ced49dc2-87d5-49b9-b8d3-2cdf596bd0ad"}},"$readPreference":{"mode":"primaryPreferred"},"$db":"admin"},"totalOplogSlotDurationMicros":484,"numYields":0,"reslen":143,"locks":{"ParallelBatchWriterMode":{"acquireCount":{"r":12}},"FeatureCompatibilityVersion":{"acquireCount":{"r":7,"w":6}},"ReplicationStateTransition":{"acquireCount":{"w":13}},"Global":{"acquireCount":{"r":7,"w":4,"W":2}},"Database":{"acquireCount":{"r":5,"w":4,"R":1}},"Collection":{"acquireCount":{"r":2,"w":3}},"Mutex":{"acquireCount":{"r":9}},"oplog":{"acquireCount":{"w":1}}},"flowControl":{"acquireCount":3,"timeAcquiringMicros":5},"readConcern":{"level":"local","provenance":"implicitDefault"},"storage":{},"remote":"127.0.0.1:35900","protocol":"op_msg","durationMillis":248}}
{"t":{"$date":"2024-04-09T02:25:24.847+00:00"},"s":"I",  "c":"REPL",     "id":21358,   "ctx":"OplogApplier-0","msg":"Replica set state transition","attr":{"newState":"SECONDARY","oldState":"RECOVERING"}}
{"t":{"$date":"2024-04-09T02:25:24.849+00:00"},"s":"I",  "c":"ELECTION", "id":4615652, "ctx":"OplogApplier-0","msg":"Starting an election, since we've seen no PRIMARY in election timeout period","attr":{"electionTimeoutPeriodMillis":10000}}
{"t":{"$date":"2024-04-09T02:25:24.850+00:00"},"s":"I",  "c":"ELECTION", "id":21438,   "ctx":"OplogApplier-0","msg":"Conducting a dry run election to see if we could be elected","attr":{"currentTerm":0}}
{"t":{"$date":"2024-04-09T02:25:24.851+00:00"},"s":"I",  "c":"ELECTION", "id":21444,   "ctx":"ReplCoord-0","msg":"Dry election run succeeded, running for election","attr":{"newTerm":1}}
{"t":{"$date":"2024-04-09T02:25:24.852+00:00"},"s":"I",  "c":"ELECTION", "id":6015300, "ctx":"ReplCoord-0","msg":"Storing last vote document in local storage for my election","attr":{"lastVote":{"term":1,"candidateIndex":0}}}
{"t":{"$date":"2024-04-09T02:25:24.854+00:00"},"s":"I",  "c":"ELECTION", "id":21450,   "ctx":"ReplCoord-0","msg":"Election succeeded, assuming primary role","attr":{"term":1}}
{"t":{"$date":"2024-04-09T02:25:24.855+00:00"},"s":"I",  "c":"REPL",     "id":21358,   "ctx":"ReplCoord-0","msg":"Replica set state transition","attr":{"newState":"PRIMARY","oldState":"SECONDARY"}}
{"t":{"$date":"2024-04-09T02:25:24.856+00:00"},"s":"I",  "c":"REPL",     "id":21106,   "ctx":"ReplCoord-0","msg":"Resetting sync source to empty","attr":{"previousSyncSource":":27017"}}
{"t":{"$date":"2024-04-09T02:25:24.857+00:00"},"s":"I",  "c":"REPL",     "id":21359,   "ctx":"ReplCoord-0","msg":"Entering primary catch-up mode"}
{"t":{"$date":"2024-04-09T02:25:24.857+00:00"},"s":"I",  "c":"REPL",     "id":6015304, "ctx":"ReplCoord-0","msg":"Skipping primary catchup since we are the only node in the replica set."}
{"t":{"$date":"2024-04-09T02:25:24.865+00:00"},"s":"I",  "c":"REPL",     "id":21363,   "ctx":"ReplCoord-0","msg":"Exited primary catch-up mode"}
{"t":{"$date":"2024-04-09T02:25:24.866+00:00"},"s":"I",  "c":"REPL",     "id":21107,   "ctx":"ReplCoord-0","msg":"Stopping replication producer"}
{"t":{"$date":"2024-04-09T02:25:24.867+00:00"},"s":"I",  "c":"REPL",     "id":21239,   "ctx":"ReplBatcher","msg":"Oplog buffer has been drained","attr":{"term":1}}
{"t":{"$date":"2024-04-09T02:25:24.868+00:00"},"s":"I",  "c":"REPL",     "id":21239,   "ctx":"ReplBatcher","msg":"Oplog buffer has been drained","attr":{"term":1}}
{"t":{"$date":"2024-04-09T02:25:24.868+00:00"},"s":"I",  "c":"REPL",     "id":21343,   "ctx":"RstlKillOpThread","msg":"Starting to kill user operations"}
{"t":{"$date":"2024-04-09T02:25:24.869+00:00"},"s":"I",  "c":"REPL",     "id":21344,   "ctx":"RstlKillOpThread","msg":"Stopped killing user operations"}
{"t":{"$date":"2024-04-09T02:25:24.870+00:00"},"s":"I",  "c":"REPL",     "id":21340,   "ctx":"RstlKillOpThread","msg":"State transition ops metrics","attr":{"metrics":{"lastStateTransition":"stepUp","userOpsKilled":0,"userOpsRunning":0}}}
{"t":{"$date":"2024-04-09T02:25:24.871+00:00"},"s":"I",  "c":"REPL",     "id":4508103, "ctx":"OplogApplier-0","msg":"Increment the config term via reconfig"}
{"t":{"$date":"2024-04-09T02:25:24.872+00:00"},"s":"I",  "c":"REPL",     "id":6015313, "ctx":"OplogApplier-0","msg":"Replication config state is Steady, starting reconfig"}
{"t":{"$date":"2024-04-09T02:25:24.873+00:00"},"s":"I",  "c":"REPL",     "id":6015317, "ctx":"OplogApplier-0","msg":"Setting new configuration state","attr":{"newState":"ConfigReconfiguring","oldState":"ConfigSteady"}}
{"t":{"$date":"2024-04-09T02:25:24.874+00:00"},"s":"I",  "c":"REPL",     "id":21353,   "ctx":"OplogApplier-0","msg":"replSetReconfig config object parses ok","attr":{"numMembers":1}}
{"t":{"$date":"2024-04-09T02:25:24.875+00:00"},"s":"I",  "c":"REPL",     "id":51814,   "ctx":"OplogApplier-0","msg":"Persisting new config to disk"}
{"t":{"$date":"2024-04-09T02:25:24.877+00:00"},"s":"I",  "c":"REPL",     "id":6015315, "ctx":"OplogApplier-0","msg":"Persisted new config to disk"}
{"t":{"$date":"2024-04-09T02:25:24.878+00:00"},"s":"I",  "c":"REPL",     "id":6015317, "ctx":"OplogApplier-0","msg":"Setting new configuration state","attr":{"newState":"ConfigSteady","oldState":"ConfigReconfiguring"}}
{"t":{"$date":"2024-04-09T02:25:24.878+00:00"},"s":"I",  "c":"REPL",     "id":21392,   "ctx":"OplogApplier-0","msg":"New replica set config in use","attr":{"config":{"_id":"mr1","version":1,"term":1,"members":[{"_id":0,"host":"localhost:27017","arbiterOnly":false,"buildIndexes":true,"hidden":false,"priority":1,"tags":{},"secondaryDelaySecs":0,"votes":1}],"protocolVersion":1,"writeConcernMajorityJournalDefault":true,"settings":{"chainingAllowed":true,"heartbeatIntervalMillis":2000,"heartbeatTimeoutSecs":10,"electionTimeoutMillis":10000,"catchUpTimeoutMillis":-1,"catchUpTakeoverDelayMillis":30000,"getLastErrorModes":{},"getLastErrorDefaults":{"w":1,"wtimeout":0},"replicaSetId":{"$oid":"6614a714bc5de5bfe3a48011"}}}}}
{"t":{"$date":"2024-04-09T02:25:24.880+00:00"},"s":"I",  "c":"REPL",     "id":21393,   "ctx":"OplogApplier-0","msg":"Found self in config","attr":{"hostAndPort":"localhost:27017"}}
{"t":{"$date":"2024-04-09T02:25:24.881+00:00"},"s":"I",  "c":"REPL",     "id":6015310, "ctx":"OplogApplier-0","msg":"Starting to transition to primary."}
{"t":{"$date":"2024-04-09T02:25:24.881+00:00"},"s":"I",  "c":"STORAGE",  "id":20320,   "ctx":"OplogApplier-0","msg":"createCollection","attr":{"namespace":"config.transactions","uuidDisposition":"generated","uuid":{"uuid":{"$uuid":"d5d4b432-34e3-4620-a840-366fade5065f"}},"options":{}}}
{"t":{"$date":"2024-04-09T02:25:24.884+00:00"},"s":"I",  "c":"NETWORK",  "id":22944,   "ctx":"conn2","msg":"Connection ended","attr":{"remote":"127.0.0.1:35886","uuid":"e247a00d-f6c0-4074-86bb-7069fec34ac6","connectionId":2,"connectionCount":3}}
{"t":{"$date":"2024-04-09T02:25:24.884+00:00"},"s":"I",  "c":"NETWORK",  "id":22944,   "ctx":"conn4","msg":"Connection ended","attr":{"remote":"127.0.0.1:35900","uuid":"fd360657-880c-44c1-9a78-8af11bdcbda7","connectionId":4,"connectionCount":2}}
{"t":{"$date":"2024-04-09T02:25:24.885+00:00"},"s":"I",  "c":"NETWORK",  "id":22944,   "ctx":"conn1","msg":"Connection ended","attr":{"remote":"127.0.0.1:35876","uuid":"31a2a8eb-9690-4df0-bd4f-4ce4ed585ddb","connectionId":1,"connectionCount":1}}
{"t":{"$date":"2024-04-09T02:25:24.885+00:00"},"s":"I",  "c":"NETWORK",  "id":22944,   "ctx":"conn3","msg":"Connection ended","attr":{"remote":"127.0.0.1:35896","uuid":"03da86e4-9c9f-4f9c-a391-1638b4139837","connectionId":3,"connectionCount":0}}
{"t":{"$date":"2024-04-09T02:25:24.908+00:00"},"s":"I",  "c":"INDEX",    "id":20345,   "ctx":"OplogApplier-0","msg":"Index build: done building","attr":{"buildUUID":null,"namespace":"config.transactions","index":"_id_","commitTimestamp":{"$timestamp":{"t":1712629524,"i":3}}}}
{"t":{"$date":"2024-04-09T02:25:24.909+00:00"},"s":"I",  "c":"STORAGE",  "id":20320,   "ctx":"OplogApplier-0","msg":"createCollection","attr":{"namespace":"config.image_collection","uuidDisposition":"generated","uuid":{"uuid":{"$uuid":"ac10922c-bf37-4ecf-b5de-d6d2212c71cc"}},"options":{}}}
{"t":{"$date":"2024-04-09T02:25:24.920+00:00"},"s":"I",  "c":"CONTROL",  "id":23377,   "ctx":"SignalHandler","msg":"Received signal","attr":{"signal":15,"error":"Terminated"}}
{"t":{"$date":"2024-04-09T02:25:24.921+00:00"},"s":"I",  "c":"CONTROL",  "id":23378,   "ctx":"SignalHandler","msg":"Signal was sent by kill(2)","attr":{"pid":267,"uid":0}}
{"t":{"$date":"2024-04-09T02:25:24.921+00:00"},"s":"I",  "c":"CONTROL",  "id":23381,   "ctx":"SignalHandler","msg":"will terminate after current cmd ends"}
{"t":{"$date":"2024-04-09T02:25:24.922+00:00"},"s":"I",  "c":"REPL",     "id":4784900, "ctx":"SignalHandler","msg":"Stepping down the ReplicationCoordinator for shutdown","attr":{"waitTimeMillis":15000}}
{"t":{"$date":"2024-04-09T02:25:24.922+00:00"},"s":"I",  "c":"REPL",     "id":4794602, "ctx":"SignalHandler","msg":"Attempting to enter quiesce mode"}
{"t":{"$date":"2024-04-09T02:25:24.923+00:00"},"s":"I",  "c":"COMMAND",  "id":4784901, "ctx":"SignalHandler","msg":"Shutting down the MirrorMaestro"}
{"t":{"$date":"2024-04-09T02:25:24.924+00:00"},"s":"I",  "c":"REPL",     "id":40441,   "ctx":"SignalHandler","msg":"Stopping TopologyVersionObserver"}
{"t":{"$date":"2024-04-09T02:25:24.924+00:00"},"s":"I",  "c":"REPL",     "id":40447,   "ctx":"TopologyVersionObserver","msg":"Stopped TopologyVersionObserver"}
{"t":{"$date":"2024-04-09T02:25:24.925+00:00"},"s":"I",  "c":"SHARDING", "id":4784902, "ctx":"SignalHandler","msg":"Shutting down the WaitForMajorityService"}
{"t":{"$date":"2024-04-09T02:25:24.926+00:00"},"s":"I",  "c":"CONTROL",  "id":4784903, "ctx":"SignalHandler","msg":"Shutting down the LogicalSessionCache"}
{"t":{"$date":"2024-04-09T02:25:24.927+00:00"},"s":"I",  "c":"NETWORK",  "id":20562,   "ctx":"SignalHandler","msg":"Shutdown: going to close listening sockets"}
{"t":{"$date":"2024-04-09T02:25:24.928+00:00"},"s":"I",  "c":"NETWORK",  "id":23017,   "ctx":"listener","msg":"removing socket file","attr":{"path":"/tmp/mongodb-27017.sock"}}
{"t":{"$date":"2024-04-09T02:25:24.929+00:00"},"s":"I",  "c":"NETWORK",  "id":4784905, "ctx":"SignalHandler","msg":"Shutting down the global connection pool"}
{"t":{"$date":"2024-04-09T02:25:24.929+00:00"},"s":"I",  "c":"CONTROL",  "id":4784906, "ctx":"SignalHandler","msg":"Shutting down the FlowControlTicketholder"}
{"t":{"$date":"2024-04-09T02:25:24.930+00:00"},"s":"I",  "c":"-",        "id":20520,   "ctx":"SignalHandler","msg":"Stopping further Flow Control ticket acquisitions."}
{"t":{"$date":"2024-04-09T02:25:24.930+00:00"},"s":"I",  "c":"REPL",     "id":4784907, "ctx":"SignalHandler","msg":"Shutting down the replica set node executor"}
{"t":{"$date":"2024-04-09T02:25:24.931+00:00"},"s":"I",  "c":"INDEX",    "id":20345,   "ctx":"OplogApplier-0","msg":"Index build: done building","attr":{"buildUUID":null,"namespace":"config.image_collection","index":"_id_","commitTimestamp":{"$timestamp":{"t":1712629524,"i":4}}}}
{"t":{"$date":"2024-04-09T02:25:24.932+00:00"},"s":"I",  "c":"ASIO",     "id":22582,   "ctx":"ReplNodeDbWorkerNetwork","msg":"Killing all outstanding egress activity."}
{"t":{"$date":"2024-04-09T02:25:24.932+00:00"},"s":"I",  "c":"CONTROL",  "id":4784908, "ctx":"SignalHandler","msg":"Shutting down the PeriodicThreadToAbortExpiredTransactions"}
{"t":{"$date":"2024-04-09T02:25:24.933+00:00"},"s":"I",  "c":"REPL",     "id":6015309, "ctx":"OplogApplier-0","msg":"Logging transition to primary to oplog on stepup"}
{"t":{"$date":"2024-04-09T02:25:24.933+00:00"},"s":"I",  "c":"REPL",     "id":4784909, "ctx":"SignalHandler","msg":"Shutting down the ReplicationCoordinator"}
{"t":{"$date":"2024-04-09T02:25:24.934+00:00"},"s":"I",  "c":"REPL",     "id":5074000, "ctx":"SignalHandler","msg":"Shutting down the replica set aware services."}
{"t":{"$date":"2024-04-09T02:25:24.934+00:00"},"s":"I",  "c":"REPL",     "id":5123006, "ctx":"SignalHandler","msg":"Shutting down PrimaryOnlyService","attr":{"service":"TenantMigrationDonorService","numInstances":0,"numOperationContexts":0}}
{"t":{"$date":"2024-04-09T02:25:24.935+00:00"},"s":"I",  "c":"STORAGE",  "id":20657,   "ctx":"OplogApplier-0","msg":"IndexBuildsCoordinator::onStepUp - this node is stepping up to primary"}
{"t":{"$date":"2024-04-09T02:25:24.935+00:00"},"s":"I",  "c":"ASIO",     "id":22582,   "ctx":"TenantMigrationDonorServiceNetwork","msg":"Killing all outstanding egress activity."}
{"t":{"$date":"2024-04-09T02:25:24.935+00:00"},"s":"I",  "c":"STORAGE",  "id":20320,   "ctx":"OplogApplier-0","msg":"createCollection","attr":{"namespace":"config.system.indexBuilds","uuidDisposition":"generated","uuid":{"uuid":{"$uuid":"cc653e39-607a-4cb5-a66a-89451efe1653"}},"options":{}}}
{"t":{"$date":"2024-04-09T02:25:24.936+00:00"},"s":"I",  "c":"REPL",     "id":5123006, "ctx":"SignalHandler","msg":"Shutting down PrimaryOnlyService","attr":{"service":"TenantMigrationRecipientService","numInstances":0,"numOperationContexts":0}}
{"t":{"$date":"2024-04-09T02:25:24.938+00:00"},"s":"I",  "c":"ASIO",     "id":22582,   "ctx":"TenantMigrationRecipientServiceNetwork","msg":"Killing all outstanding egress activity."}
{"t":{"$date":"2024-04-09T02:25:24.938+00:00"},"s":"I",  "c":"REPL",     "id":21328,   "ctx":"SignalHandler","msg":"Shutting down replication subsystems"}
{"t":{"$date":"2024-04-09T02:25:24.939+00:00"},"s":"I",  "c":"REPL",     "id":21302,   "ctx":"SignalHandler","msg":"Stopping replication reporter thread"}
{"t":{"$date":"2024-04-09T02:25:24.940+00:00"},"s":"I",  "c":"REPL",     "id":21303,   "ctx":"SignalHandler","msg":"Stopping replication fetcher thread"}
{"t":{"$date":"2024-04-09T02:25:24.940+00:00"},"s":"I",  "c":"REPL",     "id":21304,   "ctx":"SignalHandler","msg":"Stopping replication applier thread"}
{"t":{"$date":"2024-04-09T02:25:24.956+00:00"},"s":"I",  "c":"INDEX",    "id":20345,   "ctx":"OplogApplier-0","msg":"Index build: done building","attr":{"buildUUID":null,"namespace":"config.system.indexBuilds","index":"_id_","commitTimestamp":{"$timestamp":{"t":1712629524,"i":6}}}}
{"t":{"$date":"2024-04-09T02:25:24.956+00:00"},"s":"I",  "c":"REPL",     "id":21331,   "ctx":"OplogApplier-0","msg":"Transition to primary complete; database writes are now permitted"}
{"t":{"$date":"2024-04-09T02:25:24.957+00:00"},"s":"I",  "c":"REPL",     "id":6015306, "ctx":"OplogApplier-0","msg":"Applier already left draining state, exiting."}
{"t":{"$date":"2024-04-09T02:25:24.957+00:00"},"s":"I",  "c":"STORAGE",  "id":20320,   "ctx":"monitoring-keys-for-HMAC","msg":"createCollection","attr":{"namespace":"admin.system.keys","uuidDisposition":"generated","uuid":{"uuid":{"$uuid":"b7c4b754-b74c-4996-a62c-909db5b4ee6e"}},"options":{}}}
{"t":{"$date":"2024-04-09T02:25:24.958+00:00"},"s":"I",  "c":"REPL",     "id":21225,   "ctx":"OplogApplier-0","msg":"Finished oplog application"}
{"t":{"$date":"2024-04-09T02:25:24.975+00:00"},"s":"I",  "c":"INDEX",    "id":20345,   "ctx":"monitoring-keys-for-HMAC","msg":"Index build: done building","attr":{"buildUUID":null,"namespace":"admin.system.keys","index":"_id_","commitTimestamp":{"$timestamp":{"t":1712629524,"i":7}}}}
{"t":{"$date":"2024-04-09T02:25:24.979+00:00"},"s":"I",  "c":"-",        "id":4939300, "ctx":"monitoring-keys-for-HMAC","msg":"Failed to refresh key cache","attr":{"error":"ShutdownInProgress{ remainingQuiesceTimeMillis: 0 }: Replication is being shut down; Error details: { writeConcern: { w: \"majority\", wtimeout: 60000, provenance: \"clientSupplied\" } }","nextWakeupMillis":2200}}
{"t":{"$date":"2024-04-09T02:25:25.847+00:00"},"s":"I",  "c":"REPL",     "id":21107,   "ctx":"BackgroundSync","msg":"Stopping replication producer"}
{"t":{"$date":"2024-04-09T02:25:25.849+00:00"},"s":"I",  "c":"REPL",     "id":5698300, "ctx":"SignalHandler","msg":"Stopping replication applier writer pool"}
{"t":{"$date":"2024-04-09T02:25:25.850+00:00"},"s":"I",  "c":"REPL",     "id":21307,   "ctx":"SignalHandler","msg":"Stopping replication storage threads"}
{"t":{"$date":"2024-04-09T02:25:25.851+00:00"},"s":"I",  "c":"ASIO",     "id":22582,   "ctx":"OplogApplierNetwork","msg":"Killing all outstanding egress activity."}
{"t":{"$date":"2024-04-09T02:25:25.852+00:00"},"s":"I",  "c":"ASIO",     "id":22582,   "ctx":"SignalHandler","msg":"Killing all outstanding egress activity."}
{"t":{"$date":"2024-04-09T02:25:25.853+00:00"},"s":"I",  "c":"ASIO",     "id":22582,   "ctx":"ReplCoordExternNetwork","msg":"Killing all outstanding egress activity."}
{"t":{"$date":"2024-04-09T02:25:25.855+00:00"},"s":"I",  "c":"ASIO",     "id":22582,   "ctx":"ReplNetwork","msg":"Killing all outstanding egress activity."}
{"t":{"$date":"2024-04-09T02:25:25.856+00:00"},"s":"I",  "c":"SHARDING", "id":4784910, "ctx":"SignalHandler","msg":"Shutting down the ShardingInitializationMongoD"}
{"t":{"$date":"2024-04-09T02:25:25.856+00:00"},"s":"I",  "c":"REPL",     "id":4784911, "ctx":"SignalHandler","msg":"Enqueuing the ReplicationStateTransitionLock for shutdown"}
{"t":{"$date":"2024-04-09T02:25:25.857+00:00"},"s":"I",  "c":"-",        "id":4784912, "ctx":"SignalHandler","msg":"Killing all operations for shutdown"}
{"t":{"$date":"2024-04-09T02:25:25.858+00:00"},"s":"I",  "c":"-",        "id":4695300, "ctx":"SignalHandler","msg":"Interrupted all currently running operations","attr":{"opsKilled":4}}
{"t":{"$date":"2024-04-09T02:25:25.858+00:00"},"s":"I",  "c":"TENANT_M", "id":5093807, "ctx":"SignalHandler","msg":"Shutting down all TenantMigrationAccessBlockers on global shutdown"}
{"t":{"$date":"2024-04-09T02:25:25.859+00:00"},"s":"I",  "c":"COMMAND",  "id":4784913, "ctx":"SignalHandler","msg":"Shutting down all open transactions"}
{"t":{"$date":"2024-04-09T02:25:25.859+00:00"},"s":"I",  "c":"REPL",     "id":4784914, "ctx":"SignalHandler","msg":"Acquiring the ReplicationStateTransitionLock for shutdown"}
{"t":{"$date":"2024-04-09T02:25:25.861+00:00"},"s":"I",  "c":"INDEX",    "id":4784915, "ctx":"SignalHandler","msg":"Shutting down the IndexBuildsCoordinator"}
{"t":{"$date":"2024-04-09T02:25:25.861+00:00"},"s":"I",  "c":"REPL",     "id":4784916, "ctx":"SignalHandler","msg":"Reacquiring the ReplicationStateTransitionLock for shutdown"}
{"t":{"$date":"2024-04-09T02:25:25.862+00:00"},"s":"I",  "c":"REPL",     "id":4784917, "ctx":"SignalHandler","msg":"Attempting to mark clean shutdown"}
{"t":{"$date":"2024-04-09T02:25:25.863+00:00"},"s":"I",  "c":"NETWORK",  "id":4784918, "ctx":"SignalHandler","msg":"Shutting down the ReplicaSetMonitor"}
{"t":{"$date":"2024-04-09T02:25:25.863+00:00"},"s":"I",  "c":"REPL",     "id":4784920, "ctx":"SignalHandler","msg":"Shutting down the LogicalTimeValidator"}
{"t":{"$date":"2024-04-09T02:25:25.864+00:00"},"s":"I",  "c":"SHARDING", "id":4784921, "ctx":"SignalHandler","msg":"Shutting down the MigrationUtilExecutor"}
{"t":{"$date":"2024-04-09T02:25:25.864+00:00"},"s":"I",  "c":"ASIO",     "id":22582,   "ctx":"MigrationUtil-TaskExecutor","msg":"Killing all outstanding egress activity."}
{"t":{"$date":"2024-04-09T02:25:25.865+00:00"},"s":"I",  "c":"COMMAND",  "id":4784923, "ctx":"SignalHandler","msg":"Shutting down the ServiceEntryPoint"}
{"t":{"$date":"2024-04-09T02:25:25.865+00:00"},"s":"I",  "c":"CONTROL",  "id":4784927, "ctx":"SignalHandler","msg":"Shutting down the HealthLog"}
{"t":{"$date":"2024-04-09T02:25:25.866+00:00"},"s":"I",  "c":"CONTROL",  "id":4784928, "ctx":"SignalHandler","msg":"Shutting down the TTL monitor"}
{"t":{"$date":"2024-04-09T02:25:25.866+00:00"},"s":"I",  "c":"INDEX",    "id":3684100, "ctx":"SignalHandler","msg":"Shutting down TTL collection monitor thread"}
{"t":{"$date":"2024-04-09T02:25:25.867+00:00"},"s":"I",  "c":"INDEX",    "id":3684101, "ctx":"SignalHandler","msg":"Finished shutting down TTL collection monitor thread"}
{"t":{"$date":"2024-04-09T02:25:25.867+00:00"},"s":"I",  "c":"CONTROL",  "id":4784929, "ctx":"SignalHandler","msg":"Acquiring the global lock for shutdown"}
{"t":{"$date":"2024-04-09T02:25:25.868+00:00"},"s":"I",  "c":"CONTROL",  "id":4784930, "ctx":"SignalHandler","msg":"Shutting down the storage engine"}
{"t":{"$date":"2024-04-09T02:25:25.868+00:00"},"s":"I",  "c":"STORAGE",  "id":22320,   "ctx":"SignalHandler","msg":"Shutting down journal flusher thread"}
{"t":{"$date":"2024-04-09T02:25:25.869+00:00"},"s":"I",  "c":"STORAGE",  "id":22321,   "ctx":"SignalHandler","msg":"Finished shutting down journal flusher thread"}
{"t":{"$date":"2024-04-09T02:25:25.869+00:00"},"s":"I",  "c":"STORAGE",  "id":22322,   "ctx":"SignalHandler","msg":"Shutting down checkpoint thread"}
{"t":{"$date":"2024-04-09T02:25:25.870+00:00"},"s":"I",  "c":"STORAGE",  "id":22323,   "ctx":"SignalHandler","msg":"Finished shutting down checkpoint thread"}
{"t":{"$date":"2024-04-09T02:25:25.870+00:00"},"s":"I",  "c":"STORAGE",  "id":22261,   "ctx":"SignalHandler","msg":"Timestamp monitor shutting down"}
{"t":{"$date":"2024-04-09T02:25:25.871+00:00"},"s":"I",  "c":"STORAGE",  "id":20282,   "ctx":"SignalHandler","msg":"Deregistering all the collections"}
{"t":{"$date":"2024-04-09T02:25:25.872+00:00"},"s":"I",  "c":"STORAGE",  "id":22372,   "ctx":"OplogVisibilityThread","msg":"Oplog visibility thread shutting down."}
{"t":{"$date":"2024-04-09T02:25:25.872+00:00"},"s":"I",  "c":"STORAGE",  "id":22317,   "ctx":"SignalHandler","msg":"WiredTigerKVEngine shutting down"}
{"t":{"$date":"2024-04-09T02:25:25.873+00:00"},"s":"I",  "c":"STORAGE",  "id":22318,   "ctx":"SignalHandler","msg":"Shutting down session sweeper thread"}
{"t":{"$date":"2024-04-09T02:25:25.873+00:00"},"s":"I",  "c":"STORAGE",  "id":22319,   "ctx":"SignalHandler","msg":"Finished shutting down session sweeper thread"}
{"t":{"$date":"2024-04-09T02:25:25.874+00:00"},"s":"I",  "c":"STORAGE",  "id":4795902, "ctx":"SignalHandler","msg":"Closing WiredTiger","attr":{"closeConfig":"leak_memory=true,"}}
{"t":{"$date":"2024-04-09T02:25:25.875+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"SignalHandler","msg":"WiredTiger message","attr":{"message":"[1712629525:875772][174:0x7fbfbfc11700], WT_CONNECTION.close: [WT_VERB_RECOVERY_PROGRESS] shutdown rollback to stable has successfully finished and ran for 0 milliseconds"}}
{"t":{"$date":"2024-04-09T02:25:25.904+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"SignalHandler","msg":"WiredTiger message","attr":{"message":"[1712629525:904759][174:0x7fbfbfc11700], close_ckpt: [WT_VERB_CHECKPOINT_PROGRESS] saving checkpoint snapshot min: 115, snapshot max: 115 snapshot count: 0, oldest timestamp: (1712629524, 1) , meta checkpoint timestamp: (1712629524, 8) base write gen: 7"}}
{"t":{"$date":"2024-04-09T02:25:25.921+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"SignalHandler","msg":"WiredTiger message","attr":{"message":"[1712629525:921407][174:0x7fbfbfc11700], WT_CONNECTION.close: [WT_VERB_RECOVERY_PROGRESS] shutdown checkpoint has successfully finished and ran for 44 milliseconds"}}
{"t":{"$date":"2024-04-09T02:25:25.922+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"SignalHandler","msg":"WiredTiger message","attr":{"message":"[1712629525:922094][174:0x7fbfbfc11700], WT_CONNECTION.close: [WT_VERB_RECOVERY_PROGRESS] shutdown was completed successfully and took 47ms, including 0ms for the rollback to stable, and 44ms for the checkpoint."}}
{"t":{"$date":"2024-04-09T02:25:26.005+00:00"},"s":"I",  "c":"STORAGE",  "id":4795901, "ctx":"SignalHandler","msg":"WiredTiger closed","attr":{"durationMillis":131}}
{"t":{"$date":"2024-04-09T02:25:26.007+00:00"},"s":"I",  "c":"STORAGE",  "id":22279,   "ctx":"SignalHandler","msg":"shutdown: removing fs lock..."}
{"t":{"$date":"2024-04-09T02:25:26.009+00:00"},"s":"I",  "c":"-",        "id":4784931, "ctx":"SignalHandler","msg":"Dropping the scope cache for shutdown"}
{"t":{"$date":"2024-04-09T02:25:26.010+00:00"},"s":"I",  "c":"FTDC",     "id":4784926, "ctx":"SignalHandler","msg":"Shutting down full-time data capture"}
{"t":{"$date":"2024-04-09T02:25:26.011+00:00"},"s":"I",  "c":"FTDC",     "id":20626,   "ctx":"SignalHandler","msg":"Shutting down full-time diagnostic data capture"}
{"t":{"$date":"2024-04-09T02:25:27.006+00:00"},"s":"I",  "c":"CONTROL",  "id":20565,   "ctx":"SignalHandler","msg":"Now exiting"}
{"t":{"$date":"2024-04-09T02:25:27.009+00:00"},"s":"I",  "c":"CONTROL",  "id":23138,   "ctx":"SignalHandler","msg":"Shutting down","attr":{"exitCode":0}}
