Load environment configuration
Waiting for RTS to start ...
{"success":true}RTS started.
SLF4J: Class path contains multiple SLF4J providers.
SLF4J: Found provider [ch.qos.logback.classic.spi.LogbackServiceProvider@6659c656]
SLF4J: Found provider [org.slf4j.reload4j.Reload4jServiceProvider@6d5380c2]
SLF4J: See https://www.slf4j.org/codes.html#multiple_bindings for an explanation.
SLF4J: Actual provider is of type [ch.qos.logback.classic.spi.LogbackServiceProvider@6659c656]
[2024-04-09 02:26:04,164]  - Starting ServerApplication vv1.19 using Java 17.0.9 with PID 1782 (/opt/appsmith/backend/server.jar started by root in /opt/appsmith/backend)
[2024-04-09 02:26:04,167]  - Running with Spring Boot v3.0.9, Spring v6.0.11
[2024-04-09 02:26:04,169]  - The following 1 profile is active: "production"
[2024-04-09 02:26:06,232]  - Multiple Spring Data modules found, entering strict repository configuration mode
[2024-04-09 02:26:06,236]  - Bootstrapping Spring Data Reactive MongoDB repositories in DEFAULT mode.
[2024-04-09 02:26:06,729]  - Finished Spring Data repository scanning in 478 ms. Found 44 Reactive MongoDB repository interfaces.
[2024-04-09 02:26:09,640]  - Application started with build version v1.19, and commitSha 867c52974d61ffafd16f6eb06d2a9abc3440df4a
[2024-04-09 02:26:10,529]  - MongoClient with metadata {"driver": {"name": "mongo-java-driver|reactive-streams|spring-boot", "version": "4.8.2"}, "os": {"type": "Linux", "name": "Linux", "architecture": "amd64", "version": "5.15.49-linuxkit"}, "platform": "Java/Eclipse Adoptium/17.0.9+9"} created with settings MongoClientSettings{readPreference=primary, writeConcern=WriteConcern{w=null, wTimeout=null ms, journal=null}, retryWrites=true, retryReads=true, readConcern=ReadConcern{level=null}, credential=MongoCredential{mechanism=null, userName='appsmith', source='appsmith', password=<hidden>, mechanismProperties=<hidden>}, streamFactoryFactory=NettyStreamFactoryFactory{eventLoopGroup=io.netty.channel.nio.NioEventLoopGroup@1a28aef1, socketChannelClass=class io.netty.channel.socket.nio.NioSocketChannel, allocator=PooledByteBufAllocator(directByDefault: true), sslContext=null}, commandListeners=[io.micrometer.core.instrument.binder.mongodb.MongoMetricsCommandListener@522ba524], codecRegistry=ProvidersCodecRegistry{codecProviders=[ValueCodecProvider{}, BsonValueCodecProvider{}, DBRefCodecProvider{}, DBObjectCodecProvider{}, DocumentCodecProvider{}, CollectionCodecProvider{}, IterableCodecProvider{}, MapCodecProvider{}, GeoJsonCodecProvider{}, GridFSFileCodecProvider{}, Jsr310CodecProvider{}, JsonObjectCodecProvider{}, BsonCodecProvider{}, EnumCodecProvider{}, com.mongodb.Jep395RecordCodecProvider@4647881c]}, clusterSettings={hosts=[localhost:27017], srvServiceName=mongodb, mode=SINGLE, requiredClusterType=UNKNOWN, requiredReplicaSetName='null', serverSelector='null', clusterListeners='[]', serverSelectionTimeout='30000 ms', localThreshold='30000 ms'}, socketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=0, receiveBufferSize=0, sendBufferSize=0}, heartbeatSocketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=10000, receiveBufferSize=0, sendBufferSize=0}, connectionPoolSettings=ConnectionPoolSettings{maxSize=100, minSize=0, maxWaitTimeMS=120000, maxConnectionLifeTimeMS=0, maxConnectionIdleTimeMS=0, maintenanceInitialDelayMS=0, maintenanceFrequencyMS=60000, connectionPoolListeners=[io.micrometer.core.instrument.binder.mongodb.MongoMetricsConnectionPoolListener@51dbd6e4], maxConnecting=2}, serverSettings=ServerSettings{heartbeatFrequencyMS=10000, minHeartbeatFrequencyMS=500, serverListeners='[]', serverMonitorListeners='[]'}, sslSettings=SslSettings{enabled=false, invalidHostNameAllowed=false, context=null}, applicationName='null', compressorList=[], uuidRepresentation=JAVA_LEGACY, serverApi=null, autoEncryptionSettings=null, contextProvider=null}
[2024-04-09 02:26:10,723]  - Monitor thread successfully connected to server with description ServerDescription{address=localhost:27017, type=REPLICA_SET_PRIMARY, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=13, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=96790605, setName='mr1', canonicalAddress=localhost:27017, hosts=[localhost:27017], passives=[], arbiters=[], primary='localhost:27017', tagSet=TagSet{[]}, electionId=7fffffff0000000000000002, setVersion=1, topologyVersion=TopologyVersion{processId=6614a734b5659c4e75dca725, counter=6}, lastWriteDate=Tue Apr 09 02:26:03 UTC 2024, lastUpdateTimeNanos=3355225827932}
[2024-04-09 02:26:14,374]  - MongoClient with metadata {"driver": {"name": "mongo-java-driver|sync|spring-boot", "version": "4.8.2"}, "os": {"type": "Linux", "name": "Linux", "architecture": "amd64", "version": "5.15.49-linuxkit"}, "platform": "Java/Eclipse Adoptium/17.0.9+9"} created with settings MongoClientSettings{readPreference=primary, writeConcern=WriteConcern{w=null, wTimeout=null ms, journal=null}, retryWrites=true, retryReads=true, readConcern=ReadConcern{level=null}, credential=MongoCredential{mechanism=null, userName='appsmith', source='appsmith', password=<hidden>, mechanismProperties=<hidden>}, streamFactoryFactory=NettyStreamFactoryFactory{eventLoopGroup=io.netty.channel.nio.NioEventLoopGroup@78422efb, socketChannelClass=class io.netty.channel.socket.nio.NioSocketChannel, allocator=PooledByteBufAllocator(directByDefault: true), sslContext=null}, commandListeners=[io.micrometer.core.instrument.binder.mongodb.MongoMetricsCommandListener@522ba524], codecRegistry=ProvidersCodecRegistry{codecProviders=[ValueCodecProvider{}, BsonValueCodecProvider{}, DBRefCodecProvider{}, DBObjectCodecProvider{}, DocumentCodecProvider{}, CollectionCodecProvider{}, IterableCodecProvider{}, MapCodecProvider{}, GeoJsonCodecProvider{}, GridFSFileCodecProvider{}, Jsr310CodecProvider{}, JsonObjectCodecProvider{}, BsonCodecProvider{}, EnumCodecProvider{}, com.mongodb.Jep395RecordCodecProvider@4647881c]}, clusterSettings={hosts=[localhost:27017], srvServiceName=mongodb, mode=SINGLE, requiredClusterType=UNKNOWN, requiredReplicaSetName='null', serverSelector='null', clusterListeners='[]', serverSelectionTimeout='30000 ms', localThreshold='30000 ms'}, socketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=0, receiveBufferSize=0, sendBufferSize=0}, heartbeatSocketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=10000, receiveBufferSize=0, sendBufferSize=0}, connectionPoolSettings=ConnectionPoolSettings{maxSize=100, minSize=0, maxWaitTimeMS=120000, maxConnectionLifeTimeMS=0, maxConnectionIdleTimeMS=0, maintenanceInitialDelayMS=0, maintenanceFrequencyMS=60000, connectionPoolListeners=[io.micrometer.core.instrument.binder.mongodb.MongoMetricsConnectionPoolListener@51dbd6e4], maxConnecting=2}, serverSettings=ServerSettings{heartbeatFrequencyMS=10000, minHeartbeatFrequencyMS=500, serverListeners='[]', serverMonitorListeners='[]'}, sslSettings=SslSettings{enabled=false, invalidHostNameAllowed=false, context=null}, applicationName='null', compressorList=[], uuidRepresentation=JAVA_LEGACY, serverApi=null, autoEncryptionSettings=null, contextProvider=null}
[2024-04-09 02:26:14,411]  - Monitor thread successfully connected to server with description ServerDescription{address=localhost:27017, type=REPLICA_SET_PRIMARY, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=13, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=38913437, setName='mr1', canonicalAddress=localhost:27017, hosts=[localhost:27017], passives=[], arbiters=[], primary='localhost:27017', tagSet=TagSet{[]}, electionId=7fffffff0000000000000002, setVersion=1, topologyVersion=TopologyVersion{processId=6614a734b5659c4e75dca725, counter=6}, lastWriteDate=Tue Apr 09 02:26:03 UTC 2024, lastUpdateTimeNanos=3358926983981}
[2024-04-09 02:26:15,360]  - Enabled plugins: []
[2024-04-09 02:26:15,361]  - Disabled plugins: []
[2024-04-09 02:26:15,374]  - PF4J version 3.10.0 in 'deployment' mode
[2024-04-09 02:26:16,081]  - Plugin 'appsmithai-plugin@1.0-SNAPSHOT' resolved
[2024-04-09 02:26:16,082]  - Plugin 'saas-plugin@1.0-SNAPSHOT' resolved
[2024-04-09 02:26:16,082]  - Plugin 'google-sheets-plugin@1.0-SNAPSHOT' resolved
[2024-04-09 02:26:16,082]  - Plugin 'js-plugin@1.0-SNAPSHOT' resolved
[2024-04-09 02:26:16,082]  - Plugin 'oracle-plugin@1.0-SNAPSHOT' resolved
[2024-04-09 02:26:16,082]  - Plugin 'smtp-plugin@1.0-SNAPSHOT' resolved
[2024-04-09 02:26:16,082]  - Plugin 'amazons3-plugin@1.0-SNAPSHOT' resolved
[2024-04-09 02:26:16,082]  - Plugin 'mysql-plugin@1.0-SNAPSHOT' resolved
[2024-04-09 02:26:16,082]  - Plugin 'restapi-plugin@1.0-SNAPSHOT' resolved
[2024-04-09 02:26:16,082]  - Plugin 'postgres-plugin@1.0-SNAPSHOT' resolved
[2024-04-09 02:26:16,082]  - Plugin 'googleai-plugin@1.0-SNAPSHOT' resolved
[2024-04-09 02:26:16,083]  - Plugin 'snowflake-plugin@1.0-SNAPSHOT' resolved
[2024-04-09 02:26:16,083]  - Plugin 'anthropic-plugin@1.0-SNAPSHOT' resolved
[2024-04-09 02:26:16,083]  - Plugin 'elasticsearch-plugin@1.0-SNAPSHOT' resolved
[2024-04-09 02:26:16,084]  - Plugin 'mssql-plugin@1.0-SNAPSHOT' resolved
[2024-04-09 02:26:16,084]  - Plugin 'redshift-plugin@1.0-SNAPSHOT' resolved
[2024-04-09 02:26:16,084]  - Plugin 'databricks-plugin@1.0-SNAPSHOT' resolved
[2024-04-09 02:26:16,085]  - Plugin 'mongo-plugin@1.0-SNAPSHOT' resolved
[2024-04-09 02:26:16,086]  - Plugin 'graphql-plugin@1.0-SNAPSHOT' resolved
[2024-04-09 02:26:16,086]  - Plugin 'aws-lambda-plugin@1.0-SNAPSHOT' resolved
[2024-04-09 02:26:16,086]  - Plugin 'openai-plugin@1.0-SNAPSHOT' resolved
[2024-04-09 02:26:16,086]  - Plugin 'dynamo-plugin@1.0-SNAPSHOT' resolved
[2024-04-09 02:26:16,087]  - Plugin 'arangodb-plugin@1.0-SNAPSHOT' resolved
[2024-04-09 02:26:16,087]  - Plugin 'redis-plugin@1.0-SNAPSHOT' resolved
[2024-04-09 02:26:16,087]  - Plugin 'firestore-plugin@1.0-SNAPSHOT' resolved
[2024-04-09 02:26:16,087]  - Start plugin 'appsmithai-plugin@1.0-SNAPSHOT'
SLF4J: A SLF4J service provider failed to instantiate:
org.slf4j.spi.SLF4JServiceProvider: ch.qos.logback.classic.spi.LogbackServiceProvider not a subtype
SLF4J: A SLF4J service provider failed to instantiate:
org.slf4j.spi.SLF4JServiceProvider: org.slf4j.reload4j.Reload4jServiceProvider not a subtype
SLF4J: No SLF4J providers were found.
SLF4J: Defaulting to no-operation (NOP) logger implementation
SLF4J: See https://www.slf4j.org/codes.html#noProviders for further details.
[2024-04-09 02:26:16,115]  - Start plugin 'saas-plugin@1.0-SNAPSHOT'
[2024-04-09 02:26:16,120]  - Start plugin 'google-sheets-plugin@1.0-SNAPSHOT'
SLF4J: A SLF4J service provider failed to instantiate:
org.slf4j.spi.SLF4JServiceProvider: ch.qos.logback.classic.spi.LogbackServiceProvider not a subtype
SLF4J: A SLF4J service provider failed to instantiate:
org.slf4j.spi.SLF4JServiceProvider: org.slf4j.reload4j.Reload4jServiceProvider not a subtype
SLF4J: No SLF4J providers were found.
SLF4J: Defaulting to no-operation (NOP) logger implementation
SLF4J: See https://www.slf4j.org/codes.html#noProviders for further details.
[2024-04-09 02:26:16,223]  - Start plugin 'js-plugin@1.0-SNAPSHOT'
[2024-04-09 02:26:16,228]  - Start plugin 'oracle-plugin@1.0-SNAPSHOT'
SLF4J: A SLF4J service provider failed to instantiate:
org.slf4j.spi.SLF4JServiceProvider: ch.qos.logback.classic.spi.LogbackServiceProvider not a subtype
SLF4J: A SLF4J service provider failed to instantiate:
org.slf4j.spi.SLF4JServiceProvider: org.slf4j.reload4j.Reload4jServiceProvider not a subtype
SLF4J: No SLF4J providers were found.
SLF4J: Defaulting to no-operation (NOP) logger implementation
SLF4J: See https://www.slf4j.org/codes.html#noProviders for further details.
[2024-04-09 02:26:16,250]  - Start plugin 'smtp-plugin@1.0-SNAPSHOT'
SLF4J: A SLF4J service provider failed to instantiate:
org.slf4j.spi.SLF4JServiceProvider: ch.qos.logback.classic.spi.LogbackServiceProvider not a subtype
SLF4J: A SLF4J service provider failed to instantiate:
org.slf4j.spi.SLF4JServiceProvider: org.slf4j.reload4j.Reload4jServiceProvider not a subtype
SLF4J: No SLF4J providers were found.
SLF4J: Defaulting to no-operation (NOP) logger implementation
SLF4J: See https://www.slf4j.org/codes.html#noProviders for further details.
[2024-04-09 02:26:16,282]  - Start plugin 'amazons3-plugin@1.0-SNAPSHOT'
SLF4J: A SLF4J service provider failed to instantiate:
org.slf4j.spi.SLF4JServiceProvider: ch.qos.logback.classic.spi.LogbackServiceProvider not a subtype
SLF4J: A SLF4J service provider failed to instantiate:
org.slf4j.spi.SLF4JServiceProvider: org.slf4j.reload4j.Reload4jServiceProvider not a subtype
SLF4J: No SLF4J providers were found.
SLF4J: Defaulting to no-operation (NOP) logger implementation
SLF4J: See https://www.slf4j.org/codes.html#noProviders for further details.
[2024-04-09 02:26:16,303]  - Start plugin 'mysql-plugin@1.0-SNAPSHOT'
SLF4J: A SLF4J service provider failed to instantiate:
org.slf4j.spi.SLF4JServiceProvider: ch.qos.logback.classic.spi.LogbackServiceProvider not a subtype
SLF4J: A SLF4J service provider failed to instantiate:
org.slf4j.spi.SLF4JServiceProvider: org.slf4j.reload4j.Reload4jServiceProvider not a subtype
SLF4J: No SLF4J providers were found.
SLF4J: Defaulting to no-operation (NOP) logger implementation
SLF4J: See https://www.slf4j.org/codes.html#noProviders for further details.
[2024-04-09 02:26:16,325]  - Start plugin 'restapi-plugin@1.0-SNAPSHOT'
SLF4J: A SLF4J service provider failed to instantiate:
org.slf4j.spi.SLF4JServiceProvider: ch.qos.logback.classic.spi.LogbackServiceProvider not a subtype
SLF4J: A SLF4J service provider failed to instantiate:
org.slf4j.spi.SLF4JServiceProvider: org.slf4j.reload4j.Reload4jServiceProvider not a subtype
SLF4J: No SLF4J providers were found.
SLF4J: Defaulting to no-operation (NOP) logger implementation
SLF4J: See https://www.slf4j.org/codes.html#noProviders for further details.
[2024-04-09 02:26:16,343]  - Start plugin 'postgres-plugin@1.0-SNAPSHOT'
SLF4J: A SLF4J service provider failed to instantiate:
org.slf4j.spi.SLF4JServiceProvider: ch.qos.logback.classic.spi.LogbackServiceProvider not a subtype
SLF4J: A SLF4J service provider failed to instantiate:
org.slf4j.spi.SLF4JServiceProvider: org.slf4j.reload4j.Reload4jServiceProvider not a subtype
SLF4J: No SLF4J providers were found.
SLF4J: Defaulting to no-operation (NOP) logger implementation
SLF4J: See https://www.slf4j.org/codes.html#noProviders for further details.
[2024-04-09 02:26:16,363]  - Start plugin 'googleai-plugin@1.0-SNAPSHOT'
[2024-04-09 02:26:16,366]  - Start plugin 'snowflake-plugin@1.0-SNAPSHOT'
SLF4J: A SLF4J service provider failed to instantiate:
org.slf4j.spi.SLF4JServiceProvider: ch.qos.logback.classic.spi.LogbackServiceProvider not a subtype
SLF4J: A SLF4J service provider failed to instantiate:
org.slf4j.spi.SLF4JServiceProvider: org.slf4j.reload4j.Reload4jServiceProvider not a subtype
SLF4J: No SLF4J providers were found.
SLF4J: Defaulting to no-operation (NOP) logger implementation
SLF4J: See https://www.slf4j.org/codes.html#noProviders for further details.
[2024-04-09 02:26:16,424]  - Start plugin 'anthropic-plugin@1.0-SNAPSHOT'
[2024-04-09 02:26:16,427]  - Start plugin 'elasticsearch-plugin@1.0-SNAPSHOT'
[2024-04-09 02:26:16,431]  - Start plugin 'mssql-plugin@1.0-SNAPSHOT'
SLF4J: A SLF4J service provider failed to instantiate:
org.slf4j.spi.SLF4JServiceProvider: ch.qos.logback.classic.spi.LogbackServiceProvider not a subtype
SLF4J: A SLF4J service provider failed to instantiate:
org.slf4j.spi.SLF4JServiceProvider: org.slf4j.reload4j.Reload4jServiceProvider not a subtype
SLF4J: No SLF4J providers were found.
SLF4J: Defaulting to no-operation (NOP) logger implementation
SLF4J: See https://www.slf4j.org/codes.html#noProviders for further details.
[2024-04-09 02:26:16,449]  - Start plugin 'redshift-plugin@1.0-SNAPSHOT'
SLF4J: A SLF4J service provider failed to instantiate:
org.slf4j.spi.SLF4JServiceProvider: ch.qos.logback.classic.spi.LogbackServiceProvider not a subtype
SLF4J: A SLF4J service provider failed to instantiate:
org.slf4j.spi.SLF4JServiceProvider: org.slf4j.reload4j.Reload4jServiceProvider not a subtype
SLF4J: No SLF4J providers were found.
SLF4J: Defaulting to no-operation (NOP) logger implementation
SLF4J: See https://www.slf4j.org/codes.html#noProviders for further details.
[2024-04-09 02:26:16,472]  - Start plugin 'databricks-plugin@1.0-SNAPSHOT'
[2024-04-09 02:26:16,486]  - Start plugin 'mongo-plugin@1.0-SNAPSHOT'
[2024-04-09 02:26:16,493]  - Start plugin 'graphql-plugin@1.0-SNAPSHOT'
[2024-04-09 02:26:16,496]  - Start plugin 'aws-lambda-plugin@1.0-SNAPSHOT'
[2024-04-09 02:26:16,543]  - Start plugin 'openai-plugin@1.0-SNAPSHOT'
SLF4J: A SLF4J service provider failed to instantiate:
org.slf4j.spi.SLF4JServiceProvider: ch.qos.logback.classic.spi.LogbackServiceProvider not a subtype
SLF4J: A SLF4J service provider failed to instantiate:
org.slf4j.spi.SLF4JServiceProvider: org.slf4j.reload4j.Reload4jServiceProvider not a subtype
SLF4J: No SLF4J providers were found.
SLF4J: Defaulting to no-operation (NOP) logger implementation
SLF4J: See https://www.slf4j.org/codes.html#noProviders for further details.
[2024-04-09 02:26:16,557]  - Start plugin 'dynamo-plugin@1.0-SNAPSHOT'
SLF4J: A SLF4J service provider failed to instantiate:
org.slf4j.spi.SLF4JServiceProvider: ch.qos.logback.classic.spi.LogbackServiceProvider not a subtype
SLF4J: A SLF4J service provider failed to instantiate:
org.slf4j.spi.SLF4JServiceProvider: org.slf4j.reload4j.Reload4jServiceProvider not a subtype
SLF4J: No SLF4J providers were found.
SLF4J: Defaulting to no-operation (NOP) logger implementation
SLF4J: See https://www.slf4j.org/codes.html#noProviders for further details.
[2024-04-09 02:26:16,584]  - Start plugin 'arangodb-plugin@1.0-SNAPSHOT'
[2024-04-09 02:26:16,587]  - Start plugin 'redis-plugin@1.0-SNAPSHOT'
SLF4J: A SLF4J service provider failed to instantiate:
org.slf4j.spi.SLF4JServiceProvider: ch.qos.logback.classic.spi.LogbackServiceProvider not a subtype
SLF4J: A SLF4J service provider failed to instantiate:
org.slf4j.spi.SLF4JServiceProvider: org.slf4j.reload4j.Reload4jServiceProvider not a subtype
SLF4J: No SLF4J providers were found.
SLF4J: Defaulting to no-operation (NOP) logger implementation
SLF4J: See https://www.slf4j.org/codes.html#noProviders for further details.
[2024-04-09 02:26:16,602]  - Start plugin 'firestore-plugin@1.0-SNAPSHOT'
SLF4J: A SLF4J service provider failed to instantiate:
org.slf4j.spi.SLF4JServiceProvider: ch.qos.logback.classic.spi.LogbackServiceProvider not a subtype
SLF4J: A SLF4J service provider failed to instantiate:
org.slf4j.spi.SLF4JServiceProvider: org.slf4j.reload4j.Reload4jServiceProvider not a subtype
SLF4J: No SLF4J providers were found.
SLF4J: Defaulting to no-operation (NOP) logger implementation
SLF4J: See https://www.slf4j.org/codes.html#noProviders for further details.
SLF4J: A SLF4J service provider failed to instantiate:
org.slf4j.spi.SLF4JServiceProvider: ch.qos.logback.classic.spi.LogbackServiceProvider not a subtype
SLF4J: A SLF4J service provider failed to instantiate:
org.slf4j.spi.SLF4JServiceProvider: org.slf4j.reload4j.Reload4jServiceProvider not a subtype
SLF4J: No SLF4J providers were found.
SLF4J: Defaulting to no-operation (NOP) logger implementation
SLF4J: See https://www.slf4j.org/codes.html#noProviders for further details.
SLF4J: A SLF4J service provider failed to instantiate:
org.slf4j.spi.SLF4JServiceProvider: ch.qos.logback.classic.spi.LogbackServiceProvider not a subtype
SLF4J: A SLF4J service provider failed to instantiate:
org.slf4j.spi.SLF4JServiceProvider: org.slf4j.reload4j.Reload4jServiceProvider not a subtype
SLF4J: No SLF4J providers were found.
SLF4J: Defaulting to no-operation (NOP) logger implementation
SLF4J: See https://www.slf4j.org/codes.html#noProviders for further details.
SLF4J: A SLF4J service provider failed to instantiate:
org.slf4j.spi.SLF4JServiceProvider: ch.qos.logback.classic.spi.LogbackServiceProvider not a subtype
SLF4J: A SLF4J service provider failed to instantiate:
org.slf4j.spi.SLF4JServiceProvider: org.slf4j.reload4j.Reload4jServiceProvider not a subtype
SLF4J: No SLF4J providers were found.
SLF4J: Defaulting to no-operation (NOP) logger implementation
SLF4J: See https://www.slf4j.org/codes.html#noProviders for further details.
SLF4J: A SLF4J service provider failed to instantiate:
org.slf4j.spi.SLF4JServiceProvider: ch.qos.logback.classic.spi.LogbackServiceProvider not a subtype
SLF4J: A SLF4J service provider failed to instantiate:
org.slf4j.spi.SLF4JServiceProvider: org.slf4j.reload4j.Reload4jServiceProvider not a subtype
SLF4J: No SLF4J providers were found.
SLF4J: Defaulting to no-operation (NOP) logger implementation
SLF4J: See https://www.slf4j.org/codes.html#noProviders for further details.
SLF4J: A SLF4J service provider failed to instantiate:
org.slf4j.spi.SLF4JServiceProvider: ch.qos.logback.classic.spi.LogbackServiceProvider not a subtype
SLF4J: A SLF4J service provider failed to instantiate:
org.slf4j.spi.SLF4JServiceProvider: org.slf4j.reload4j.Reload4jServiceProvider not a subtype
SLF4J: No SLF4J providers were found.
SLF4J: Defaulting to no-operation (NOP) logger implementation
SLF4J: See https://www.slf4j.org/codes.html#noProviders for further details.
[2024-04-09 02:26:21,029]  - Mongock runner COMMUNITY version[5.1.6]
[2024-04-09 02:26:21,029]  - Running Mongock with NO metadata
[2024-04-09 02:26:21,029]  - Property transaction-enabled not provided. It will become true as default in next versions. Set explicit value to false in case transaction are not desired.
[2024-04-09 02:26:21,029]  - Property transaction-enabled not provided and is unknown if driver is transactionable. BY DEFAULT MONGOCK WILL RUN IN NO-TRANSACTION MODE.
[2024-04-09 02:26:21,136]  - Reflections took 82 ms to scan 1 urls, producing 5 keys and 60 values
[2024-04-09 02:26:21,178]  - Reflections took 22 ms to scan 1 urls, producing 5 keys and 60 values
[2024-04-09 02:26:21,655]  - Mongock trying to acquire the lock
[2024-04-09 02:26:21,683]  - Mongock acquired the lock until: Tue Apr 09 02:27:21 UTC 2024
[2024-04-09 02:26:21,687]  - Starting mongock lock daemon...
[2024-04-09 02:26:21,688]  - Mongock starting the data migration sequence id[2024-04-09T02:26:20.908815357-52]...
[2024-04-09 02:26:21,771]  - method[initializeSchemaVersion] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 02:26:21,919]  - APPLIED - {"id"="initialize-schema-version", "type"="execution", "author"="", "class"="DatabaseChangelog0", "method"="initializeSchemaVersion"}
[2024-04-09 02:26:21,930]  - method[initialPlugins] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 02:26:21,998]  - APPLIED - {"id"="initial-plugins", "type"="execution", "author"="", "class"="DatabaseChangelog1", "method"="initialPlugins"}
[2024-04-09 02:26:22,008]  - method[addInitialIndexes] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 02:26:22,988]  - APPLIED - {"id"="initial-indexes", "type"="execution", "author"="", "class"="DatabaseChangelog1", "method"="addInitialIndexes"}
[2024-04-09 02:26:22,999]  - method[setInitialSequenceForDatasource] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 02:26:23,035]  - APPLIED - {"id"="set-initial-sequence-for-datasource", "type"="execution", "author"="", "class"="DatabaseChangelog1", "method"="setInitialSequenceForDatasource"}
[2024-04-09 02:26:23,041]  - method[setPluginImageAndDocsLink] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 02:26:23,089]  - APPLIED - {"id"="set-plugin-image-and-docs-link", "type"="execution", "author"="", "class"="DatabaseChangelog1", "method"="setPluginImageAndDocsLink"}
[2024-04-09 02:26:23,097]  - method[mysqlPlugin] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 02:26:23,125]  - APPLIED - {"id"="install-mysql-plugins", "type"="execution", "author"="", "class"="DatabaseChangelog1", "method"="mysqlPlugin"}
[2024-04-09 02:26:23,134]  - method[updateDatabaseDocumentationLinks] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 02:26:23,157]  - APPLIED - {"id"="update-database-documentation-links", "type"="execution", "author"="", "class"="DatabaseChangelog1", "method"="updateDatabaseDocumentationLinks"}
[2024-04-09 02:26:23,163]  - method[generateUniqueIdForInstance] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 02:26:23,174]  - APPLIED - {"id"="generate-unique-id-for-instance", "type"="execution", "author"="", "class"="DatabaseChangelog1", "method"="generateUniqueIdForInstance"}
[2024-04-09 02:26:23,184]  - method[fixTokenExpiration] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 02:26:23,235]  - APPLIED - {"id"="fix-password-reset-token-expiration", "type"="execution", "author"="", "class"="DatabaseChangelog1", "method"="fixTokenExpiration"}
[2024-04-09 02:26:23,242]  - method[addElasticSearchPlugin] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 02:26:23,254]  - APPLIED - {"id"="add-elastic-search-plugin", "type"="execution", "author"="", "class"="DatabaseChangelog1", "method"="addElasticSearchPlugin"}
[2024-04-09 02:26:23,266]  - method[addDynamoPlugin] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 02:26:23,279]  - APPLIED - {"id"="add-dynamo-plugin", "type"="execution", "author"="", "class"="DatabaseChangelog1", "method"="addDynamoPlugin"}
[2024-04-09 02:26:23,292]  - method[usePngLogos] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 02:26:23,305]  - APPLIED - {"id"="use-png-logos", "type"="execution", "author"="", "class"="DatabaseChangelog1", "method"="usePngLogos"}
[2024-04-09 02:26:23,313]  - method[addRedisPlugin] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 02:26:23,325]  - APPLIED - {"id"="add-redis-plugin", "type"="execution", "author"="", "class"="DatabaseChangelog1", "method"="addRedisPlugin"}
[2024-04-09 02:26:23,331]  - method[addMsSqlPlugin] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 02:26:23,356]  - APPLIED - {"id"="add-msSql-plugin", "type"="execution", "author"="", "class"="DatabaseChangelog1", "method"="addMsSqlPlugin"}
[2024-04-09 02:26:23,361]  - method[addNewPageIndexAfterDroppingNewPage] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 02:26:23,427]  - APPLIED - {"id"="createNewPageIndexAfterDroppingNewPage", "type"="execution", "author"="", "class"="DatabaseChangelog1", "method"="addNewPageIndexAfterDroppingNewPage"}
[2024-04-09 02:26:23,432]  - method[addNewActionIndexAfterDroppingNewAction] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 02:26:23,476]  - APPLIED - {"id"="createNewActionIndexAfterDroppingNewAction", "type"="execution", "author"="", "class"="DatabaseChangelog1", "method"="addNewActionIndexAfterDroppingNewAction"}
[2024-04-09 02:26:23,482]  - method[addNewPageAndNewActionNewIndexes] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 02:26:23,522]  - APPLIED - {"id"="new-page-new-action-add-indexes", "type"="execution", "author"="", "class"="DatabaseChangelog1", "method"="addNewPageAndNewActionNewIndexes"}
[2024-04-09 02:26:23,532]  - method[updateActionIndexToSingleMultipleIndices] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 02:26:23,579]  - APPLIED - {"id"="update-action-index-to-single-multiple-indices", "type"="execution", "author"="", "class"="DatabaseChangelog1", "method"="updateActionIndexToSingleMultipleIndices"}
[2024-04-09 02:26:23,589]  - method[addFirestorePlugin] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 02:26:23,619]  - APPLIED - {"id"="add-firestore-plugin", "type"="execution", "author"="", "class"="DatabaseChangelog1", "method"="addFirestorePlugin"}
[2024-04-09 02:26:23,625]  - method[addRedshiftPlugin] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 02:26:23,641]  - APPLIED - {"id"="add-redshift-plugin", "type"="execution", "author"="", "class"="DatabaseChangelog1", "method"="addRedshiftPlugin"}
[2024-04-09 02:26:23,650]  - method[clearUserDataCollection] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 02:26:23,658]  - APPLIED - {"id"="clear-userdata-collection", "type"="execution", "author"="", "class"="DatabaseChangelog1", "method"="clearUserDataCollection"}
[2024-04-09 02:26:23,670]  - method[updateDatabaseDocumentationLinks_v1_2_1] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 02:26:23,736]  - APPLIED - {"id"="update-database-documentation-links-v1-2-1", "type"="execution", "author"="", "class"="DatabaseChangelog1", "method"="updateDatabaseDocumentationLinks_v1_2_1"}
[2024-04-09 02:26:23,744]  - method[addAmazonS3Plugin] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 02:26:23,758]  - APPLIED - {"id"="add-amazons3-plugin", "type"="execution", "author"="", "class"="DatabaseChangelog1", "method"="addAmazonS3Plugin"}
[2024-04-09 02:26:23,764]  - method[updatePluginDatasourceFormComponents] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 02:26:23,835]  - APPLIED - {"id"="update-plugin-datasource-form-components", "type"="execution", "author"="", "class"="DatabaseChangelog1", "method"="updatePluginDatasourceFormComponents"}
[2024-04-09 02:26:23,840]  - method[updateS3DatasourceConfigurationAndLabel] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 02:26:23,851]  - APPLIED - {"id"="update-s3-datasource-configuration-and-label", "type"="execution", "author"="", "class"="DatabaseChangelog1", "method"="updateS3DatasourceConfigurationAndLabel"}
[2024-04-09 02:26:23,856]  - method[addGoogleSheetsPlugin] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 02:26:23,869]  - APPLIED - {"id"="add-google-sheets-plugin", "type"="execution", "author"="", "class"="DatabaseChangelog1", "method"="addGoogleSheetsPlugin"}
[2024-04-09 02:26:23,875]  - method[markInstanceAsUnregistered] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 02:26:23,881]  - APPLIED - {"id"="mark-instance-unregistered", "type"="execution", "author"="", "class"="DatabaseChangelog1", "method"="markInstanceAsUnregistered"}
[2024-04-09 02:26:23,887]  - method[addSnowflakePlugin] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 02:26:23,897]  - APPLIED - {"id"="add-snowflake-plugin", "type"="execution", "author"="", "class"="DatabaseChangelog1", "method"="addSnowflakePlugin"}
[2024-04-09 02:26:23,907]  - method[addArangoDBPlugin] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 02:26:23,924]  - APPLIED - {"id"="add-arangodb-plugin", "type"="execution", "author"="", "class"="DatabaseChangelog1", "method"="addArangoDBPlugin"}
[2024-04-09 02:26:23,932]  - method[setSvgLogoToPluginIcons] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 02:26:24,017]  - APPLIED - {"id"="set-svg-logo-to-plugins", "type"="execution", "author"="", "class"="DatabaseChangelog1", "method"="setSvgLogoToPluginIcons"}
[2024-04-09 02:26:24,036]  - method[createPluginReferenceForGenerateCRUDPage] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 02:26:24,153]  - APPLIED - {"id"="create-plugin-reference-for-genarate-CRUD-page", "type"="execution", "author"="", "class"="DatabaseChangelog1", "method"="createPluginReferenceForGenerateCRUDPage"}
[2024-04-09 02:26:24,161]  - method[createPluginReferenceForS3AndGSheetGenerateCRUDPage] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 02:26:24,233]  - APPLIED - {"id"="create-plugin-reference-for-S3-GSheet-genarate-CRUD-page", "type"="execution", "author"="", "class"="DatabaseChangelog1", "method"="createPluginReferenceForS3AndGSheetGenerateCRUDPage"}
[2024-04-09 02:26:24,241]  - method[addJSPlugin] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 02:26:24,251]  - APPLIED - {"id"="add-js-plugin", "type"="execution", "author"="", "class"="DatabaseChangelog1", "method"="addJSPlugin"}
[2024-04-09 02:26:24,257]  - method[updatePluginPackageNameIndexToPluginNamePackageNameAndVersion] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 02:26:24,397]  - APPLIED - {"id"="update-plugin-package-name-index", "type"="execution", "author"="", "class"="DatabaseChangelog1", "method"="updatePluginPackageNameIndexToPluginNamePackageNameAndVersion"}
[2024-04-09 02:26:24,403]  - method[migrateS3PluginToUqi] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 02:26:24,414]  - APPLIED - {"id"="migrate-s3-to-uqi", "type"="execution", "author"="", "class"="DatabaseChangelog1", "method"="migrateS3PluginToUqi"}
[2024-04-09 02:26:24,434]  - method[addSmtpPluginPlugin] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 02:26:24,461]  - APPLIED - {"id"="add-smtp-plugin", "type"="execution", "author"="", "class"="DatabaseChangelog1", "method"="addSmtpPluginPlugin"}
[2024-04-09 02:26:24,476]  - method[addPluginNameForGoogleSheets] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 02:26:24,505]  - APPLIED - {"id"="add-google-sheets-plugin-name", "type"="execution", "author"="", "class"="DatabaseChangelog1", "method"="addPluginNameForGoogleSheets"}
[2024-04-09 02:26:24,545]  - method[migrateFirestorePluginToUqi] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 02:26:24,573]  - APPLIED - {"id"="migrate-firestore-to-uqi", "type"="execution", "author"="", "class"="DatabaseChangelog1", "method"="migrateFirestorePluginToUqi"}
[2024-04-09 02:26:24,588]  - method[migrateFirestorePluginToUqi3] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 02:26:24,608]  - APPLIED - {"id"="migrate-firestore-to-uqi-3", "type"="execution", "author"="", "class"="DatabaseChangelog1", "method"="migrateFirestorePluginToUqi3"}
[2024-04-09 02:26:24,618]  - method[updateGitIndexes] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 02:26:24,771]  - APPLIED - {"id"="update-index-for-git", "type"="execution", "author"="", "class"="DatabaseChangelog1", "method"="updateGitIndexes"}
[2024-04-09 02:26:24,777]  - method[useAssetsCDNForPluginIcons] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 02:26:24,872]  - APPLIED - {"id"="use-assets-cdn-for-plugin-icons", "type"="execution", "author"="", "class"="DatabaseChangelog1", "method"="useAssetsCDNForPluginIcons"}
[2024-04-09 02:26:24,877]  - method[updateNewActionActionCollectionAndUserDataIndexes] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 02:26:24,922]  - APPLIED - {"id"="update-index-for-newAction-actionCollection-userData", "type"="execution", "author"="", "class"="DatabaseChangelog1", "method"="updateNewActionActionCollectionAndUserDataIndexes"}
[2024-04-09 02:26:24,928]  - method[markMSSQLCrudUnavailable] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 02:26:24,940]  - APPLIED - {"id"="mark-mssql-crud-unavailable", "type"="execution", "author"="", "class"="DatabaseChangelog1", "method"="markMSSQLCrudUnavailable"}
[2024-04-09 02:26:24,947]  - method[updateNewActionActionCollectionIndexes] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 02:26:25,028]  - APPLIED - {"id"="update-index-for-newAction-actionCollection", "type"="execution", "author"="", "class"="DatabaseChangelog1", "method"="updateNewActionActionCollectionIndexes"}
[2024-04-09 02:26:25,048]  - method[fixPluginTitleCasing] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 02:26:25,074]  - APPLIED - {"id"="fix-plugin-title-casing", "type"="execution", "author"="", "class"="DatabaseChangelog2", "method"="fixPluginTitleCasing"}
[2024-04-09 02:26:25,082]  - method[addIndexesForGit] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 02:26:25,177]  - APPLIED - {"id"="update-git-indexes", "type"="execution", "author"="", "class"="DatabaseChangelog2", "method"="addIndexesForGit"}
[2024-04-09 02:26:25,188]  - method[addWorkspaceIndexes] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 02:26:25,231]  - APPLIED - {"id"="add-workspace-indexes", "type"="execution", "author"="", "class"="DatabaseChangelog2", "method"="addWorkspaceIndexes"}
[2024-04-09 02:26:25,253]  - method[addDefaultTenant] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 02:26:25,293]  - APPLIED - {"id"="add-default-tenant", "type"="execution", "author"="", "class"="DatabaseChangelog2", "method"="addDefaultTenant"}
[2024-04-09 02:26:25,299]  - method[organizationToWorkspaceIndexesRecreate] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 02:26:25,360]  - APPLIED - {"id"="organization-to-workspace-indexes-recreate", "type"="execution", "author"="", "class"="DatabaseChangelog2", "method"="organizationToWorkspaceIndexesRecreate"}
[2024-04-09 02:26:25,372]  - method[clearRedisCache2] with arguments: [org.springframework.data.redis.core.ReactiveRedisOperations]
[2024-04-09 02:26:25,420]  - APPLIED - {"id"="flush-spring-redis-keys-2a", "type"="execution", "author"="", "class"="DatabaseChangelog2", "method"="clearRedisCache2"}
[2024-04-09 02:26:25,427]  - method[addAnonymousUser] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 02:26:25,455]  - APPLIED - {"id"="add-anonymousUser", "type"="execution", "author"="", "class"="DatabaseChangelog2", "method"="addAnonymousUser"}
[2024-04-09 02:26:25,462]  - method[addInstanceConfigurationPlaceHolder] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 02:26:25,531]  - APPLIED - {"id"="add-instance-config-object", "type"="execution", "author"="", "class"="DatabaseChangelog2", "method"="addInstanceConfigurationPlaceHolder"}
[2024-04-09 02:26:25,537]  - method[addAnonymousUserPermissionGroup] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 02:26:25,562]  - APPLIED - {"id"="add-anonymous-user-permission-group", "type"="execution", "author"="", "class"="DatabaseChangelog2", "method"="addAnonymousUserPermissionGroup"}
[2024-04-09 02:26:25,569]  - method[createThemesIndices] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 02:26:25,629]  - APPLIED - {"id"="create-themes-indices", "type"="execution", "author"="", "class"="DatabaseChangelog2", "method"="createThemesIndices"}
[2024-04-09 02:26:25,634]  - method[createSystemThemes3] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 02:26:25,804]  - APPLIED - {"id"="create-system-themes-v3", "type"="execution", "author"="", "class"="DatabaseChangelog2", "method"="createSystemThemes3"}
[2024-04-09 02:26:25,811]  - method[addPermissionGroupIndex] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 02:26:25,953]  - APPLIED - {"id"="create-indices-on-permissions-for-performance", "type"="execution", "author"="", "class"="DatabaseChangelog2", "method"="addPermissionGroupIndex"}
[2024-04-09 02:26:25,973]  - method[updateBadThemeState] with arguments: [org.springframework.data.mongodb.core.MongoTemplate, com.appsmith.server.acl.PolicyGenerator, com.appsmith.server.repositories.CacheableRepositoryHelper]
[2024-04-09 02:26:25,992]  - APPLIED - {"id"="update-bad-theme-state", "type"="execution", "author"="", "class"="DatabaseChangelog2", "method"="updateBadThemeState"}
[2024-04-09 02:26:25,999]  - method[addTenantAdminPermissionsToInstanceAdmin] with arguments: [org.springframework.data.mongodb.core.MongoTemplate, com.appsmith.server.solutions.PolicySolutionImpl]
[2024-04-09 02:26:26,041]  - APPLIED - {"id"="add-tenant-admin-permissions-instance-admin", "type"="execution", "author"="", "class"="DatabaseChangelog2", "method"="addTenantAdminPermissionsToInstanceAdmin"}
[2024-04-09 02:26:26,047]  - method[addGraphQLPlugin] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 02:26:26,062]  - APPLIED - {"id"="add-graphql-plugin", "type"="execution", "author"="", "class"="DatabaseChangelog2", "method"="addGraphQLPlugin"}
[2024-04-09 02:26:26,077]  - method[addIndicesRecommendedByMongoCloud] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 02:26:26,149]  - APPLIED - {"id"="indices-recommended-by-mongodb-cloud", "type"="execution", "author"="", "class"="DatabaseChangelog2", "method"="addIndicesRecommendedByMongoCloud"}
[2024-04-09 02:26:26,155]  - method[addUniqueIndexOnUidString] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 02:26:26,212]  - APPLIED - {"id"="add-unique-index-for-uidstring", "type"="execution", "author"="", "class"="DatabaseChangelog2", "method"="addUniqueIndexOnUidString"}
[2024-04-09 02:26:26,219]  - method[modifyReadPermissionGroupToReadPermissionGroupMembers] with arguments: [org.springframework.data.mongodb.core.MongoTemplate, com.appsmith.server.solutions.PolicySolutionImpl]
[2024-04-09 02:26:26,226]  - APPLIED - {"id"="change-readPermissionGroup-to-readPermissionGroupMembers", "type"="execution", "author"="", "class"="DatabaseChangelog2", "method"="modifyReadPermissionGroupToReadPermissionGroupMembers"}
[2024-04-09 02:26:26,233]  - method[deletePermissionsInPermissionGroups] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 02:26:26,242]  - APPLIED - {"id"="delete-permissions-in-permissionGroups", "type"="execution", "author"="", "class"="DatabaseChangelog2", "method"="deletePermissionsInPermissionGroups"}
[2024-04-09 02:26:26,250]  - method[removeUsagePulsesForAppsmithCloud] with arguments: [org.springframework.data.mongodb.core.MongoTemplate, com.appsmith.server.configurations.CommonConfig$$SpringCGLIB$$0]
[2024-04-09 02:26:26,250]  - APPLIED - {"id"="remove-usage-pulses-for-appsmith-cloud", "type"="execution", "author"="", "class"="DatabaseChangelog2", "method"="removeUsagePulsesForAppsmithCloud"}
[2024-04-09 02:26:26,259]  - method[addSslModeSettingsForExistingMssqlDatasource] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 02:26:26,287]  - APPLIED - {"id"="add-ssl-mode-settings-for-existing-mssql-datasources", "type"="execution", "author"="", "class"="DatabaseChangelog2", "method"="addSslModeSettingsForExistingMssqlDatasource"}
[2024-04-09 02:26:26,296]  - method[addOraclePlugin] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 02:26:26,312]  - APPLIED - {"id"="add-oracle-plugin", "type"="execution", "author"="", "class"="DatabaseChangelog2", "method"="addOraclePlugin"}
[2024-04-09 02:26:26,321]  - method[updateOraclePluginName] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 02:26:26,338]  - APPLIED - {"id"="update-oracle-plugin-name", "type"="execution", "author"="", "class"="DatabaseChangelog2", "method"="updateOraclePluginName"}
[2024-04-09 02:26:26,355]  - method[updateSuperUsers] with arguments: [org.springframework.data.mongodb.core.MongoTemplate, com.appsmith.server.repositories.CacheableRepositoryHelper, com.appsmith.server.solutions.PolicySolution, com.appsmith.server.acl.PolicyGenerator]
[2024-04-09 02:26:26,387]  - APPLIED - {"id"="update-super-users", "type"="execution", "author"="", "class"="DatabaseChangelog2", "method"="updateSuperUsers"}
[2024-04-09 02:26:26,399]  - method[com.appsmith.server.migrations.db.ce.Migration003AddInstanceNameToTenantConfiguration] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 02:26:26,400]  - method[addInstanceNameEnvVarToTenantConfiguration] with arguments: []
[2024-04-09 02:26:26,426]  - APPLIED - {"id"="add-instance-name-env-variable-tenant-configuration", "type"="execution", "author"="default_author", "class"="Migration003AddInstanceNameToTenantConfiguration", "method"="addInstanceNameEnvVarToTenantConfiguration"}
[2024-04-09 02:26:26,433]  - method[com.appsmith.server.migrations.db.ce.Migration004PermissionGroupDefaultWorkspaceIdMigration] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 02:26:26,433]  - method[defaultWorkspaceIdMigration] with arguments: []
No permissionGroup data to migrate.
[2024-04-09 02:26:26,439]  - APPLIED - {"id"="migrate-default-workspace-id-to-default-domain-id", "type"="execution", "author"="default_author", "class"="Migration004PermissionGroupDefaultWorkspaceIdMigration", "method"="defaultWorkspaceIdMigration"}
[2024-04-09 02:26:26,448]  - method[com.appsmith.server.migrations.db.ce.Migration005CreateIndexForApplicationSnapshotMigration] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 02:26:26,448]  - method[addIndexOnApplicationIdAndChunkOrder] with arguments: []
[2024-04-09 02:26:26,504]  - APPLIED - {"id"="create-index-for-application-snapshot-collection", "type"="execution", "author"="default_author", "class"="Migration005CreateIndexForApplicationSnapshotMigration", "method"="addIndexOnApplicationIdAndChunkOrder"}
[2024-04-09 02:26:26,509]  - method[com.appsmith.server.migrations.db.ce.Migration006ResetOnPageLoadEdgesInLayouts] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 02:26:26,509]  - method[executeMigration] with arguments: []
[2024-04-09 02:26:26,517]  - APPLIED - {"id"="reset-on-page-load-edges-in-layouts", "type"="execution", "author"="default_author", "class"="Migration006ResetOnPageLoadEdgesInLayouts", "method"="executeMigration"}
[2024-04-09 02:26:26,521]  - method[com.appsmith.server.migrations.db.ce.Migration007OptOutUnsupportedPluginsForAirGap] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 02:26:26,522]  - method[optOutUnsupportedPluginsForAirGapInstance] with arguments: []
[2024-04-09 02:26:26,568]  - APPLIED - {"id"="opt-out-unsupported-plugins-airgap-instance", "type"="execution", "author"="default_author", "class"="Migration007OptOutUnsupportedPluginsForAirGap", "method"="optOutUnsupportedPluginsForAirGapInstance"}
[2024-04-09 02:26:26,574]  - method[com.appsmith.server.migrations.db.ce.Migration008SupportNonHostedPluginsForAirgap] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 02:26:26,574]  - method[supportNonHostedPluginsForAirgap] with arguments: []
[2024-04-09 02:26:26,652]  - APPLIED - {"id"="support-non-self-hosted-plugins-for-airgap", "type"="execution", "author"="default_author", "class"="Migration008SupportNonHostedPluginsForAirgap", "method"="supportNonHostedPluginsForAirgap"}
[2024-04-09 02:26:26,658]  - method[com.appsmith.server.migrations.db.ce.Migration009UpdateOracleLogoToSVG] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 02:26:26,659]  - method[updateOracleLogoToSVG] with arguments: []
[2024-04-09 02:26:26,669]  - APPLIED - {"id"="update-oracle-logo-to-svg", "type"="execution", "author"="default_author", "class"="Migration009UpdateOracleLogoToSVG", "method"="updateOracleLogoToSVG"}
[2024-04-09 02:26:26,675]  - method[com.appsmith.server.migrations.db.ce.Migration010UpdatePluginDocsLink] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 02:26:26,675]  - method[updatePluginDocumentationLinks] with arguments: []
[2024-04-09 02:26:26,777]  - APPLIED - {"id"="update-plugins-docs-link", "type"="execution", "author"="default_author", "class"="Migration010UpdatePluginDocsLink", "method"="updatePluginDocumentationLinks"}
[2024-04-09 02:26:26,783]  - method[com.appsmith.server.migrations.db.ce.Migration011CreateIndexDefaultDomainIdDefaultDomainTypeDropIndexDefaultWorkspaceId] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 02:26:26,784]  - method[createNewIndexDefaultDomainIdDefaultDomainTypeAndDropOldIndexDefaultWorkspaceId] with arguments: []
[2024-04-09 02:26:26,939]  - APPLIED - {"id"="create-index-default-domain-id-default-domain-type", "type"="execution", "author"="default_author", "class"="Migration011CreateIndexDefaultDomainIdDefaultDomainTypeDropIndexDefaultWorkspaceId", "method"="createNewIndexDefaultDomainIdDefaultDomainTypeAndDropOldIndexDefaultWorkspaceId"}
[2024-04-09 02:26:26,952]  - method[com.appsmith.server.migrations.db.ce.Migration012RemoveStructureFromWithinDatasource] with arguments: [org.springframework.data.mongodb.core.MongoOperations, org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 02:26:26,952]  - method[executeMigration] with arguments: []
[2024-04-09 02:26:26,999]  - APPLIED - {"id"="remove-structure-from-within-datasource-modified", "type"="execution", "author"="default_author", "class"="Migration012RemoveStructureFromWithinDatasource", "method"="executeMigration"}
[2024-04-09 02:26:27,007]  - method[com.appsmith.server.migrations.db.ce.Migration013AddEmailBodyTypeToSMTPPlugin] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 02:26:27,008]  - method[addSmtpEmailBodyType] with arguments: []
[2024-04-09 02:26:27,023]  - APPLIED - {"id"="add-smtp-email-body-type", "type"="execution", "author"="default_author", "class"="Migration013AddEmailBodyTypeToSMTPPlugin", "method"="addSmtpEmailBodyType"}
[2024-04-09 02:26:27,029]  - method[com.appsmith.server.migrations.db.ce.Migration014AddIndexToDatasourceStorage] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 02:26:27,029]  - method[addingIndexToDatasourceStorage] with arguments: []
[2024-04-09 02:26:27,067]  - APPLIED - {"id"="index-for-datasource-storage", "type"="execution", "author"="default_author", "class"="Migration014AddIndexToDatasourceStorage", "method"="addingIndexToDatasourceStorage"}
[2024-04-09 02:26:27,071]  - method[com.appsmith.server.migrations.db.ce.Migration015AddPluginTypeIndexToNewActionCollection] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 02:26:27,071]  - method[addingIndexToNewAction] with arguments: []
[2024-04-09 02:26:27,089]  - APPLIED - {"id"="app-id-plugin-type-index-for-new-action", "type"="execution", "author"="default_author", "class"="Migration015AddPluginTypeIndexToNewActionCollection", "method"="addingIndexToNewAction"}
[2024-04-09 02:26:27,094]  - method[com.appsmith.server.migrations.db.ce.Migration016RenameIndexesWithLongNames] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 02:26:27,095]  - method[executeMigration] with arguments: []
[2024-04-09 02:26:27,302]  - APPLIED - {"id"="rename-indexes-with-long-names", "type"="execution", "author"="default_author", "class"="Migration016RenameIndexesWithLongNames", "method"="executeMigration"}
[2024-04-09 02:26:27,310]  - method[com.appsmith.server.migrations.db.ce.Migration017UnsetEncryptionVersion2Fields] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 02:26:27,316]  - method[executeMigration] with arguments: [org.springframework.data.mongodb.core.MongoOperations]
[2024-04-09 02:26:27,327]  - APPLIED - {"id"="unset-not-encrypted-encryption-version-2-fields", "type"="execution", "author"="default_author", "class"="Migration017UnsetEncryptionVersion2Fields", "method"="executeMigration"}
[2024-04-09 02:26:27,333]  - method[com.appsmith.server.migrations.db.ce.Migration018UpdateOraclePluginDocumentationLink] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 02:26:27,333]  - method[updateOracleDocumentationLink] with arguments: []
[2024-04-09 02:26:27,343]  - APPLIED - {"id"="update-oracle-doc-link", "type"="execution", "author"="default_author", "class"="Migration018UpdateOraclePluginDocumentationLink", "method"="updateOracleDocumentationLink"}
[2024-04-09 02:26:27,347]  - method[com.appsmith.server.migrations.db.ce.Migration019RemoveNullEnvIdDatasourceStructureDocuments] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 02:26:27,347]  - method[executeMigration] with arguments: []
[2024-04-09 02:26:27,362]  - APPLIED - {"id"="delete-null-envId-key-document", "type"="execution", "author"="default_author", "class"="Migration019RemoveNullEnvIdDatasourceStructureDocuments", "method"="executeMigration"}
[2024-04-09 02:26:27,369]  - method[com.appsmith.server.migrations.db.ce.Migration021MoveGoogleMapsKeyToTenantConfiguration] with arguments: [org.springframework.data.mongodb.core.MongoTemplate, com.appsmith.server.configurations.CommonConfig]
[2024-04-09 02:26:27,369]  - method[executeMigration] with arguments: []
[2024-04-09 02:26:27,369]  - APPLIED - {"id"="move-google-maps-key-to-tenant-configuration", "type"="execution", "author"="default_author", "class"="Migration021MoveGoogleMapsKeyToTenantConfiguration", "method"="executeMigration"}
[2024-04-09 02:26:27,375]  - method[com.appsmith.server.migrations.db.ce.Migration022AddConnectionMethodDefaultValueToAllMySQLDatasources] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 02:26:27,375]  - method[updateConnectionMethodDefaultValueForMySQL] with arguments: []
[2024-04-09 02:26:27,385]  - APPLIED - {"id"="add-connection-method-default-value-for-mysql", "type"="execution", "author"="default_author", "class"="Migration022AddConnectionMethodDefaultValueToAllMySQLDatasources", "method"="updateConnectionMethodDefaultValueForMySQL"}
[2024-04-09 02:26:27,395]  - method[com.appsmith.server.migrations.db.ce.Migration023UpdateJSPluginIcon] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 02:26:27,395]  - method[executeMigration] with arguments: []
[2024-04-09 02:26:27,404]  - APPLIED - {"id"="update-js-plugin-icon", "type"="execution", "author"="default_author", "class"="Migration023UpdateJSPluginIcon", "method"="executeMigration"}
[2024-04-09 02:26:27,411]  - method[com.appsmith.server.migrations.db.ce.Migration024EnableGenerateCRUDPageToggleForMssqlPlugin] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 02:26:27,411]  - method[enableGenerateCRUDPageToggle] with arguments: []
[2024-04-09 02:26:27,426]  - APPLIED - {"id"="enable-generate-crud-page-for-mssql", "type"="execution", "author"="default_author", "class"="Migration024EnableGenerateCRUDPageToggleForMssqlPlugin", "method"="enableGenerateCRUDPageToggle"}
[2024-04-09 02:26:27,431]  - method[com.appsmith.server.migrations.db.ce.Migration025AddIndexDeletedInApplication] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 02:26:27,431]  - method[createIndexInApplicationCollection] with arguments: []
[2024-04-09 02:26:27,451]  - APPLIED - {"id"="add-index-application-deleted", "type"="execution", "author"="default_author", "class"="Migration025AddIndexDeletedInApplication", "method"="createIndexInApplicationCollection"}
[2024-04-09 02:26:27,455]  - method[com.appsmith.server.migrations.db.ce.Migration025RemoveUnassignPermissionFromUnnecessaryRoles] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 02:26:27,456]  - method[executeMigration] with arguments: []
[2024-04-09 02:26:27,458]  - Check if performant query can be used.
[2024-04-09 02:26:27,467]  - Using performant query.
[2024-04-09 02:26:27,471]  - APPLIED - {"id"="remove-unassign-permission-from-workspace-dev-viewer-roles", "type"="execution", "author"="default_author", "class"="Migration025RemoveUnassignPermissionFromUnnecessaryRoles", "method"="executeMigration"}
[2024-04-09 02:26:27,475]  - method[com.appsmith.server.migrations.db.ce.Migration026AddIndexTenantAndDeletedInWorkspace] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 02:26:27,476]  - method[addIndexInWorkspaceCollection] with arguments: []
[2024-04-09 02:26:27,494]  - APPLIED - {"id"="add-index-workspace-tenant-deleted", "type"="execution", "author"="default_author", "class"="Migration026AddIndexTenantAndDeletedInWorkspace", "method"="addIndexInWorkspaceCollection"}
[2024-04-09 02:26:27,498]  - method[com.appsmith.server.migrations.db.ce.Migration027AddIndexDatasourceIdAndDeletedInAction] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 02:26:27,498]  - method[addIndexInNewActionCollection] with arguments: []
[2024-04-09 02:26:27,533]  - APPLIED - {"id"="new-action-compound-index-datasource-id", "type"="execution", "author"="default_author", "class"="Migration027AddIndexDatasourceIdAndDeletedInAction", "method"="addIndexInNewActionCollection"}
[2024-04-09 02:26:27,538]  - method[com.appsmith.server.migrations.db.ce.Migration028TagUserManagementRolesWithoutDefaultDomainTypeAndId] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 02:26:27,538]  - method[tagUserManagementRolesWithoutDefaultDomainTypeAndId] with arguments: []
[2024-04-09 02:26:27,556]  - APPLIED - {"id"="tag-user-management-roles-without-default-domain-type-and-id", "type"="execution", "author"="default_author", "class"="Migration028TagUserManagementRolesWithoutDefaultDomainTypeAndId", "method"="tagUserManagementRolesWithoutDefaultDomainTypeAndId"}
[2024-04-09 02:26:27,561]  - method[com.appsmith.server.migrations.db.ce.Migration029PopulateDefaultDomainIdInUserManagementRoles] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 02:26:27,563]  - method[populateDefaultDomainIdInUserManagementRoles] with arguments: []
[2024-04-09 02:26:27,581]  - APPLIED - {"id"="populate-default-domain-id-in-user-management-roles", "type"="execution", "author"="default_author", "class"="Migration029PopulateDefaultDomainIdInUserManagementRoles", "method"="populateDefaultDomainIdInUserManagementRoles"}
[2024-04-09 02:26:27,585]  - method[com.appsmith.server.migrations.db.ce.Migration030TagUsersWithNoUserManagementRoles] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 02:26:27,586]  - method[tagUsersWithNoUserManagementRoles] with arguments: []
[2024-04-09 02:26:27,596]  - APPLIED - {"id"="tag-users-with-no-user-management-roles", "type"="execution", "author"="default_author", "class"="Migration030TagUsersWithNoUserManagementRoles", "method"="tagUsersWithNoUserManagementRoles"}
[2024-04-09 02:26:27,603]  - method[com.appsmith.server.migrations.db.ce.Migration031CreateUserManagementRolesForUsersTaggedIn030] with arguments: [org.springframework.data.mongodb.core.MongoTemplate, com.appsmith.server.solutions.PolicySolution]
[2024-04-09 02:26:27,603]  - method[createUserManagementRolesForUsersTaggedInMigration030] with arguments: []
[2024-04-09 02:26:27,604]  - Check if performant query can be used.
[2024-04-09 02:26:27,610]  - Using performant query.
[2024-04-09 02:26:27,614]  - APPLIED - {"id"="create-user-management-roles-for-users-tagged-in-migration-030", "type"="execution", "author"="default_author", "class"="Migration031CreateUserManagementRolesForUsersTaggedIn030", "method"="createUserManagementRolesForUsersTaggedInMigration030"}
[2024-04-09 02:26:27,621]  - method[com.appsmith.server.migrations.db.ce.Migration032AddingXmlParserToApplicationLibraries] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 02:26:27,621]  - method[addXmlParserEntryToEachApplication] with arguments: []
[2024-04-09 02:26:27,657]  - Going to add Xml Parser uid in all application DTOs
[2024-04-09 02:26:27,666]  - APPLIED - {"id"="add-xml-parser-to-application-jslibs", "type"="execution", "author"="default_author", "class"="Migration032AddingXmlParserToApplicationLibraries", "method"="addXmlParserEntryToEachApplication"}
[2024-04-09 02:26:27,670]  - method[com.appsmith.server.migrations.db.ce.Migration033AddOpenAIPlugin] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 02:26:27,671]  - method[addPluginToDbAndWorkspace] with arguments: []
[2024-04-09 02:26:27,680]  - APPLIED - {"id"="add-open-ai-plugin", "type"="execution", "author"="default_author", "class"="Migration033AddOpenAIPlugin", "method"="addPluginToDbAndWorkspace"}
[2024-04-09 02:26:27,685]  - method[com.appsmith.server.migrations.db.ce.Migration034ChangeOpenAIIntegrationDocumentationLink] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 02:26:27,685]  - method[changeDocumentationLink] with arguments: []
[2024-04-09 02:26:27,695]  - APPLIED - {"id"="change-open-ai-integration-documentation-link", "type"="execution", "author"="default_author", "class"="Migration034ChangeOpenAIIntegrationDocumentationLink", "method"="changeDocumentationLink"}
[2024-04-09 02:26:27,700]  - method[com.appsmith.server.migrations.db.ce.Migration035AddAnthropicPlugin] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 02:26:27,701]  - method[addPluginToDbAndWorkspace] with arguments: []
[2024-04-09 02:26:27,713]  - APPLIED - {"id"="add-anthropic-plugin", "type"="execution", "author"="default_author", "class"="Migration035AddAnthropicPlugin", "method"="addPluginToDbAndWorkspace"}
[2024-04-09 02:26:27,724]  - method[com.appsmith.server.migrations.db.ce.Migration035RemoveMockDbEndPointInDatasourceInSelfHostedInstance] with arguments: [org.springframework.data.mongodb.core.MongoTemplate, com.appsmith.server.configurations.CommonConfig, com.appsmith.server.solutions.EnvManager]
[2024-04-09 02:26:27,725]  - method[removeMockDbEndpointInDatasource] with arguments: []
[2024-04-09 02:26:27,735]  - APPLIED - {"id"="remove-mockdb-endpoint-in-datasource-self-hosted-instance", "type"="execution", "author"="default_author", "class"="Migration035RemoveMockDbEndPointInDatasourceInSelfHostedInstance", "method"="removeMockDbEndpointInDatasource"}
[2024-04-09 02:26:27,741]  - method[com.appsmith.server.migrations.db.ce.Migration036AddRecentlyUsedEntitiesForUserData] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 02:26:27,742]  - method[addRecentlyUsedEntitiesForUserData] with arguments: []
[2024-04-09 02:26:27,747]  - APPLIED - {"id"="add-recently-used-entities-for-user", "type"="execution", "author"="default_author", "class"="Migration036AddRecentlyUsedEntitiesForUserData", "method"="addRecentlyUsedEntitiesForUserData"}
[2024-04-09 02:26:27,753]  - method[com.appsmith.server.migrations.db.ce.Migration037AddCompoundIndexForNameAndDeletedAt] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 02:26:27,753]  - method[addIndexInWorkspaceAndApplicationsCollection] with arguments: []
[2024-04-09 02:26:27,802]  - APPLIED - {"id"="add-compound-index-name-deleted", "type"="execution", "author"="default_author", "class"="Migration037AddCompoundIndexForNameAndDeletedAt", "method"="addIndexInWorkspaceAndApplicationsCollection"}
[2024-04-09 02:26:27,808]  - method[com.appsmith.server.migrations.db.ce.Migration037MarkAnonymousUserAsSystemGenerated] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 02:26:27,809]  - method[executeMigration] with arguments: []
[2024-04-09 02:26:27,818]  - Marked 1 anonymous users as system generated
[2024-04-09 02:26:27,818]  - APPLIED - {"id"="mark-anonymous-user-as-system-generated", "type"="execution", "author"="default_author", "class"="Migration037MarkAnonymousUserAsSystemGenerated", "method"="executeMigration"}
[2024-04-09 02:26:27,825]  - method[com.appsmith.server.migrations.db.ce.Migration038AddCompoundIndexForActionCollection] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 02:26:27,826]  - method[addIndexInActionCollection] with arguments: []
[2024-04-09 02:26:27,860]  - APPLIED - {"id"="add-compound-index-action-collection", "type"="execution", "author"="default_author", "class"="Migration038AddCompoundIndexForActionCollection", "method"="addIndexInActionCollection"}
[2024-04-09 02:26:27,865]  - method[com.appsmith.server.migrations.db.ce.Migration038AddGoogleAIPlugin] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 02:26:27,865]  - method[addPluginToDbAndWorkspace] with arguments: []
[2024-04-09 02:26:27,880]  - APPLIED - {"id"="add-google-ai-plugin", "type"="execution", "author"="default_author", "class"="Migration038AddGoogleAIPlugin", "method"="addPluginToDbAndWorkspace"}
[2024-04-09 02:26:27,886]  - method[com.appsmith.server.migrations.db.ce.Migration039AddCompoundIndexForDatasourceStorage] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 02:26:27,886]  - method[addIndexInDatasourceStorageCollection] with arguments: []
[2024-04-09 02:26:27,915]  - APPLIED - {"id"="add-compound-index-datasource-storage", "type"="execution", "author"="default_author", "class"="Migration039AddCompoundIndexForDatasourceStorage", "method"="addIndexInDatasourceStorageCollection"}
[2024-04-09 02:26:27,925]  - method[com.appsmith.server.migrations.db.ce.Migration039AddDatabricksPlugin] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 02:26:27,926]  - method[addPluginToDbAndWorkspace] with arguments: []
[2024-04-09 02:26:27,939]  - APPLIED - {"id"="add-databricks-plugin", "type"="execution", "author"="default_author", "class"="Migration039AddDatabricksPlugin", "method"="addPluginToDbAndWorkspace"}
[2024-04-09 02:26:27,943]  - method[com.appsmith.server.migrations.db.ce.Migration039OpenAIMessagesJsToggle] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 02:26:27,943]  - method[moveMessagesToDataKeyForSupportingJsToggle] with arguments: []
[2024-04-09 02:26:27,950]  - APPLIED - {"id"="move-messages-to-data-key-in-openai", "type"="execution", "author"="default_author", "class"="Migration039OpenAIMessagesJsToggle", "method"="moveMessagesToDataKeyForSupportingJsToggle"}
[2024-04-09 02:26:27,969]  - method[com.appsmith.server.migrations.db.ce.Migration040AddAWSLambdaPlugin] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 02:26:27,970]  - method[addPluginToDbAndWorkspace] with arguments: []
[2024-04-09 02:26:27,989]  - APPLIED - {"id"="add-aws-lambda-plugin", "type"="execution", "author"="default_author", "class"="Migration040AddAWSLambdaPlugin", "method"="addPluginToDbAndWorkspace"}
[2024-04-09 02:26:27,995]  - method[com.appsmith.server.migrations.db.ce.Migration040AddAppsmithAiPlugin] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 02:26:27,996]  - method[addPluginToDbAndWorkspace] with arguments: []
[2024-04-09 02:26:28,007]  - APPLIED - {"id"="add-appsmith-ai-plugin", "type"="execution", "author"="default_author", "class"="Migration040AddAppsmithAiPlugin", "method"="addPluginToDbAndWorkspace"}
[2024-04-09 02:26:28,014]  - method[com.appsmith.server.migrations.db.ce.Migration041TagWorkspacesForGitOperationsPermissionMigration] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 02:26:28,014]  - method[addPermissionForGitOperationsToExistingWorkspaces] with arguments: []
[2024-04-09 02:26:28,030]  - APPLIED - {"id"="tag-workspaces-to-migrate-adding-git-permissions", "type"="execution", "author"="default_author", "class"="Migration041TagWorkspacesForGitOperationsPermissionMigration", "method"="addPermissionForGitOperationsToExistingWorkspaces"}
[2024-04-09 02:26:28,036]  - method[com.appsmith.server.migrations.db.ce.Migration042AddPermissionsForGitOperations] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 02:26:28,036]  - method[addPermissionForGitOperationsToExistingApplications] with arguments: []
[2024-04-09 02:26:28,043]  - APPLIED - {"id"="add-permissions-for-git-operations", "type"="execution", "author"="default_author", "class"="Migration042AddPermissionsForGitOperations", "method"="addPermissionForGitOperationsToExistingApplications"}
[2024-04-09 02:26:28,048]  - method[com.appsmith.server.migrations.db.ce.Migration043AddIndexActionCollectionPageID] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 02:26:28,048]  - method[addIndexToActionCollectionCollection] with arguments: []
[2024-04-09 02:26:28,093]  - APPLIED - {"id"="add-index-to-action-collection-default-resources-pageid", "type"="execution", "author"="default_author", "class"="Migration043AddIndexActionCollectionPageID", "method"="addIndexToActionCollectionCollection"}
[2024-04-09 02:26:28,103]  - method[com.appsmith.server.migrations.db.ce.Migration044AddIndexesToDomainObjects] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 02:26:28,103]  - method[addIndexToDomainObjects] with arguments: []
[2024-04-09 02:26:28,246]  - APPLIED - {"id"="add-index-to-domain-objects", "type"="execution", "author"="default_author", "class"="Migration044AddIndexesToDomainObjects", "method"="addIndexToDomainObjects"}
[2024-04-09 02:26:28,254]  - method[com.appsmith.server.migrations.db.ce.Migration045AddDefaultAppsmithAiDatasourceForOrphanActions] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 02:26:28,254]  - method[addDefaultAppsmithAiDatasourceForOrphanActions] with arguments: []
[2024-04-09 02:26:28,270]  - APPLIED - {"id"="add-default-appsmith-datasource", "type"="execution", "author"="default_author", "class"="Migration045AddDefaultAppsmithAiDatasourceForOrphanActions", "method"="addDefaultAppsmithAiDatasourceForOrphanActions"}
[2024-04-09 02:26:28,277]  - method[com.appsmith.server.migrations.db.ce.Migration046DeleteArchivedPlugins] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 02:26:28,278]  - method[deleteArchivedPlugins] with arguments: []
[2024-04-09 02:26:28,295]  - APPLIED - {"id"="delete-archived-pluginsd", "type"="execution", "author"="default_author", "class"="Migration046DeleteArchivedPlugins", "method"="deleteArchivedPlugins"}
[2024-04-09 02:26:28,299]  - method[com.appsmith.server.migrations.db.ce.Migration047AddMissingFieldsInDefaultAppsmithAiDatasource] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 02:26:28,301]  - method[addMissingFieldsInDefaultAppsmithAiDatasource] with arguments: []
[2024-04-09 02:26:28,317]  - APPLIED - {"id"="add-missing-fields-in-default-appsmith-datasource", "type"="execution", "author"="default_author", "class"="Migration047AddMissingFieldsInDefaultAppsmithAiDatasource", "method"="addMissingFieldsInDefaultAppsmithAiDatasource"}
[2024-04-09 02:26:28,323]  - method[com.appsmith.server.migrations.db.ce.Migration048AddCompoundIndexToUserEntity] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 02:26:28,324]  - method[addMissingIndexInUserCollection] with arguments: []
[2024-04-09 02:26:28,488]  - APPLIED - {"id"="add-compound-index-in-user-collection", "type"="execution", "author"="default_author", "class"="Migration048AddCompoundIndexToUserEntity", "method"="addMissingIndexInUserCollection"}
[2024-04-09 02:26:28,495]  - method[com.appsmith.server.migrations.db.ce.Migration049RemoveLayoutBaseFields] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 02:26:28,495]  - method[execute] with arguments: []
[2024-04-09 02:26:28,526]  - APPLIED - {"id"="remove-layout-base-fields", "type"="execution", "author"="default_author", "class"="Migration049RemoveLayoutBaseFields", "method"="execute"}
[2024-04-09 02:26:28,530]  - method[com.appsmith.server.migrations.db.ce.Migration020TransferToDatasourceStorage] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 02:26:28,530]  - method[executeMigration] with arguments: []
[2024-04-09 02:26:28,531]  - Check if performant query can be used.
[2024-04-09 02:26:28,537]  - Using performant query.
[2024-04-09 02:26:28,545]  - APPLIED - {"id"="migrate-configurations-to-data-storage-v2", "type"="execution", "author"="default_author", "class"="Migration020TransferToDatasourceStorage", "method"="executeMigration"}
[2024-04-09 02:26:28,552]  - Mongock releasing the lock
[2024-04-09 02:26:28,552]  - Mongock releasing the lock
[2024-04-09 02:26:28,559]  - Mongock released the lock
[2024-04-09 02:26:28,559]  - Mongock has finished
[2024-04-09 02:26:29,687]  - Exposing 2 endpoint(s) beneath base path '/actuator'
[2024-04-09 02:26:30,139]  - Netty started on port 8080
[2024-04-09 02:26:30,164]  - Started ServerApplication in 27.883 seconds (process running for 32.858)
[2024-04-09 02:26:30,185]  - Performing RTS health check of this instance...
[2024-04-09 02:26:30,223]  - In memory cache miss for anonymous user permission groups. Fetching from DB and adding it to in memory storage.
[2024-04-09 02:26:30,346]  - Fetched and set conenncted mongo db version as: 5.0.26
[2024-04-09 02:26:30,420]  - Triggering registration of this instance...
[2024-04-09 02:26:32,911]  - Registration successful, updating state ...
[2024-04-09 02:26:32,999]  - RTS health check succeeded
[2024-04-09 02:26:33,008]  - License verification completed with status: valid
[2024-04-09 02:26:33,047]  - Cache miss for key tenantNewFeatures:6614a751cb467e382bcada07
[2024-04-09 02:26:33,388]  - Cache entry added for key tenantNewFeatures:6614a751cb467e382bcada07
[2024-04-09 02:26:40,140]  - Fetching features for default tenant
[2024-04-09 02:26:41,476]  - Cancelled mongock lock daemon
[2024-04-09 02:26:56,040] userEmail=anonymousUser, sessionId=, thread=reactor-http-epoll-3, requestId=d8efa1b4-78e4-4dfa-ba60-f62e0bf08e5c - Going to fetch consolidatedAPI response for applicationId: null, defaultPageId: null, branchName: null, mode: PUBLISHED
[2024-04-09 02:26:56,113] userEmail=anonymousUser, sessionId=, thread=lettuce-epollEventLoop-10-1, requestId=d8efa1b4-78e4-4dfa-ba60-f62e0bf08e5c - Cache miss for key featureFlag:68385f9726ad05c8e551a8d5be5cd8213f1e733346a9eb00e86f83df94639555
[2024-04-09 02:26:56,162] userEmail=anonymousUser, sessionId=, thread=lettuce-epollEventLoop-10-1, requestId=d8efa1b4-78e4-4dfa-ba60-f62e0bf08e5c - Cache miss for key permissionGroupsForUser:anonymousUser6614a751cb467e382bcada07
[2024-04-09 02:26:56,167] userEmail=anonymousUser, sessionId=, thread=lettuce-epollEventLoop-10-1, requestId=d8efa1b4-78e4-4dfa-ba60-f62e0bf08e5c - Cache entry added for key permissionGroupsForUser:anonymousUser6614a751cb467e382bcada07
[2024-04-09 02:26:56,429] userEmail=anonymousUser, sessionId=, thread=reactor-http-epoll-3, requestId=d8efa1b4-78e4-4dfa-ba60-f62e0bf08e5c - Cache entry added for key featureFlag:68385f9726ad05c8e551a8d5be5cd8213f1e733346a9eb00e86f83df94639555
[2024-04-09 02:27:53,359] userEmail=anonymousUser, sessionId=, thread=reactor-http-epoll-3, requestId=efcadfcf-d9f6-4ca3-98fa-527e4a36b9a3 - Going to fetch consolidatedAPI response for applicationId: null, defaultPageId: null, branchName: null, mode: PUBLISHED
[2024-04-09 02:28:13,534] userEmail=anonymousUser, sessionId=, thread=reactor-http-epoll-3, requestId=abd3a226-c761-41b7-bab2-8e20dec64d17 - Going to fetch consolidatedAPI response for applicationId: null, defaultPageId: null, branchName: null, mode: PUBLISHED
[2024-04-09 02:28:42,528] userEmail=anonymousUser, sessionId=, thread=reactor-http-epoll-3, requestId=ad87f817-dd23-4134-84fb-e6736a350e42 - Going to fetch consolidatedAPI response for applicationId: null, defaultPageId: null, branchName: null, mode: PUBLISHED
[2024-04-09 02:29:22,269] userEmail=anonymousUser, sessionId=, thread=lettuce-epollEventLoop-10-1, requestId=ae55875f-f202-4f3b-9ba4-fe32a9976100 - Cache miss for key permissionGroupsForUser:1037415873@qq.com6614a751cb467e382bcada07
[2024-04-09 02:29:22,291] userEmail=anonymousUser, sessionId=, thread=nioEventLoopGroup-3-6, requestId=ae55875f-f202-4f3b-9ba4-fe32a9976100 - Cache entry added for key permissionGroupsForUser:1037415873@qq.com6614a751cb467e382bcada07
[2024-04-09 02:29:22,307] userEmail=anonymousUser, sessionId=, thread=nioEventLoopGroup-3-6, requestId=ae55875f-f202-4f3b-9ba4-fe32a9976100 - UserServiceCEImpl::Time taken for create user: 155 ms
[2024-04-09 02:29:22,659] userEmail=anonymousUser, sessionId=, thread=nioEventLoopGroup-3-6, requestId=ae55875f-f202-4f3b-9ba4-fe32a9976100 - Cache entry evicted for key permissionGroupsForUser:1037415873@qq.com6614a751cb467e382bcada07
[2024-04-09 02:29:22,683] userEmail=anonymousUser, sessionId=, thread=nioEventLoopGroup-3-6, requestId=ae55875f-f202-4f3b-9ba4-fe32a9976100 - UserServiceCEImpl::Time taken to create default workspace: 335 ms
[2024-04-09 02:29:22,684] userEmail=anonymousUser, sessionId=, thread=nioEventLoopGroup-3-6, requestId=ae55875f-f202-4f3b-9ba4-fe32a9976100 - Created blank default workspace for user '<EMAIL>'.
[2024-04-09 02:29:22,697] userEmail=anonymousUser, sessionId=, thread=nioEventLoopGroup-3-6, requestId=ae55875f-f202-4f3b-9ba4-fe32a9976100 - UserServiceCEImpl::Time taken to find created user: 545 ms
[2024-04-09 02:29:22,697] userEmail=anonymousUser, sessionId=, thread=nioEventLoopGroup-3-6, requestId=ae55875f-f202-4f3b-9ba4-fe32a9976100 - UserSignupCEImpl::Time taken for create user and send email: 566 ms
[2024-04-09 02:29:22,702] userEmail=anonymousUser, sessionId=, thread=nioEventLoopGroup-3-6, requestId=ae55875f-f202-4f3b-9ba4-fe32a9976100 - Login succeeded for user: User(name=kun wang, email=<EMAIL>, hashedEmail=null, passwordResetInitiated=false, source=FORM, state=ACTIVATED, isEnabled=true, emailVerificationRequired=null, emailVerified=null, currentWorkspaceId=null, workspaceIds=null, examplesWorkspaceId=null, groupIds=[], permissions=[], inviteToken=null, isAnonymous=false, tenantId=6614a751cb467e382bcada07, isSystemGenerated=null)
[2024-04-09 02:29:22,860] userEmail=anonymousUser, sessionId=, thread=lettuce-epollEventLoop-10-1, requestId=ae55875f-f202-4f3b-9ba4-fe32a9976100 - Cache miss for key permissionGroupsForUser:1037415873@qq.com6614a751cb467e382bcada07
[2024-04-09 02:29:22,869] userEmail=anonymousUser, sessionId=, thread=nioEventLoopGroup-3-6, requestId=ae55875f-f202-4f3b-9ba4-fe32a9976100 - Cache entry added for key permissionGroupsForUser:1037415873@qq.com6614a751cb467e382bcada07
[2024-04-09 02:29:23,381] userEmail=anonymousUser, sessionId=, thread=nioEventLoopGroup-3-6, requestId=ae55875f-f202-4f3b-9ba4-fe32a9976100 - Published application 6614a802cb467e382bcada29 in 159 ms
[2024-04-09 02:29:23,395] userEmail=anonymousUser, sessionId=, thread=nioEventLoopGroup-3-6, requestId=ae55875f-f202-4f3b-9ba4-fe32a9976100 - UserSignupCEImpl::Time taken for authentication success: 631 ms
[2024-04-09 02:29:23,395] userEmail=anonymousUser, sessionId=, thread=nioEventLoopGroup-3-6, requestId=ae55875f-f202-4f3b-9ba4-fe32a9976100 - UserSignupCEImpl::Time taken to complete signupAndLogin: 1408 ms
[2024-04-09 02:29:23,421] userEmail=anonymousUser, sessionId=, thread=nioEventLoopGroup-3-6, requestId=ae55875f-f202-4f3b-9ba4-fe32a9976100 - Cache entry evicted for key permissionGroupsForUser:1037415873@qq.com6614a751cb467e382bcada07
[2024-04-09 02:29:23,423] userEmail=anonymousUser, sessionId=, thread=lettuce-epollEventLoop-10-1, requestId=ae55875f-f202-4f3b-9ba4-fe32a9976100 - UserSignupCEImpl::Time taken to complete makeSuperUser: 24 ms
[2024-04-09 02:29:23,429]  - UserSignupCEImpl::Time taken to send create super user event: 0 ms
[2024-04-09 02:29:23,436] userEmail=anonymousUser, sessionId=, thread=lettuce-epollEventLoop-10-1, requestId=ae55875f-f202-4f3b-9ba4-fe32a9976100 - Cache miss for key permissionGroupsForUser:1037415873@qq.com6614a751cb467e382bcada07
[2024-04-09 02:29:23,449] userEmail=anonymousUser, sessionId=, thread=nioEventLoopGroup-3-6, requestId=ae55875f-f202-4f3b-9ba4-fe32a9976100 - UserSignupCEImpl::Time taken to update user data for user: 19 ms
[2024-04-09 02:29:23,450]  - UserSignupCEImpl::Time taken to get instance ID: 0 ms
[2024-04-09 02:29:23,450]  - UserSignupCEImpl::Time taken to get external address: 0 ms
[2024-04-09 02:29:23,451]  - Installation setup complete.
[2024-04-09 02:29:23,455]  - UserSignupCEImpl::Time taken to send installation setup complete analytics event: 1 ms
[2024-04-09 02:29:23,455]  - UserSignupCEImpl::Time taken to send installation setup analytics event: 6 ms
[2024-04-09 02:29:23,465] userEmail=anonymousUser, sessionId=, thread=nioEventLoopGroup-3-4, requestId=ae55875f-f202-4f3b-9ba4-fe32a9976100 - Cache entry added for key permissionGroupsForUser:1037415873@qq.com6614a751cb467e382bcada07
[2024-04-09 02:29:23,598] userEmail=anonymousUser, sessionId=, thread=nioEventLoopGroup-3-6, requestId=ae55875f-f202-4f3b-9ba4-fe32a9976100 - UserSignupCEImpl::Time taken to apply env changes: 165 ms
[2024-04-09 02:29:23,598] userEmail=anonymousUser, sessionId=, thread=nioEventLoopGroup-3-6, requestId=ae55875f-f202-4f3b-9ba4-fe32a9976100 - UserSignupCEImpl::Time taken to complete all secondary functions: 168 ms
[2024-04-09 02:29:23,598] userEmail=anonymousUser, sessionId=, thread=nioEventLoopGroup-3-6, requestId=ae55875f-f202-4f3b-9ba4-fe32a9976100 - UserSignupCEImpl::Time taken for the user mono to complete: 1637 ms
[2024-04-09 02:29:24,205] userEmail=<EMAIL>, sessionId=65e970a7-e395-4475-821a-7440618a868b, thread=lettuce-epollEventLoop-10-1, requestId=0ab8be9d-265a-468c-87d3-8faeb2e70c0d - Going to fetch consolidatedAPI response for applicationId: null, defaultPageId: null, branchName: null, mode: PUBLISHED
[2024-04-09 02:29:24,222] userEmail=<EMAIL>, sessionId=65e970a7-e395-4475-821a-7440618a868b, thread=lettuce-epollEventLoop-10-1, requestId=0ab8be9d-265a-468c-87d3-8faeb2e70c0d - Cache miss for key featureFlag:eb14d5413a965c055d666d1f4150c6c5162c1b04786cd542e94ac033c53173cc
[2024-04-09 02:29:24,474] userEmail=<EMAIL>, sessionId=65e970a7-e395-4475-821a-7440618a868b, thread=reactor-http-epoll-1, requestId=0ab8be9d-265a-468c-87d3-8faeb2e70c0d - Cache entry added for key featureFlag:eb14d5413a965c055d666d1f4150c6c5162c1b04786cd542e94ac033c53173cc
[2024-04-09 02:29:25,585] userEmail=<EMAIL>, sessionId=65e970a7-e395-4475-821a-7440618a868b, thread=lettuce-epollEventLoop-10-1, requestId=9d64fa29-430e-478c-9f8d-b17dcff195e6 - Going to get all applications by workspace id 6614a802cb467e382bcada24
[2024-04-09 02:29:25,800] userEmail=<EMAIL>, sessionId=65e970a7-e395-4475-821a-7440618a868b, thread=lettuce-epollEventLoop-10-1, requestId=4da256f4-ab84-40af-b2a6-264c93eef33f - Going to get all resources from base controller {workspaceId=[6614a802cb467e382bcada24]}
[2024-04-09 02:29:25,819] userEmail=<EMAIL>, sessionId=65e970a7-e395-4475-821a-7440618a868b, thread=nioEventLoopGroup-3-4, requestId=4da256f4-ab84-40af-b2a6-264c93eef33f - Fetching plugins by params: {workspaceId=[6614a802cb467e382bcada24]} for org: kun's apps
[2024-04-09 02:29:25,850] userEmail=<EMAIL>, sessionId=65e970a7-e395-4475-821a-7440618a868b, thread=nioEventLoopGroup-3-4, requestId=4da256f4-ab84-40af-b2a6-264c93eef33f - Error loading templates metadata in plugin restapi-plugin
[2024-04-09 02:29:25,870] userEmail=<EMAIL>, sessionId=65e970a7-e395-4475-821a-7440618a868b, thread=nioEventLoopGroup-3-4, requestId=4da256f4-ab84-40af-b2a6-264c93eef33f - Error loading templates metadata in plugin elasticsearch-plugin
[2024-04-09 02:29:25,873] userEmail=<EMAIL>, sessionId=65e970a7-e395-4475-821a-7440618a868b, thread=nioEventLoopGroup-3-4, requestId=4da256f4-ab84-40af-b2a6-264c93eef33f - Error loading templates metadata in plugin dynamo-plugin
[2024-04-09 02:29:25,878] userEmail=<EMAIL>, sessionId=65e970a7-e395-4475-821a-7440618a868b, thread=nioEventLoopGroup-3-4, requestId=4da256f4-ab84-40af-b2a6-264c93eef33f - Error loading templates metadata in plugin redis-plugin
[2024-04-09 02:29:25,885] userEmail=<EMAIL>, sessionId=65e970a7-e395-4475-821a-7440618a868b, thread=nioEventLoopGroup-3-4, requestId=4da256f4-ab84-40af-b2a6-264c93eef33f - Error loading templates metadata in plugin firestore-plugin
[2024-04-09 02:29:25,888] userEmail=<EMAIL>, sessionId=65e970a7-e395-4475-821a-7440618a868b, thread=nioEventLoopGroup-3-4, requestId=4da256f4-ab84-40af-b2a6-264c93eef33f - Error loading templates metadata in plugin redshift-plugin
[2024-04-09 02:29:25,891] userEmail=<EMAIL>, sessionId=65e970a7-e395-4475-821a-7440618a868b, thread=nioEventLoopGroup-3-4, requestId=4da256f4-ab84-40af-b2a6-264c93eef33f - Error loading templates metadata in plugin amazons3-plugin
[2024-04-09 02:29:25,895] userEmail=<EMAIL>, sessionId=65e970a7-e395-4475-821a-7440618a868b, thread=nioEventLoopGroup-3-4, requestId=4da256f4-ab84-40af-b2a6-264c93eef33f - Error loading templates metadata in plugin google-sheets-plugin
[2024-04-09 02:29:25,902] userEmail=<EMAIL>, sessionId=65e970a7-e395-4475-821a-7440618a868b, thread=nioEventLoopGroup-3-4, requestId=4da256f4-ab84-40af-b2a6-264c93eef33f - Error loading templates metadata in plugin js-plugin
[2024-04-09 02:29:25,906] userEmail=<EMAIL>, sessionId=65e970a7-e395-4475-821a-7440618a868b, thread=nioEventLoopGroup-3-4, requestId=4da256f4-ab84-40af-b2a6-264c93eef33f - Error loading templates metadata in plugin smtp-plugin
[2024-04-09 02:29:25,910] userEmail=<EMAIL>, sessionId=65e970a7-e395-4475-821a-7440618a868b, thread=nioEventLoopGroup-3-4, requestId=4da256f4-ab84-40af-b2a6-264c93eef33f - Error loading templates metadata in plugin graphql-plugin
[2024-04-09 02:29:25,918] userEmail=<EMAIL>, sessionId=65e970a7-e395-4475-821a-7440618a868b, thread=nioEventLoopGroup-3-4, requestId=4da256f4-ab84-40af-b2a6-264c93eef33f - Error loading templates metadata in plugin openai-plugin
[2024-04-09 02:29:25,924] userEmail=<EMAIL>, sessionId=65e970a7-e395-4475-821a-7440618a868b, thread=nioEventLoopGroup-3-4, requestId=4da256f4-ab84-40af-b2a6-264c93eef33f - Error loading templates metadata in plugin anthropic-plugin
[2024-04-09 02:29:25,928] userEmail=<EMAIL>, sessionId=65e970a7-e395-4475-821a-7440618a868b, thread=nioEventLoopGroup-3-4, requestId=4da256f4-ab84-40af-b2a6-264c93eef33f - Error loading templates metadata in plugin googleai-plugin
[2024-04-09 02:29:25,932] userEmail=<EMAIL>, sessionId=65e970a7-e395-4475-821a-7440618a868b, thread=nioEventLoopGroup-3-4, requestId=4da256f4-ab84-40af-b2a6-264c93eef33f - Error loading templates metadata in plugin databricks-plugin
[2024-04-09 02:29:25,937] userEmail=<EMAIL>, sessionId=65e970a7-e395-4475-821a-7440618a868b, thread=nioEventLoopGroup-3-4, requestId=4da256f4-ab84-40af-b2a6-264c93eef33f - Error loading templates metadata in plugin aws-lambda-plugin
[2024-04-09 02:29:25,940] userEmail=<EMAIL>, sessionId=65e970a7-e395-4475-821a-7440618a868b, thread=nioEventLoopGroup-3-4, requestId=4da256f4-ab84-40af-b2a6-264c93eef33f - Error loading templates metadata in plugin appsmithai-plugin
[2024-04-09 02:29:25,957] userEmail=<EMAIL>, sessionId=65e970a7-e395-4475-821a-7440618a868b, thread=nioEventLoopGroup-3-4, requestId=4da256f4-ab84-40af-b2a6-264c93eef33f - Error loading templates metadata in plugin saas-plugin
[2024-04-09 02:29:25,962] userEmail=<EMAIL>, sessionId=65e970a7-e395-4475-821a-7440618a868b, thread=nioEventLoopGroup-3-4, requestId=4da256f4-ab84-40af-b2a6-264c93eef33f - Error loading templates metadata in plugin saas-plugin
[2024-04-09 02:29:25,967] userEmail=<EMAIL>, sessionId=65e970a7-e395-4475-821a-7440618a868b, thread=nioEventLoopGroup-3-4, requestId=4da256f4-ab84-40af-b2a6-264c93eef33f - Error loading templates metadata in plugin saas-plugin
[2024-04-09 02:29:49,590] userEmail=<EMAIL>, sessionId=65e970a7-e395-4475-821a-7440618a868b, thread=lettuce-epollEventLoop-10-1, requestId=1900ab6e-2eba-48c1-8060-3d1b80594be2 - Going to fetch consolidatedAPI response for applicationId: null, defaultPageId: null, branchName: null, mode: PUBLISHED
[2024-04-09 02:29:50,548] userEmail=<EMAIL>, sessionId=65e970a7-e395-4475-821a-7440618a868b, thread=lettuce-epollEventLoop-10-1, requestId=e2e476a3-bde1-4945-a838-ef9a3d562082 - Going to get all applications by workspace id 6614a802cb467e382bcada24
[2024-04-09 02:30:09,463] userEmail=<EMAIL>, sessionId=65e970a7-e395-4475-821a-7440618a868b, thread=lettuce-epollEventLoop-10-1, requestId=9ca84451-b3c5-42a8-9648-d0793755534a - Going to fetch consolidatedAPI response for applicationId: null, defaultPageId: null, branchName: null, mode: PUBLISHED
[2024-04-09 02:30:10,258] userEmail=<EMAIL>, sessionId=65e970a7-e395-4475-821a-7440618a868b, thread=lettuce-epollEventLoop-10-1, requestId=3f22471c-5b35-4052-af99-014820db8bd7 - Going to get all applications by workspace id 6614a802cb467e382bcada24
[2024-04-09 02:30:35,059] userEmail=<EMAIL>, sessionId=65e970a7-e395-4475-821a-7440618a868b, thread=lettuce-epollEventLoop-10-1, requestId=265b420c-78d3-43b9-9e74-14791c0cf038 - Going to fetch consolidatedAPI response for applicationId: null, defaultPageId: null, branchName: null, mode: PUBLISHED
[2024-04-09 02:30:35,823] userEmail=<EMAIL>, sessionId=65e970a7-e395-4475-821a-7440618a868b, thread=lettuce-epollEventLoop-10-1, requestId=95a65e4b-b278-4346-abfc-9278187fc794 - Going to get all applications by workspace id 6614a802cb467e382bcada24
[2024-04-09 02:30:43,359] userEmail=<EMAIL>, sessionId=65e970a7-e395-4475-821a-7440618a868b, thread=lettuce-epollEventLoop-10-1, requestId=c9ecf40d-5feb-4bdb-9576-6afe981b691f - Going to fetch consolidatedAPI response for applicationId: null, defaultPageId: null, branchName: null, mode: PUBLISHED
[2024-04-09 02:30:44,156] userEmail=<EMAIL>, sessionId=65e970a7-e395-4475-821a-7440618a868b, thread=lettuce-epollEventLoop-10-1, requestId=30499aab-f360-455e-8e5e-237dd10fc5e8 - Going to get all applications by workspace id 6614a802cb467e382bcada24
[2024-04-09 02:31:08,816] userEmail=<EMAIL>, sessionId=65e970a7-e395-4475-821a-7440618a868b, thread=lettuce-epollEventLoop-10-1, requestId=ed87da74-4881-40ab-9b25-266a03d618c7 - Going to fetch consolidatedAPI response for applicationId: null, defaultPageId: null, branchName: null, mode: PUBLISHED
[2024-04-09 02:31:10,562] userEmail=<EMAIL>, sessionId=65e970a7-e395-4475-821a-7440618a868b, thread=lettuce-epollEventLoop-10-1, requestId=2438bb04-e030-40ef-9bfe-bde740f13f3e - Going to get all applications by workspace id 6614a802cb467e382bcada24
[2024-04-09 02:32:03,572] userEmail=<EMAIL>, sessionId=65e970a7-e395-4475-821a-7440618a868b, thread=reactor-http-epoll-3, requestId=e8979304-3e26-4651-a822-8f6d5e0d3dd5 - Going to update resource from base controller with id: 6614a802cb467e382bcada29
[2024-04-09 02:35:58,847] userEmail=<EMAIL>, sessionId=65e970a7-e395-4475-821a-7440618a868b, thread=lettuce-epollEventLoop-10-1, requestId=783c6c03-1f01-45de-bc2f-793c70a15213 - Going to fetch consolidatedAPI response for applicationId: null, defaultPageId: null, branchName: null, mode: PUBLISHED
[2024-04-09 02:35:59,959] userEmail=<EMAIL>, sessionId=65e970a7-e395-4475-821a-7440618a868b, thread=lettuce-epollEventLoop-10-1, requestId=0bc60ae5-d140-436e-9f0a-c35a2a128c83 - Going to get all applications by workspace id 6614a802cb467e382bcada24
[2024-04-09 02:36:08,453] userEmail=<EMAIL>, sessionId=65e970a7-e395-4475-821a-7440618a868b, thread=lettuce-epollEventLoop-10-1, requestId=662f33ab-44b1-43be-a18c-7d1a3fd95733 - Going to fetch consolidatedAPI response for applicationId: null, defaultPageId: null, branchName: null, mode: PUBLISHED
[2024-04-09 02:36:10,110] userEmail=<EMAIL>, sessionId=65e970a7-e395-4475-821a-7440618a868b, thread=lettuce-epollEventLoop-10-1, requestId=7436df82-f9f0-4348-8043-9083ebf1ee33 - Going to get all applications by workspace id 6614a802cb467e382bcada24
[2024-04-09 02:36:55,899] userEmail=<EMAIL>, sessionId=65e970a7-e395-4475-821a-7440618a868b, thread=lettuce-epollEventLoop-10-1, requestId=bd8fee9f-b377-4d80-910c-557556329d13 - Going to fetch consolidatedAPI response for applicationId: null, defaultPageId: null, branchName: null, mode: PUBLISHED
[2024-04-09 02:36:57,136] userEmail=<EMAIL>, sessionId=65e970a7-e395-4475-821a-7440618a868b, thread=lettuce-epollEventLoop-10-1, requestId=8c1acc83-2fde-498c-ae24-906bdc70bda5 - Going to get all applications by workspace id 6614a802cb467e382bcada24
[2024-04-09 02:37:03,447] userEmail=<EMAIL>, sessionId=65e970a7-e395-4475-821a-7440618a868b, thread=lettuce-epollEventLoop-10-1, requestId=b3c892f6-2702-4043-8ea3-a0188c5739da - Going to fetch consolidatedAPI response for applicationId: null, defaultPageId: null, branchName: null, mode: PUBLISHED
[2024-04-09 02:37:04,500] userEmail=<EMAIL>, sessionId=65e970a7-e395-4475-821a-7440618a868b, thread=lettuce-epollEventLoop-10-1, requestId=6487a2a8-acd5-431d-9c29-260ed3d8cfa7 - Going to get all applications by workspace id 6614a802cb467e382bcada24
[2024-04-09 02:38:19,797] userEmail=<EMAIL>, sessionId=65e970a7-e395-4475-821a-7440618a868b, thread=lettuce-epollEventLoop-10-1, requestId=5ee84cce-47b5-401c-a5b0-58a3d730e23b - Going to fetch consolidatedAPI response for applicationId: null, defaultPageId: null, branchName: null, mode: PUBLISHED
[2024-04-09 02:38:20,813] userEmail=<EMAIL>, sessionId=65e970a7-e395-4475-821a-7440618a868b, thread=lettuce-epollEventLoop-10-1, requestId=e36fedf3-38b0-40c8-b14d-7ec721da3a39 - Going to get all applications by workspace id 6614a802cb467e382bcada24
[2024-04-09 02:39:19,606] userEmail=anonymousUser, sessionId=, thread=reactor-http-epoll-3, requestId=bb2c5c93-0873-4b8c-b183-c43e2e2647eb - Going to fetch consolidatedAPI response for applicationId: null, defaultPageId: null, branchName: null, mode: PUBLISHED
[2024-04-09 02:39:20,500]  - In the logout success handler
[2024-04-09 02:39:39,067]  - Can't <NAME_EMAIL>
[2024-04-09 02:39:39,071]  - In the login failure handler. Cause: Unable to find username: <EMAIL>
org.springframework.security.core.userdetails.UsernameNotFoundException: Unable to find username: <EMAIL>
	at com.appsmith.server.authentication.handlers.ce.CustomFormLoginServiceCEImpl.findByUsername(CustomFormLoginServiceCEImpl.java:36)
	at org.springframework.security.authentication.UserDetailsRepositoryReactiveAuthenticationManager.retrieveUser(UserDetailsRepositoryReactiveAuthenticationManager.java:45)
	at org.springframework.security.authentication.AbstractUserDetailsReactiveAuthenticationManager.authenticate(AbstractUserDetailsReactiveAuthenticationManager.java:98)
	at org.springframework.security.authentication.ObservationReactiveAuthenticationManager.lambda$authenticate$3(ObservationReactiveAuthenticationManager.java:55)
	at reactor.core.publisher.MonoDeferContextual.subscribe(MonoDeferContextual.java:47)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:64)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:165)
	at com.appsmith.server.configurations.MDCConfig$MdcContextLifter.onNext(MDCConfig.java:53)
	at reactor.core.publisher.FluxHide$SuppressFuseableSubscriber.onNext(FluxHide.java:137)
	at reactor.core.publisher.Operators$ScalarSubscription.request(Operators.java:2545)
	at reactor.core.publisher.FluxHide$SuppressFuseableSubscriber.request(FluxHide.java:152)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.request(MonoFlatMap.java:194)
	at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.set(Operators.java:2341)
	at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.onSubscribe(Operators.java:2215)
	at com.appsmith.server.configurations.MDCConfig$MdcContextLifter.onSubscribe(MDCConfig.java:47)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onSubscribe(MonoFlatMap.java:117)
	at com.appsmith.server.configurations.MDCConfig$MdcContextLifter.onSubscribe(MDCConfig.java:47)
	at reactor.core.publisher.FluxHide$SuppressFuseableSubscriber.onSubscribe(FluxHide.java:122)
	at reactor.core.publisher.MonoJust.subscribe(MonoJust.java:55)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:64)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:165)
	at com.appsmith.server.configurations.MDCConfig$MdcContextLifter.onNext(MDCConfig.java:53)
	at reactor.core.publisher.FluxSwitchIfEmpty$SwitchIfEmptySubscriber.onNext(FluxSwitchIfEmpty.java:74)
	at com.appsmith.server.configurations.MDCConfig$MdcContextLifter.onNext(MDCConfig.java:53)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.secondComplete(MonoFlatMap.java:245)
	at reactor.core.publisher.MonoFlatMap$FlatMapInner.onNext(MonoFlatMap.java:305)
	at com.appsmith.server.configurations.MDCConfig$MdcContextLifter.onNext(MDCConfig.java:53)
	at reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:122)
	at com.appsmith.server.configurations.MDCConfig$MdcContextLifter.onNext(MDCConfig.java:53)
	at reactor.core.publisher.Operators$MonoSubscriber.complete(Operators.java:1839)
	at reactor.core.publisher.MonoCacheTime.subscribeOrReturn(MonoCacheTime.java:151)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:57)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:165)
	at com.appsmith.server.configurations.MDCConfig$MdcContextLifter.onNext(MDCConfig.java:53)
	at reactor.core.publisher.FluxFilter$FilterSubscriber.onNext(FluxFilter.java:113)
	at com.appsmith.server.configurations.MDCConfig$MdcContextLifter.onNext(MDCConfig.java:53)
	at reactor.core.publisher.FluxPeek$PeekSubscriber.onNext(FluxPeek.java:200)
	at com.appsmith.server.configurations.MDCConfig$MdcContextLifter.onNext(MDCConfig.java:53)
	at reactor.core.publisher.FluxSwitchIfEmpty$SwitchIfEmptySubscriber.onNext(FluxSwitchIfEmpty.java:74)
	at com.appsmith.server.configurations.MDCConfig$MdcContextLifter.onNext(MDCConfig.java:53)
	at reactor.core.publisher.MonoNext$NextSubscriber.onNext(MonoNext.java:82)
	at com.appsmith.server.configurations.MDCConfig$MdcContextLifter.onNext(MDCConfig.java:53)
	at reactor.core.publisher.FluxFilter$FilterSubscriber.onNext(FluxFilter.java:113)
	at com.appsmith.server.configurations.MDCConfig$MdcContextLifter.onNext(MDCConfig.java:53)
	at reactor.core.publisher.FluxFlatMap$FlatMapMain.drainLoop(FluxFlatMap.java:713)
	at reactor.core.publisher.FluxFlatMap$FlatMapMain.drain(FluxFlatMap.java:589)
	at reactor.core.publisher.FluxFlatMap$FlatMapInner.onSubscribe(FluxFlatMap.java:956)
	at com.appsmith.server.configurations.MDCConfig$MdcContextLifter.onSubscribe(MDCConfig.java:47)
	at reactor.core.publisher.MonoJust.subscribe(MonoJust.java:55)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4495)
	at reactor.core.publisher.FluxFlatMap$FlatMapMain.onNext(FluxFlatMap.java:427)
	at com.appsmith.server.configurations.MDCConfig$MdcContextLifter.onNext(MDCConfig.java:53)
	at reactor.core.publisher.FluxPeekFuseable$PeekFuseableSubscriber.onNext(FluxPeekFuseable.java:210)
	at com.appsmith.server.configurations.MDCConfig$MdcContextLifter.onNext(MDCConfig.java:53)
	at reactor.core.publisher.FluxHide$SuppressFuseableSubscriber.onNext(FluxHide.java:137)
	at reactor.core.publisher.FluxIterable$IterableSubscription.slowPath(FluxIterable.java:335)
	at reactor.core.publisher.FluxIterable$IterableSubscription.request(FluxIterable.java:294)
	at reactor.core.publisher.FluxHide$SuppressFuseableSubscriber.request(FluxHide.java:152)
	at reactor.core.publisher.FluxPeekFuseable$PeekFuseableSubscriber.request(FluxPeekFuseable.java:144)
	at reactor.core.publisher.FluxFlatMap$FlatMapMain.onSubscribe(FluxFlatMap.java:371)
	at com.appsmith.server.configurations.MDCConfig$MdcContextLifter.onSubscribe(MDCConfig.java:47)
	at reactor.core.publisher.FluxPeekFuseable$PeekFuseableSubscriber.onSubscribe(FluxPeekFuseable.java:178)
	at com.appsmith.server.configurations.MDCConfig$MdcContextLifter.onSubscribe(MDCConfig.java:47)
	at reactor.core.publisher.FluxHide$SuppressFuseableSubscriber.onSubscribe(FluxHide.java:122)
	at reactor.core.publisher.FluxIterable.subscribe(FluxIterable.java:201)
	at reactor.core.publisher.FluxIterable.subscribe(FluxIterable.java:83)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:64)
	at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:64)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:165)
	at com.appsmith.server.configurations.MDCConfig$MdcContextLifter.onNext(MDCConfig.java:53)
	at reactor.core.publisher.MonoSubscribeOn$SubscribeOnSubscriber.onNext(MonoSubscribeOn.java:146)
	at com.appsmith.server.configurations.MDCConfig$MdcContextLifter.onNext(MDCConfig.java:53)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onNext(FluxMapFuseable.java:129)
	at com.appsmith.server.configurations.MDCConfig$MdcContextLifter.onNext(MDCConfig.java:53)
	at reactor.core.publisher.FluxHide$SuppressFuseableSubscriber.onNext(FluxHide.java:137)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.secondComplete(MonoFlatMap.java:245)
	at reactor.core.publisher.MonoFlatMap$FlatMapInner.onNext(MonoFlatMap.java:305)
	at com.appsmith.server.configurations.MDCConfig$MdcContextLifter.onNext(MDCConfig.java:53)
	at reactor.core.publisher.Operators$ScalarSubscription.request(Operators.java:2545)
	at reactor.core.publisher.MonoFlatMap$FlatMapInner.onSubscribe(MonoFlatMap.java:291)
	at com.appsmith.server.configurations.MDCConfig$MdcContextLifter.onSubscribe(MDCConfig.java:47)
	at reactor.core.publisher.MonoJust.subscribe(MonoJust.java:55)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:64)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:165)
	at com.appsmith.server.configurations.MDCConfig$MdcContextLifter.onNext(MDCConfig.java:53)
	at reactor.core.publisher.MonoSubscribeOn$SubscribeOnSubscriber.onNext(MonoSubscribeOn.java:146)
	at com.appsmith.server.configurations.MDCConfig$MdcContextLifter.onNext(MDCConfig.java:53)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.secondComplete(MonoFlatMap.java:245)
	at reactor.core.publisher.MonoFlatMap$FlatMapInner.onNext(MonoFlatMap.java:305)
	at com.appsmith.server.configurations.MDCConfig$MdcContextLifter.onNext(MDCConfig.java:53)
	at reactor.core.publisher.Operators$ScalarSubscription.request(Operators.java:2545)
	at reactor.core.publisher.MonoFlatMap$FlatMapInner.onSubscribe(MonoFlatMap.java:291)
	at com.appsmith.server.configurations.MDCConfig$MdcContextLifter.onSubscribe(MDCConfig.java:47)
	at reactor.core.publisher.MonoJust.subscribe(MonoJust.java:55)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:64)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:165)
	at com.appsmith.server.configurations.MDCConfig$MdcContextLifter.onNext(MDCConfig.java:53)
	at reactor.core.publisher.FluxHide$SuppressFuseableSubscriber.onNext(FluxHide.java:137)
	at reactor.core.publisher.Operators$ScalarSubscription.request(Operators.java:2545)
	at reactor.core.publisher.FluxHide$SuppressFuseableSubscriber.request(FluxHide.java:152)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.request(MonoFlatMap.java:194)
	at reactor.core.publisher.MonoSubscribeOn$SubscribeOnSubscriber.trySchedule(MonoSubscribeOn.java:189)
	at reactor.core.publisher.MonoSubscribeOn$SubscribeOnSubscriber.onSubscribe(MonoSubscribeOn.java:134)
	at com.appsmith.server.configurations.MDCConfig$MdcContextLifter.onSubscribe(MDCConfig.java:47)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onSubscribe(MonoFlatMap.java:117)
	at com.appsmith.server.configurations.MDCConfig$MdcContextLifter.onSubscribe(MDCConfig.java:47)
	at reactor.core.publisher.FluxHide$SuppressFuseableSubscriber.onSubscribe(FluxHide.java:122)
	at reactor.core.publisher.MonoJust.subscribe(MonoJust.java:55)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4495)
	at reactor.core.publisher.MonoSubscribeOn$SubscribeOnSubscriber.run(MonoSubscribeOn.java:126)
	at io.sentry.spring.jakarta.webflux.SentryScheduleHook.lambda$apply$0(SentryScheduleHook.java:22)
	at reactor.core.scheduler.WorkerTask.call(WorkerTask.java:84)
	at reactor.core.scheduler.WorkerTask.call(WorkerTask.java:37)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
[2024-04-09 02:39:39,441] userEmail=anonymousUser, sessionId=, thread=reactor-http-epoll-3, requestId=060d6e5c-e10f-4029-be5c-49fd2b3e97da - Going to fetch consolidatedAPI response for applicationId: null, defaultPageId: null, branchName: null, mode: PUBLISHED
[2024-04-09 02:39:54,011]  - Can't <NAME_EMAIL>
[2024-04-09 02:39:54,012]  - In the login failure handler. Cause: Unable to find username: <EMAIL>
org.springframework.security.core.userdetails.UsernameNotFoundException: Unable to find username: <EMAIL>
	at com.appsmith.server.authentication.handlers.ce.CustomFormLoginServiceCEImpl.findByUsername(CustomFormLoginServiceCEImpl.java:36)
	at org.springframework.security.authentication.UserDetailsRepositoryReactiveAuthenticationManager.retrieveUser(UserDetailsRepositoryReactiveAuthenticationManager.java:45)
	at org.springframework.security.authentication.AbstractUserDetailsReactiveAuthenticationManager.authenticate(AbstractUserDetailsReactiveAuthenticationManager.java:98)
	at org.springframework.security.authentication.ObservationReactiveAuthenticationManager.lambda$authenticate$3(ObservationReactiveAuthenticationManager.java:55)
	at reactor.core.publisher.MonoDeferContextual.subscribe(MonoDeferContextual.java:47)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:64)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:165)
	at com.appsmith.server.configurations.MDCConfig$MdcContextLifter.onNext(MDCConfig.java:53)
	at reactor.core.publisher.FluxHide$SuppressFuseableSubscriber.onNext(FluxHide.java:137)
	at reactor.core.publisher.Operators$ScalarSubscription.request(Operators.java:2545)
	at reactor.core.publisher.FluxHide$SuppressFuseableSubscriber.request(FluxHide.java:152)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.request(MonoFlatMap.java:194)
	at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.set(Operators.java:2341)
	at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.onSubscribe(Operators.java:2215)
	at com.appsmith.server.configurations.MDCConfig$MdcContextLifter.onSubscribe(MDCConfig.java:47)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onSubscribe(MonoFlatMap.java:117)
	at com.appsmith.server.configurations.MDCConfig$MdcContextLifter.onSubscribe(MDCConfig.java:47)
	at reactor.core.publisher.FluxHide$SuppressFuseableSubscriber.onSubscribe(FluxHide.java:122)
	at reactor.core.publisher.MonoJust.subscribe(MonoJust.java:55)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:64)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:165)
	at com.appsmith.server.configurations.MDCConfig$MdcContextLifter.onNext(MDCConfig.java:53)
	at reactor.core.publisher.FluxSwitchIfEmpty$SwitchIfEmptySubscriber.onNext(FluxSwitchIfEmpty.java:74)
	at com.appsmith.server.configurations.MDCConfig$MdcContextLifter.onNext(MDCConfig.java:53)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.secondComplete(MonoFlatMap.java:245)
	at reactor.core.publisher.MonoFlatMap$FlatMapInner.onNext(MonoFlatMap.java:305)
	at com.appsmith.server.configurations.MDCConfig$MdcContextLifter.onNext(MDCConfig.java:53)
	at reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:122)
	at com.appsmith.server.configurations.MDCConfig$MdcContextLifter.onNext(MDCConfig.java:53)
	at reactor.core.publisher.Operators$MonoSubscriber.complete(Operators.java:1839)
	at reactor.core.publisher.MonoCacheTime.subscribeOrReturn(MonoCacheTime.java:151)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:57)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:165)
	at com.appsmith.server.configurations.MDCConfig$MdcContextLifter.onNext(MDCConfig.java:53)
	at reactor.core.publisher.FluxFilter$FilterSubscriber.onNext(FluxFilter.java:113)
	at com.appsmith.server.configurations.MDCConfig$MdcContextLifter.onNext(MDCConfig.java:53)
	at reactor.core.publisher.FluxPeek$PeekSubscriber.onNext(FluxPeek.java:200)
	at com.appsmith.server.configurations.MDCConfig$MdcContextLifter.onNext(MDCConfig.java:53)
	at reactor.core.publisher.FluxSwitchIfEmpty$SwitchIfEmptySubscriber.onNext(FluxSwitchIfEmpty.java:74)
	at com.appsmith.server.configurations.MDCConfig$MdcContextLifter.onNext(MDCConfig.java:53)
	at reactor.core.publisher.MonoNext$NextSubscriber.onNext(MonoNext.java:82)
	at com.appsmith.server.configurations.MDCConfig$MdcContextLifter.onNext(MDCConfig.java:53)
	at reactor.core.publisher.FluxFilter$FilterSubscriber.onNext(FluxFilter.java:113)
	at com.appsmith.server.configurations.MDCConfig$MdcContextLifter.onNext(MDCConfig.java:53)
	at reactor.core.publisher.FluxFlatMap$FlatMapMain.drainLoop(FluxFlatMap.java:713)
	at reactor.core.publisher.FluxFlatMap$FlatMapMain.drain(FluxFlatMap.java:589)
	at reactor.core.publisher.FluxFlatMap$FlatMapInner.onSubscribe(FluxFlatMap.java:956)
	at com.appsmith.server.configurations.MDCConfig$MdcContextLifter.onSubscribe(MDCConfig.java:47)
	at reactor.core.publisher.MonoJust.subscribe(MonoJust.java:55)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4495)
	at reactor.core.publisher.FluxFlatMap$FlatMapMain.onNext(FluxFlatMap.java:427)
	at com.appsmith.server.configurations.MDCConfig$MdcContextLifter.onNext(MDCConfig.java:53)
	at reactor.core.publisher.FluxPeekFuseable$PeekFuseableSubscriber.onNext(FluxPeekFuseable.java:210)
	at com.appsmith.server.configurations.MDCConfig$MdcContextLifter.onNext(MDCConfig.java:53)
	at reactor.core.publisher.FluxHide$SuppressFuseableSubscriber.onNext(FluxHide.java:137)
	at reactor.core.publisher.FluxIterable$IterableSubscription.slowPath(FluxIterable.java:335)
	at reactor.core.publisher.FluxIterable$IterableSubscription.request(FluxIterable.java:294)
	at reactor.core.publisher.FluxHide$SuppressFuseableSubscriber.request(FluxHide.java:152)
	at reactor.core.publisher.FluxPeekFuseable$PeekFuseableSubscriber.request(FluxPeekFuseable.java:144)
	at reactor.core.publisher.FluxFlatMap$FlatMapMain.onSubscribe(FluxFlatMap.java:371)
	at com.appsmith.server.configurations.MDCConfig$MdcContextLifter.onSubscribe(MDCConfig.java:47)
	at reactor.core.publisher.FluxPeekFuseable$PeekFuseableSubscriber.onSubscribe(FluxPeekFuseable.java:178)
	at com.appsmith.server.configurations.MDCConfig$MdcContextLifter.onSubscribe(MDCConfig.java:47)
	at reactor.core.publisher.FluxHide$SuppressFuseableSubscriber.onSubscribe(FluxHide.java:122)
	at reactor.core.publisher.FluxIterable.subscribe(FluxIterable.java:201)
	at reactor.core.publisher.FluxIterable.subscribe(FluxIterable.java:83)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:64)
	at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:64)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:165)
	at com.appsmith.server.configurations.MDCConfig$MdcContextLifter.onNext(MDCConfig.java:53)
	at reactor.core.publisher.MonoSubscribeOn$SubscribeOnSubscriber.onNext(MonoSubscribeOn.java:146)
	at com.appsmith.server.configurations.MDCConfig$MdcContextLifter.onNext(MDCConfig.java:53)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onNext(FluxMapFuseable.java:129)
	at com.appsmith.server.configurations.MDCConfig$MdcContextLifter.onNext(MDCConfig.java:53)
	at reactor.core.publisher.FluxHide$SuppressFuseableSubscriber.onNext(FluxHide.java:137)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.secondComplete(MonoFlatMap.java:245)
	at reactor.core.publisher.MonoFlatMap$FlatMapInner.onNext(MonoFlatMap.java:305)
	at com.appsmith.server.configurations.MDCConfig$MdcContextLifter.onNext(MDCConfig.java:53)
	at reactor.core.publisher.Operators$ScalarSubscription.request(Operators.java:2545)
	at reactor.core.publisher.MonoFlatMap$FlatMapInner.onSubscribe(MonoFlatMap.java:291)
	at com.appsmith.server.configurations.MDCConfig$MdcContextLifter.onSubscribe(MDCConfig.java:47)
	at reactor.core.publisher.MonoJust.subscribe(MonoJust.java:55)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:64)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:165)
	at com.appsmith.server.configurations.MDCConfig$MdcContextLifter.onNext(MDCConfig.java:53)
	at reactor.core.publisher.MonoSubscribeOn$SubscribeOnSubscriber.onNext(MonoSubscribeOn.java:146)
	at com.appsmith.server.configurations.MDCConfig$MdcContextLifter.onNext(MDCConfig.java:53)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.secondComplete(MonoFlatMap.java:245)
	at reactor.core.publisher.MonoFlatMap$FlatMapInner.onNext(MonoFlatMap.java:305)
	at com.appsmith.server.configurations.MDCConfig$MdcContextLifter.onNext(MDCConfig.java:53)
	at reactor.core.publisher.Operators$ScalarSubscription.request(Operators.java:2545)
	at reactor.core.publisher.MonoFlatMap$FlatMapInner.onSubscribe(MonoFlatMap.java:291)
	at com.appsmith.server.configurations.MDCConfig$MdcContextLifter.onSubscribe(MDCConfig.java:47)
	at reactor.core.publisher.MonoJust.subscribe(MonoJust.java:55)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:64)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:165)
	at com.appsmith.server.configurations.MDCConfig$MdcContextLifter.onNext(MDCConfig.java:53)
	at reactor.core.publisher.FluxHide$SuppressFuseableSubscriber.onNext(FluxHide.java:137)
	at reactor.core.publisher.Operators$ScalarSubscription.request(Operators.java:2545)
	at reactor.core.publisher.FluxHide$SuppressFuseableSubscriber.request(FluxHide.java:152)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.request(MonoFlatMap.java:194)
	at reactor.core.publisher.MonoSubscribeOn$SubscribeOnSubscriber.trySchedule(MonoSubscribeOn.java:189)
	at reactor.core.publisher.MonoSubscribeOn$SubscribeOnSubscriber.onSubscribe(MonoSubscribeOn.java:134)
	at com.appsmith.server.configurations.MDCConfig$MdcContextLifter.onSubscribe(MDCConfig.java:47)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onSubscribe(MonoFlatMap.java:117)
	at com.appsmith.server.configurations.MDCConfig$MdcContextLifter.onSubscribe(MDCConfig.java:47)
	at reactor.core.publisher.FluxHide$SuppressFuseableSubscriber.onSubscribe(FluxHide.java:122)
	at reactor.core.publisher.MonoJust.subscribe(MonoJust.java:55)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4495)
	at reactor.core.publisher.MonoSubscribeOn$SubscribeOnSubscriber.run(MonoSubscribeOn.java:126)
	at io.sentry.spring.jakarta.webflux.SentryScheduleHook.lambda$apply$0(SentryScheduleHook.java:22)
	at reactor.core.scheduler.WorkerTask.call(WorkerTask.java:84)
	at reactor.core.scheduler.WorkerTask.call(WorkerTask.java:37)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
[2024-04-09 02:39:54,271] userEmail=anonymousUser, sessionId=, thread=reactor-http-epoll-3, requestId=11127a81-e960-4779-854f-c9d382703b6b - Going to fetch consolidatedAPI response for applicationId: null, defaultPageId: null, branchName: null, mode: PUBLISHED
[2024-04-09 02:40:13,725] userEmail=<EMAIL>, sessionId=65e970a7-e395-4475-821a-7440618a868b, thread=lettuce-epollEventLoop-10-1, requestId=5680e28c-d080-41ed-b628-6e855c759081 - Going to fetch consolidatedAPI response for applicationId: null, defaultPageId: null, branchName: null, mode: PUBLISHED
[2024-04-09 02:40:15,514] userEmail=<EMAIL>, sessionId=65e970a7-e395-4475-821a-7440618a868b, thread=lettuce-epollEventLoop-10-1, requestId=4c01845f-388a-4389-8dd7-963d352e113e - Going to get all applications by workspace id 6614a802cb467e382bcada24
[2024-04-09 02:40:25,834] userEmail=<EMAIL>, sessionId=65e970a7-e395-4475-821a-7440618a868b, thread=lettuce-epollEventLoop-10-1, requestId=b9458ed2-59d5-4827-b79b-e9bfb6c3c0c3 - Going to delete application with id: 6614a802cb467e382bcada29
[2024-04-09 02:40:25,837] userEmail=<EMAIL>, sessionId=65e970a7-e395-4475-821a-7440618a868b, thread=lettuce-epollEventLoop-10-1, requestId=b9458ed2-59d5-4827-b79b-e9bfb6c3c0c3 - Archiving application with id: 6614a802cb467e382bcada29
[2024-04-09 02:40:25,855] userEmail=<EMAIL>, sessionId=65e970a7-e395-4475-821a-7440618a868b, thread=nioEventLoopGroup-3-4, requestId=b9458ed2-59d5-4827-b79b-e9bfb6c3c0c3 - Archiving application with id: 6614a802cb467e382bcada29
[2024-04-09 02:40:25,855] userEmail=<EMAIL>, sessionId=65e970a7-e395-4475-821a-7440618a868b, thread=nioEventLoopGroup-3-4, requestId=b9458ed2-59d5-4827-b79b-e9bfb6c3c0c3 - Archiving actionCollections, actions, pages and themes for applicationId: 6614a802cb467e382bcada29
[2024-04-09 02:40:59,518] userEmail=anonymousUser, sessionId=, thread=reactor-http-epoll-3, requestId=abf4a892-80af-4916-be2b-7e88517ab4ee - Going to fetch consolidatedAPI response for applicationId: null, defaultPageId: null, branchName: null, mode: PUBLISHED
[2024-04-09 02:41:01,733]  - In the logout success handler
[2024-04-09 02:41:18,466]  - Can't <NAME_EMAIL>
[2024-04-09 02:41:18,467]  - In the login failure handler. Cause: Unable to find username: <EMAIL>
org.springframework.security.core.userdetails.UsernameNotFoundException: Unable to find username: <EMAIL>
	at com.appsmith.server.authentication.handlers.ce.CustomFormLoginServiceCEImpl.findByUsername(CustomFormLoginServiceCEImpl.java:36)
	at org.springframework.security.authentication.UserDetailsRepositoryReactiveAuthenticationManager.retrieveUser(UserDetailsRepositoryReactiveAuthenticationManager.java:45)
	at org.springframework.security.authentication.AbstractUserDetailsReactiveAuthenticationManager.authenticate(AbstractUserDetailsReactiveAuthenticationManager.java:98)
	at org.springframework.security.authentication.ObservationReactiveAuthenticationManager.lambda$authenticate$3(ObservationReactiveAuthenticationManager.java:55)
	at reactor.core.publisher.MonoDeferContextual.subscribe(MonoDeferContextual.java:47)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:64)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:165)
	at com.appsmith.server.configurations.MDCConfig$MdcContextLifter.onNext(MDCConfig.java:53)
	at reactor.core.publisher.FluxHide$SuppressFuseableSubscriber.onNext(FluxHide.java:137)
	at reactor.core.publisher.Operators$ScalarSubscription.request(Operators.java:2545)
	at reactor.core.publisher.FluxHide$SuppressFuseableSubscriber.request(FluxHide.java:152)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.request(MonoFlatMap.java:194)
	at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.set(Operators.java:2341)
	at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.onSubscribe(Operators.java:2215)
	at com.appsmith.server.configurations.MDCConfig$MdcContextLifter.onSubscribe(MDCConfig.java:47)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onSubscribe(MonoFlatMap.java:117)
	at com.appsmith.server.configurations.MDCConfig$MdcContextLifter.onSubscribe(MDCConfig.java:47)
	at reactor.core.publisher.FluxHide$SuppressFuseableSubscriber.onSubscribe(FluxHide.java:122)
	at reactor.core.publisher.MonoJust.subscribe(MonoJust.java:55)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:64)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:165)
	at com.appsmith.server.configurations.MDCConfig$MdcContextLifter.onNext(MDCConfig.java:53)
	at reactor.core.publisher.FluxSwitchIfEmpty$SwitchIfEmptySubscriber.onNext(FluxSwitchIfEmpty.java:74)
	at com.appsmith.server.configurations.MDCConfig$MdcContextLifter.onNext(MDCConfig.java:53)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.secondComplete(MonoFlatMap.java:245)
	at reactor.core.publisher.MonoFlatMap$FlatMapInner.onNext(MonoFlatMap.java:305)
	at com.appsmith.server.configurations.MDCConfig$MdcContextLifter.onNext(MDCConfig.java:53)
	at reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:122)
	at com.appsmith.server.configurations.MDCConfig$MdcContextLifter.onNext(MDCConfig.java:53)
	at reactor.core.publisher.Operators$MonoSubscriber.complete(Operators.java:1839)
	at reactor.core.publisher.MonoCacheTime.subscribeOrReturn(MonoCacheTime.java:151)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:57)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:165)
	at com.appsmith.server.configurations.MDCConfig$MdcContextLifter.onNext(MDCConfig.java:53)
	at reactor.core.publisher.FluxFilter$FilterSubscriber.onNext(FluxFilter.java:113)
	at com.appsmith.server.configurations.MDCConfig$MdcContextLifter.onNext(MDCConfig.java:53)
	at reactor.core.publisher.FluxPeek$PeekSubscriber.onNext(FluxPeek.java:200)
	at com.appsmith.server.configurations.MDCConfig$MdcContextLifter.onNext(MDCConfig.java:53)
	at reactor.core.publisher.FluxSwitchIfEmpty$SwitchIfEmptySubscriber.onNext(FluxSwitchIfEmpty.java:74)
	at com.appsmith.server.configurations.MDCConfig$MdcContextLifter.onNext(MDCConfig.java:53)
	at reactor.core.publisher.MonoNext$NextSubscriber.onNext(MonoNext.java:82)
	at com.appsmith.server.configurations.MDCConfig$MdcContextLifter.onNext(MDCConfig.java:53)
	at reactor.core.publisher.FluxFilter$FilterSubscriber.onNext(FluxFilter.java:113)
	at com.appsmith.server.configurations.MDCConfig$MdcContextLifter.onNext(MDCConfig.java:53)
	at reactor.core.publisher.FluxFlatMap$FlatMapMain.drainLoop(FluxFlatMap.java:713)
	at reactor.core.publisher.FluxFlatMap$FlatMapMain.drain(FluxFlatMap.java:589)
	at reactor.core.publisher.FluxFlatMap$FlatMapInner.onSubscribe(FluxFlatMap.java:956)
	at com.appsmith.server.configurations.MDCConfig$MdcContextLifter.onSubscribe(MDCConfig.java:47)
	at reactor.core.publisher.MonoJust.subscribe(MonoJust.java:55)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4495)
	at reactor.core.publisher.FluxFlatMap$FlatMapMain.onNext(FluxFlatMap.java:427)
	at com.appsmith.server.configurations.MDCConfig$MdcContextLifter.onNext(MDCConfig.java:53)
	at reactor.core.publisher.FluxPeekFuseable$PeekFuseableSubscriber.onNext(FluxPeekFuseable.java:210)
	at com.appsmith.server.configurations.MDCConfig$MdcContextLifter.onNext(MDCConfig.java:53)
	at reactor.core.publisher.FluxHide$SuppressFuseableSubscriber.onNext(FluxHide.java:137)
	at reactor.core.publisher.FluxIterable$IterableSubscription.slowPath(FluxIterable.java:335)
	at reactor.core.publisher.FluxIterable$IterableSubscription.request(FluxIterable.java:294)
	at reactor.core.publisher.FluxHide$SuppressFuseableSubscriber.request(FluxHide.java:152)
	at reactor.core.publisher.FluxPeekFuseable$PeekFuseableSubscriber.request(FluxPeekFuseable.java:144)
	at reactor.core.publisher.FluxFlatMap$FlatMapMain.onSubscribe(FluxFlatMap.java:371)
	at com.appsmith.server.configurations.MDCConfig$MdcContextLifter.onSubscribe(MDCConfig.java:47)
	at reactor.core.publisher.FluxPeekFuseable$PeekFuseableSubscriber.onSubscribe(FluxPeekFuseable.java:178)
	at com.appsmith.server.configurations.MDCConfig$MdcContextLifter.onSubscribe(MDCConfig.java:47)
	at reactor.core.publisher.FluxHide$SuppressFuseableSubscriber.onSubscribe(FluxHide.java:122)
	at reactor.core.publisher.FluxIterable.subscribe(FluxIterable.java:201)
	at reactor.core.publisher.FluxIterable.subscribe(FluxIterable.java:83)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:64)
	at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:64)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:165)
	at com.appsmith.server.configurations.MDCConfig$MdcContextLifter.onNext(MDCConfig.java:53)
	at reactor.core.publisher.MonoSubscribeOn$SubscribeOnSubscriber.onNext(MonoSubscribeOn.java:146)
	at com.appsmith.server.configurations.MDCConfig$MdcContextLifter.onNext(MDCConfig.java:53)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onNext(FluxMapFuseable.java:129)
	at com.appsmith.server.configurations.MDCConfig$MdcContextLifter.onNext(MDCConfig.java:53)
	at reactor.core.publisher.FluxHide$SuppressFuseableSubscriber.onNext(FluxHide.java:137)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.secondComplete(MonoFlatMap.java:245)
	at reactor.core.publisher.MonoFlatMap$FlatMapInner.onNext(MonoFlatMap.java:305)
	at com.appsmith.server.configurations.MDCConfig$MdcContextLifter.onNext(MDCConfig.java:53)
	at reactor.core.publisher.Operators$ScalarSubscription.request(Operators.java:2545)
	at reactor.core.publisher.MonoFlatMap$FlatMapInner.onSubscribe(MonoFlatMap.java:291)
	at com.appsmith.server.configurations.MDCConfig$MdcContextLifter.onSubscribe(MDCConfig.java:47)
	at reactor.core.publisher.MonoJust.subscribe(MonoJust.java:55)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:64)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:165)
	at com.appsmith.server.configurations.MDCConfig$MdcContextLifter.onNext(MDCConfig.java:53)
	at reactor.core.publisher.MonoSubscribeOn$SubscribeOnSubscriber.onNext(MonoSubscribeOn.java:146)
	at com.appsmith.server.configurations.MDCConfig$MdcContextLifter.onNext(MDCConfig.java:53)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.secondComplete(MonoFlatMap.java:245)
	at reactor.core.publisher.MonoFlatMap$FlatMapInner.onNext(MonoFlatMap.java:305)
	at com.appsmith.server.configurations.MDCConfig$MdcContextLifter.onNext(MDCConfig.java:53)
	at reactor.core.publisher.Operators$ScalarSubscription.request(Operators.java:2545)
	at reactor.core.publisher.MonoFlatMap$FlatMapInner.onSubscribe(MonoFlatMap.java:291)
	at com.appsmith.server.configurations.MDCConfig$MdcContextLifter.onSubscribe(MDCConfig.java:47)
	at reactor.core.publisher.MonoJust.subscribe(MonoJust.java:55)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:64)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:165)
	at com.appsmith.server.configurations.MDCConfig$MdcContextLifter.onNext(MDCConfig.java:53)
	at reactor.core.publisher.FluxHide$SuppressFuseableSubscriber.onNext(FluxHide.java:137)
	at reactor.core.publisher.Operators$ScalarSubscription.request(Operators.java:2545)
	at reactor.core.publisher.FluxHide$SuppressFuseableSubscriber.request(FluxHide.java:152)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.request(MonoFlatMap.java:194)
	at reactor.core.publisher.MonoSubscribeOn$SubscribeOnSubscriber.trySchedule(MonoSubscribeOn.java:189)
	at reactor.core.publisher.MonoSubscribeOn$SubscribeOnSubscriber.onSubscribe(MonoSubscribeOn.java:134)
	at com.appsmith.server.configurations.MDCConfig$MdcContextLifter.onSubscribe(MDCConfig.java:47)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onSubscribe(MonoFlatMap.java:117)
	at com.appsmith.server.configurations.MDCConfig$MdcContextLifter.onSubscribe(MDCConfig.java:47)
	at reactor.core.publisher.FluxHide$SuppressFuseableSubscriber.onSubscribe(FluxHide.java:122)
	at reactor.core.publisher.MonoJust.subscribe(MonoJust.java:55)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4495)
	at reactor.core.publisher.MonoSubscribeOn$SubscribeOnSubscriber.run(MonoSubscribeOn.java:126)
	at io.sentry.spring.jakarta.webflux.SentryScheduleHook.lambda$apply$0(SentryScheduleHook.java:22)
	at reactor.core.scheduler.WorkerTask.call(WorkerTask.java:84)
	at reactor.core.scheduler.WorkerTask.call(WorkerTask.java:37)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
[2024-04-09 02:41:18,982] userEmail=anonymousUser, sessionId=, thread=reactor-http-epoll-3, requestId=c6ee6175-6a9a-483a-87fa-a2d0abdd4c76 - Going to fetch consolidatedAPI response for applicationId: null, defaultPageId: null, branchName: null, mode: PUBLISHED
[2024-04-09 02:41:58,406] userEmail=anonymousUser, sessionId=, thread=lettuce-epollEventLoop-10-1, requestId=c6ed7be2-7fee-4eb6-a35c-8e41394245aa - Cache miss for key permissionGroupsForUser:1030741583@qq.com6614a751cb467e382bcada07
[2024-04-09 02:41:58,412] userEmail=anonymousUser, sessionId=, thread=nioEventLoopGroup-3-5, requestId=c6ed7be2-7fee-4eb6-a35c-8e41394245aa - Cache entry added for key permissionGroupsForUser:1030741583@qq.com6614a751cb467e382bcada07
[2024-04-09 02:41:58,419] userEmail=anonymousUser, sessionId=, thread=nioEventLoopGroup-3-5, requestId=c6ed7be2-7fee-4eb6-a35c-8e41394245aa - UserServiceCEImpl::Time taken for create user: 46 ms
[2024-04-09 02:41:58,536] userEmail=anonymousUser, sessionId=, thread=nioEventLoopGroup-3-4, requestId=c6ed7be2-7fee-4eb6-a35c-8e41394245aa - Cache entry evicted for key permissionGroupsForUser:1030741583@qq.com6614a751cb467e382bcada07
[2024-04-09 02:41:58,546] userEmail=anonymousUser, sessionId=, thread=nioEventLoopGroup-3-4, requestId=c6ed7be2-7fee-4eb6-a35c-8e41394245aa - UserServiceCEImpl::Time taken to create default workspace: 124 ms
[2024-04-09 02:41:58,546] userEmail=anonymousUser, sessionId=, thread=nioEventLoopGroup-3-4, requestId=c6ed7be2-7fee-4eb6-a35c-8e41394245aa - Created blank default workspace for user '<EMAIL>'.
[2024-04-09 02:41:58,550] userEmail=anonymousUser, sessionId=, thread=nioEventLoopGroup-3-4, requestId=c6ed7be2-7fee-4eb6-a35c-8e41394245aa - UserServiceCEImpl::Time taken to find created user: 177 ms
[2024-04-09 02:41:58,550] userEmail=anonymousUser, sessionId=, thread=nioEventLoopGroup-3-4, requestId=c6ed7be2-7fee-4eb6-a35c-8e41394245aa - UserSignupCEImpl::Time taken for create user and send email: 182 ms
[2024-04-09 02:41:58,551] userEmail=anonymousUser, sessionId=, thread=nioEventLoopGroup-3-4, requestId=c6ed7be2-7fee-4eb6-a35c-8e41394245aa - Login succeeded for user: User(name=null, email=<EMAIL>, hashedEmail=null, passwordResetInitiated=false, source=FORM, state=null, isEnabled=true, emailVerificationRequired=null, emailVerified=null, currentWorkspaceId=null, workspaceIds=null, examplesWorkspaceId=null, groupIds=[], permissions=[], inviteToken=null, isAnonymous=false, tenantId=6614a751cb467e382bcada07, isSystemGenerated=null)
[2024-04-09 02:41:58,575] userEmail=anonymousUser, sessionId=, thread=lettuce-epollEventLoop-10-1, requestId=c6ed7be2-7fee-4eb6-a35c-8e41394245aa - Cache miss for key permissionGroupsForUser:1030741583@qq.com6614a751cb467e382bcada07
[2024-04-09 02:41:58,589] userEmail=anonymousUser, sessionId=, thread=nioEventLoopGroup-3-4, requestId=c6ed7be2-7fee-4eb6-a35c-8e41394245aa - Cache entry added for key permissionGroupsForUser:1030741583@qq.com6614a751cb467e382bcada07
[2024-04-09 02:41:58,709] userEmail=anonymousUser, sessionId=, thread=nioEventLoopGroup-3-4, requestId=c6ed7be2-7fee-4eb6-a35c-8e41394245aa - Published application 6614aaf6cb467e382bcada34 in 68 ms
[2024-04-09 02:41:58,716] userEmail=anonymousUser, sessionId=, thread=nioEventLoopGroup-3-4, requestId=c6ed7be2-7fee-4eb6-a35c-8e41394245aa - UserSignupCEImpl::Time taken for authentication success: 165 ms
[2024-04-09 02:41:59,234] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=lettuce-epollEventLoop-10-1, requestId=d5f6b568-ff95-4989-b959-582c7b8d6252 - Going to fetch consolidatedAPI response for applicationId: null, defaultPageId: null, branchName: null, mode: PUBLISHED
[2024-04-09 02:41:59,245] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=lettuce-epollEventLoop-10-1, requestId=d5f6b568-ff95-4989-b959-582c7b8d6252 - Cache miss for key featureFlag:7a291c0bf4ce695224d0512ae7e0766d4e1ea5ca7eb9b517aee6b91d8355e0d8
[2024-04-09 02:42:01,443] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=reactor-http-epoll-3, requestId=d5f6b568-ff95-4989-b959-582c7b8d6252 - Cache entry added for key featureFlag:7a291c0bf4ce695224d0512ae7e0766d4e1ea5ca7eb9b517aee6b91d8355e0d8
[2024-04-09 02:42:10,853] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=lettuce-epollEventLoop-10-1, requestId=23a8e5ac-7a1f-4630-8390-c946922a4082 - Going to get all applications by workspace id 6614aaf6cb467e382bcada2f
[2024-04-09 02:42:10,922] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=lettuce-epollEventLoop-10-1, requestId=6bad2480-880f-457b-b174-742d85f2a112 - Going to get all resources from base controller {workspaceId=[6614aaf6cb467e382bcada2f]}
[2024-04-09 02:42:10,993] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=nioEventLoopGroup-3-4, requestId=6bad2480-880f-457b-b174-742d85f2a112 - Fetching plugins by params: {workspaceId=[6614aaf6cb467e382bcada2f]} for org: 1030741583's apps
[2024-04-09 02:42:15,893] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=lettuce-epollEventLoop-10-1, requestId=88af5de3-b54a-4e0e-9ee8-ccfd0a086501 - Going to fetch consolidatedAPI response for applicationId: null, defaultPageId: null, branchName: null, mode: PUBLISHED
[2024-04-09 02:42:17,196] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=lettuce-epollEventLoop-10-1, requestId=2b9d3daf-f45b-4fa9-a0f8-286b31ce4265 - Going to get all applications by workspace id 6614aaf6cb467e382bcada2f
[2024-04-09 02:42:23,003] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=lettuce-epollEventLoop-10-1, requestId=a3a88435-28cd-4658-a486-bace5144f6e3 - Going to fetch consolidatedAPI response for applicationId: null, defaultPageId: 6614aaf6cb467e382bcada37, branchName: null, mode: PUBLISHED
[2024-04-09 02:42:23,077] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=nioEventLoopGroup-3-4, requestId=a3a88435-28cd-4658-a486-bace5144f6e3 - Retrieved possible application ids for page, picking the appropriate one now
[2024-04-09 02:42:23,129] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=nioEventLoopGroup-3-5, requestId=a3a88435-28cd-4658-a486-bace5144f6e3 - Fetched application data for id: 6614aaf6cb467e382bcada34
[2024-04-09 02:42:23,252] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=nioEventLoopGroup-3-4, requestId=a3a88435-28cd-4658-a486-bace5144f6e3 - Retrieved Page DTOs from DB ...
[2024-04-09 02:42:23,256] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=nioEventLoopGroup-3-4, requestId=a3a88435-28cd-4658-a486-bace5144f6e3 - Populating applicationPagesDTO ...
[2024-04-09 02:42:28,510] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=lettuce-epollEventLoop-10-1, requestId=68f3c1ef-16bd-4bc1-b2d3-17911b291efb - Going to fetch consolidatedAPI response for applicationId: null, defaultPageId: null, branchName: null, mode: PUBLISHED
[2024-04-09 02:42:29,770] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=lettuce-epollEventLoop-10-1, requestId=9b72196c-ae91-43d3-8aab-389485dfd5f2 - Going to get all applications by workspace id 6614aaf6cb467e382bcada2f
[2024-04-09 02:42:31,822] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=lettuce-epollEventLoop-10-1, requestId=8a047f77-2934-44f0-a9a8-1ed3f54e9e02 - Going to fetch consolidatedAPI response for applicationId: null, defaultPageId: 6614aaf6cb467e382bcada37, branchName: null, mode: EDIT
[2024-04-09 02:42:31,876] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=nioEventLoopGroup-3-3, requestId=8a047f77-2934-44f0-a9a8-1ed3f54e9e02 - Retrieved possible application ids for page, picking the appropriate one now
[2024-04-09 02:42:31,895] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=nioEventLoopGroup-3-4, requestId=8a047f77-2934-44f0-a9a8-1ed3f54e9e02 - Fetched application data for id: 6614aaf6cb467e382bcada34
[2024-04-09 02:42:31,966] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=nioEventLoopGroup-3-6, requestId=8a047f77-2934-44f0-a9a8-1ed3f54e9e02 - Retrieved Page DTOs from DB ...
[2024-04-09 02:42:31,971] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=nioEventLoopGroup-3-6, requestId=8a047f77-2934-44f0-a9a8-1ed3f54e9e02 - Populating applicationPagesDTO ...
[2024-04-09 02:42:32,055] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=nioEventLoopGroup-3-6, requestId=8a047f77-2934-44f0-a9a8-1ed3f54e9e02 - Fetching plugins by params: {workspaceId=[6614aaf6cb467e382bcada2f]} for org: 1030741583's apps
[2024-04-09 02:42:33,075] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=lettuce-epollEventLoop-10-1, requestId=30c226e3-ed3c-4d82-a2fd-bf5dbd0cf181 - Going to get snapshot with application id: 6614aaf6cb467e382bcada34, branch: null
[2024-04-09 02:42:35,212] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=lettuce-epollEventLoop-10-1, requestId=568bb5eb-2d2d-47d0-aabb-e93ecc41d7c6 - Going to get resource from base controller for id: 6614aaf6cb467e382bcada2f
[2024-04-09 02:56:38,808]  - Fetching features for default tenant
[2024-04-09 03:01:09,028]  - Commencing graceful shutdown. Waiting for active requests to complete
[2024-04-09 03:01:09,039]  - Graceful shutdown complete
[2024-04-09 03:01:09,115]  - Operator called default onErrorDropped
reactor.core.Exceptions$ErrorCallbackNotImplemented: java.util.concurrent.CancellationException: Disconnected
Caused by: java.util.concurrent.CancellationException: Disconnected
	at reactor.core.publisher.FluxPublish$PublishSubscriber.disconnectAction(FluxPublish.java:327)
	at reactor.core.publisher.FluxPublish$PublishSubscriber.dispose(FluxPublish.java:318)
	at org.springframework.data.redis.connection.lettuce.LettuceReactiveSubscription$State.terminate(LettuceReactiveSubscription.java:271)
	at org.springframework.data.redis.connection.lettuce.LettuceReactiveSubscription.lambda$cancel$8(LettuceReactiveSubscription.java:149)
	at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:45)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:64)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:240)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.onComplete(MonoIgnoreThen.java:203)
	at com.appsmith.server.configurations.MDCConfig$MdcContextLifter.onComplete(MDCConfig.java:63)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.onComplete(MonoIgnoreThen.java:209)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:238)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.onComplete(MonoIgnoreThen.java:203)
	at com.appsmith.server.configurations.MDCConfig$MdcContextLifter.onComplete(MDCConfig.java:63)
	at com.appsmith.server.configurations.MDCConfig$MdcContextLifter.onComplete(MDCConfig.java:63)
	at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.onComplete(Operators.java:2205)
	at com.appsmith.server.configurations.MDCConfig$MdcContextLifter.onComplete(MDCConfig.java:63)
	at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.onComplete(MonoPeekTerminal.java:299)
	at com.appsmith.server.configurations.MDCConfig$MdcContextLifter.onComplete(MDCConfig.java:63)
	at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.onComplete(Operators.java:2205)
	at com.appsmith.server.configurations.MDCConfig$MdcContextLifter.onComplete(MDCConfig.java:63)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.secondComplete(MonoFlatMap.java:250)
	at reactor.core.publisher.MonoFlatMap$FlatMapInner.onComplete(MonoFlatMap.java:324)
	at com.appsmith.server.configurations.MDCConfig$MdcContextLifter.onComplete(MDCConfig.java:63)
	at reactor.core.publisher.MonoIgnoreElements$IgnoreElementsSubscriber.onComplete(MonoIgnoreElements.java:89)
	at com.appsmith.server.configurations.MDCConfig$MdcContextLifter.onComplete(MDCConfig.java:63)
	at io.lettuce.core.RedisPublisher$ImmediateSubscriber.onComplete(RedisPublisher.java:900)
	at io.lettuce.core.RedisPublisher$State.onAllDataRead(RedisPublisher.java:702)
	at io.lettuce.core.RedisPublisher$State$3.read(RedisPublisher.java:612)
	at io.lettuce.core.RedisPublisher$State$3.onDataAvailable(RedisPublisher.java:569)
	at io.lettuce.core.RedisPublisher$RedisSubscription.onDataAvailable(RedisPublisher.java:326)
	at io.lettuce.core.RedisPublisher$RedisSubscription.onAllDataRead(RedisPublisher.java:341)
	at io.lettuce.core.RedisPublisher$SubscriptionCommand.doOnComplete(RedisPublisher.java:782)
	at io.lettuce.core.protocol.CommandWrapper.complete(CommandWrapper.java:65)
	at io.lettuce.core.protocol.CommandWrapper.complete(CommandWrapper.java:63)
	at io.lettuce.core.protocol.CommandHandler.complete(CommandHandler.java:747)
	at io.lettuce.core.pubsub.PubSubCommandHandler.complete(PubSubCommandHandler.java:167)
	at io.lettuce.core.protocol.CommandHandler.decode(CommandHandler.java:682)
	at io.lettuce.core.pubsub.PubSubCommandHandler.decode(PubSubCommandHandler.java:112)
	at io.lettuce.core.protocol.CommandHandler.channelRead(CommandHandler.java:599)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
	at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:800)
	at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:499)
	at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:397)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)
Load environment configuration
Waiting for RTS to start ...
{"success":true}RTS started.
SLF4J: Class path contains multiple SLF4J providers.
SLF4J: Found provider [ch.qos.logback.classic.spi.LogbackServiceProvider@6659c656]
SLF4J: Found provider [org.slf4j.reload4j.Reload4jServiceProvider@6d5380c2]
SLF4J: See https://www.slf4j.org/codes.html#multiple_bindings for an explanation.
SLF4J: Actual provider is of type [ch.qos.logback.classic.spi.LogbackServiceProvider@6659c656]
[2024-04-09 03:11:25,575]  - Starting ServerApplication vv1.19 using Java 17.0.9 with PID 1508 (/opt/appsmith/backend/server.jar started by root in /opt/appsmith/backend)
[2024-04-09 03:11:25,578]  - Running with Spring Boot v3.0.9, Spring v6.0.11
[2024-04-09 03:11:25,588]  - The following 1 profile is active: "production"
[2024-04-09 03:11:27,590]  - Multiple Spring Data modules found, entering strict repository configuration mode
[2024-04-09 03:11:27,591]  - Bootstrapping Spring Data Reactive MongoDB repositories in DEFAULT mode.
[2024-04-09 03:11:28,174]  - Finished Spring Data repository scanning in 569 ms. Found 44 Reactive MongoDB repository interfaces.
[2024-04-09 03:11:30,993]  - Application started with build version v1.19, and commitSha 867c52974d61ffafd16f6eb06d2a9abc3440df4a
[2024-04-09 03:11:31,715]  - MongoClient with metadata {"driver": {"name": "mongo-java-driver|reactive-streams|spring-boot", "version": "4.8.2"}, "os": {"type": "Linux", "name": "Linux", "architecture": "amd64", "version": "5.15.49-linuxkit"}, "platform": "Java/Eclipse Adoptium/17.0.9+9"} created with settings MongoClientSettings{readPreference=primary, writeConcern=WriteConcern{w=null, wTimeout=null ms, journal=null}, retryWrites=true, retryReads=true, readConcern=ReadConcern{level=null}, credential=MongoCredential{mechanism=null, userName='appsmith', source='appsmith', password=<hidden>, mechanismProperties=<hidden>}, streamFactoryFactory=NettyStreamFactoryFactory{eventLoopGroup=io.netty.channel.nio.NioEventLoopGroup@1a28aef1, socketChannelClass=class io.netty.channel.socket.nio.NioSocketChannel, allocator=PooledByteBufAllocator(directByDefault: true), sslContext=null}, commandListeners=[io.micrometer.core.instrument.binder.mongodb.MongoMetricsCommandListener@522ba524], codecRegistry=ProvidersCodecRegistry{codecProviders=[ValueCodecProvider{}, BsonValueCodecProvider{}, DBRefCodecProvider{}, DBObjectCodecProvider{}, DocumentCodecProvider{}, CollectionCodecProvider{}, IterableCodecProvider{}, MapCodecProvider{}, GeoJsonCodecProvider{}, GridFSFileCodecProvider{}, Jsr310CodecProvider{}, JsonObjectCodecProvider{}, BsonCodecProvider{}, EnumCodecProvider{}, com.mongodb.Jep395RecordCodecProvider@4647881c]}, clusterSettings={hosts=[localhost:27017], srvServiceName=mongodb, mode=SINGLE, requiredClusterType=UNKNOWN, requiredReplicaSetName='null', serverSelector='null', clusterListeners='[]', serverSelectionTimeout='30000 ms', localThreshold='30000 ms'}, socketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=0, receiveBufferSize=0, sendBufferSize=0}, heartbeatSocketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=10000, receiveBufferSize=0, sendBufferSize=0}, connectionPoolSettings=ConnectionPoolSettings{maxSize=100, minSize=0, maxWaitTimeMS=120000, maxConnectionLifeTimeMS=0, maxConnectionIdleTimeMS=0, maintenanceInitialDelayMS=0, maintenanceFrequencyMS=60000, connectionPoolListeners=[io.micrometer.core.instrument.binder.mongodb.MongoMetricsConnectionPoolListener@51dbd6e4], maxConnecting=2}, serverSettings=ServerSettings{heartbeatFrequencyMS=10000, minHeartbeatFrequencyMS=500, serverListeners='[]', serverMonitorListeners='[]'}, sslSettings=SslSettings{enabled=false, invalidHostNameAllowed=false, context=null}, applicationName='null', compressorList=[], uuidRepresentation=JAVA_LEGACY, serverApi=null, autoEncryptionSettings=null, contextProvider=null}
[2024-04-09 03:11:32,011]  - Monitor thread successfully connected to server with description ServerDescription{address=localhost:27017, type=REPLICA_SET_PRIMARY, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=13, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=164977505, setName='mr1', canonicalAddress=localhost:27017, hosts=[localhost:27017], passives=[], arbiters=[], primary='localhost:27017', tagSet=TagSet{[]}, electionId=7fffffff0000000000000003, setVersion=1, topologyVersion=TopologyVersion{processId=6614b1d8b960826999c97427, counter=6}, lastWriteDate=Tue Apr 09 03:11:24 UTC 2024, lastUpdateTimeNanos=6078484847684}
[2024-04-09 03:11:34,653]  - MongoClient with metadata {"driver": {"name": "mongo-java-driver|sync|spring-boot", "version": "4.8.2"}, "os": {"type": "Linux", "name": "Linux", "architecture": "amd64", "version": "5.15.49-linuxkit"}, "platform": "Java/Eclipse Adoptium/17.0.9+9"} created with settings MongoClientSettings{readPreference=primary, writeConcern=WriteConcern{w=null, wTimeout=null ms, journal=null}, retryWrites=true, retryReads=true, readConcern=ReadConcern{level=null}, credential=MongoCredential{mechanism=null, userName='appsmith', source='appsmith', password=<hidden>, mechanismProperties=<hidden>}, streamFactoryFactory=NettyStreamFactoryFactory{eventLoopGroup=io.netty.channel.nio.NioEventLoopGroup@50eb4a2c, socketChannelClass=class io.netty.channel.socket.nio.NioSocketChannel, allocator=PooledByteBufAllocator(directByDefault: true), sslContext=null}, commandListeners=[io.micrometer.core.instrument.binder.mongodb.MongoMetricsCommandListener@522ba524], codecRegistry=ProvidersCodecRegistry{codecProviders=[ValueCodecProvider{}, BsonValueCodecProvider{}, DBRefCodecProvider{}, DBObjectCodecProvider{}, DocumentCodecProvider{}, CollectionCodecProvider{}, IterableCodecProvider{}, MapCodecProvider{}, GeoJsonCodecProvider{}, GridFSFileCodecProvider{}, Jsr310CodecProvider{}, JsonObjectCodecProvider{}, BsonCodecProvider{}, EnumCodecProvider{}, com.mongodb.Jep395RecordCodecProvider@4647881c]}, clusterSettings={hosts=[localhost:27017], srvServiceName=mongodb, mode=SINGLE, requiredClusterType=UNKNOWN, requiredReplicaSetName='null', serverSelector='null', clusterListeners='[]', serverSelectionTimeout='30000 ms', localThreshold='30000 ms'}, socketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=0, receiveBufferSize=0, sendBufferSize=0}, heartbeatSocketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=10000, receiveBufferSize=0, sendBufferSize=0}, connectionPoolSettings=ConnectionPoolSettings{maxSize=100, minSize=0, maxWaitTimeMS=120000, maxConnectionLifeTimeMS=0, maxConnectionIdleTimeMS=0, maintenanceInitialDelayMS=0, maintenanceFrequencyMS=60000, connectionPoolListeners=[io.micrometer.core.instrument.binder.mongodb.MongoMetricsConnectionPoolListener@51dbd6e4], maxConnecting=2}, serverSettings=ServerSettings{heartbeatFrequencyMS=10000, minHeartbeatFrequencyMS=500, serverListeners='[]', serverMonitorListeners='[]'}, sslSettings=SslSettings{enabled=false, invalidHostNameAllowed=false, context=null}, applicationName='null', compressorList=[], uuidRepresentation=JAVA_LEGACY, serverApi=null, autoEncryptionSettings=null, contextProvider=null}
[2024-04-09 03:11:34,692]  - Monitor thread successfully connected to server with description ServerDescription{address=localhost:27017, type=REPLICA_SET_PRIMARY, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=13, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=41923272, setName='mr1', canonicalAddress=localhost:27017, hosts=[localhost:27017], passives=[], arbiters=[], primary='localhost:27017', tagSet=TagSet{[]}, electionId=7fffffff0000000000000003, setVersion=1, topologyVersion=TopologyVersion{processId=6614b1d8b960826999c97427, counter=6}, lastWriteDate=Tue Apr 09 03:11:24 UTC 2024, lastUpdateTimeNanos=6081179815546}
[2024-04-09 03:11:35,466]  - Enabled plugins: []
[2024-04-09 03:11:35,466]  - Disabled plugins: []
[2024-04-09 03:11:35,475]  - PF4J version 3.10.0 in 'deployment' mode
[2024-04-09 03:11:35,895]  - Plugin 'appsmithai-plugin@1.0-SNAPSHOT' resolved
[2024-04-09 03:11:35,896]  - Plugin 'saas-plugin@1.0-SNAPSHOT' resolved
[2024-04-09 03:11:35,896]  - Plugin 'google-sheets-plugin@1.0-SNAPSHOT' resolved
[2024-04-09 03:11:35,897]  - Plugin 'js-plugin@1.0-SNAPSHOT' resolved
[2024-04-09 03:11:35,897]  - Plugin 'oracle-plugin@1.0-SNAPSHOT' resolved
[2024-04-09 03:11:35,897]  - Plugin 'smtp-plugin@1.0-SNAPSHOT' resolved
[2024-04-09 03:11:35,897]  - Plugin 'amazons3-plugin@1.0-SNAPSHOT' resolved
[2024-04-09 03:11:35,897]  - Plugin 'mysql-plugin@1.0-SNAPSHOT' resolved
[2024-04-09 03:11:35,897]  - Plugin 'restapi-plugin@1.0-SNAPSHOT' resolved
[2024-04-09 03:11:35,897]  - Plugin 'postgres-plugin@1.0-SNAPSHOT' resolved
[2024-04-09 03:11:35,897]  - Plugin 'googleai-plugin@1.0-SNAPSHOT' resolved
[2024-04-09 03:11:35,897]  - Plugin 'snowflake-plugin@1.0-SNAPSHOT' resolved
[2024-04-09 03:11:35,897]  - Plugin 'anthropic-plugin@1.0-SNAPSHOT' resolved
[2024-04-09 03:11:35,897]  - Plugin 'elasticsearch-plugin@1.0-SNAPSHOT' resolved
[2024-04-09 03:11:35,898]  - Plugin 'mssql-plugin@1.0-SNAPSHOT' resolved
[2024-04-09 03:11:35,898]  - Plugin 'redshift-plugin@1.0-SNAPSHOT' resolved
[2024-04-09 03:11:35,898]  - Plugin 'databricks-plugin@1.0-SNAPSHOT' resolved
[2024-04-09 03:11:35,898]  - Plugin 'mongo-plugin@1.0-SNAPSHOT' resolved
[2024-04-09 03:11:35,898]  - Plugin 'graphql-plugin@1.0-SNAPSHOT' resolved
[2024-04-09 03:11:35,898]  - Plugin 'aws-lambda-plugin@1.0-SNAPSHOT' resolved
[2024-04-09 03:11:35,898]  - Plugin 'openai-plugin@1.0-SNAPSHOT' resolved
[2024-04-09 03:11:35,898]  - Plugin 'dynamo-plugin@1.0-SNAPSHOT' resolved
[2024-04-09 03:11:35,898]  - Plugin 'arangodb-plugin@1.0-SNAPSHOT' resolved
[2024-04-09 03:11:35,898]  - Plugin 'redis-plugin@1.0-SNAPSHOT' resolved
[2024-04-09 03:11:35,898]  - Plugin 'firestore-plugin@1.0-SNAPSHOT' resolved
[2024-04-09 03:11:35,898]  - Start plugin 'appsmithai-plugin@1.0-SNAPSHOT'
SLF4J: A SLF4J service provider failed to instantiate:
org.slf4j.spi.SLF4JServiceProvider: ch.qos.logback.classic.spi.LogbackServiceProvider not a subtype
SLF4J: A SLF4J service provider failed to instantiate:
org.slf4j.spi.SLF4JServiceProvider: org.slf4j.reload4j.Reload4jServiceProvider not a subtype
SLF4J: No SLF4J providers were found.
SLF4J: Defaulting to no-operation (NOP) logger implementation
SLF4J: See https://www.slf4j.org/codes.html#noProviders for further details.
[2024-04-09 03:11:35,917]  - Start plugin 'saas-plugin@1.0-SNAPSHOT'
[2024-04-09 03:11:35,918]  - Start plugin 'google-sheets-plugin@1.0-SNAPSHOT'
SLF4J: A SLF4J service provider failed to instantiate:
org.slf4j.spi.SLF4JServiceProvider: ch.qos.logback.classic.spi.LogbackServiceProvider not a subtype
SLF4J: A SLF4J service provider failed to instantiate:
org.slf4j.spi.SLF4JServiceProvider: org.slf4j.reload4j.Reload4jServiceProvider not a subtype
SLF4J: No SLF4J providers were found.
SLF4J: Defaulting to no-operation (NOP) logger implementation
SLF4J: See https://www.slf4j.org/codes.html#noProviders for further details.
[2024-04-09 03:11:35,997]  - Start plugin 'js-plugin@1.0-SNAPSHOT'
[2024-04-09 03:11:35,999]  - Start plugin 'oracle-plugin@1.0-SNAPSHOT'
SLF4J: A SLF4J service provider failed to instantiate:
org.slf4j.spi.SLF4JServiceProvider: ch.qos.logback.classic.spi.LogbackServiceProvider not a subtype
SLF4J: A SLF4J service provider failed to instantiate:
org.slf4j.spi.SLF4JServiceProvider: org.slf4j.reload4j.Reload4jServiceProvider not a subtype
SLF4J: No SLF4J providers were found.
SLF4J: Defaulting to no-operation (NOP) logger implementation
SLF4J: See https://www.slf4j.org/codes.html#noProviders for further details.
[2024-04-09 03:11:36,022]  - Start plugin 'smtp-plugin@1.0-SNAPSHOT'
SLF4J: A SLF4J service provider failed to instantiate:
org.slf4j.spi.SLF4JServiceProvider: ch.qos.logback.classic.spi.LogbackServiceProvider not a subtype
SLF4J: A SLF4J service provider failed to instantiate:
org.slf4j.spi.SLF4JServiceProvider: org.slf4j.reload4j.Reload4jServiceProvider not a subtype
SLF4J: No SLF4J providers were found.
SLF4J: Defaulting to no-operation (NOP) logger implementation
SLF4J: See https://www.slf4j.org/codes.html#noProviders for further details.
[2024-04-09 03:11:36,036]  - Start plugin 'amazons3-plugin@1.0-SNAPSHOT'
SLF4J: A SLF4J service provider failed to instantiate:
org.slf4j.spi.SLF4JServiceProvider: ch.qos.logback.classic.spi.LogbackServiceProvider not a subtype
SLF4J: A SLF4J service provider failed to instantiate:
org.slf4j.spi.SLF4JServiceProvider: org.slf4j.reload4j.Reload4jServiceProvider not a subtype
SLF4J: No SLF4J providers were found.
SLF4J: Defaulting to no-operation (NOP) logger implementation
SLF4J: See https://www.slf4j.org/codes.html#noProviders for further details.
[2024-04-09 03:11:36,050]  - Start plugin 'mysql-plugin@1.0-SNAPSHOT'
SLF4J: A SLF4J service provider failed to instantiate:
org.slf4j.spi.SLF4JServiceProvider: ch.qos.logback.classic.spi.LogbackServiceProvider not a subtype
SLF4J: A SLF4J service provider failed to instantiate:
org.slf4j.spi.SLF4JServiceProvider: org.slf4j.reload4j.Reload4jServiceProvider not a subtype
SLF4J: No SLF4J providers were found.
SLF4J: Defaulting to no-operation (NOP) logger implementation
SLF4J: See https://www.slf4j.org/codes.html#noProviders for further details.
[2024-04-09 03:11:36,065]  - Start plugin 'restapi-plugin@1.0-SNAPSHOT'
SLF4J: A SLF4J service provider failed to instantiate:
org.slf4j.spi.SLF4JServiceProvider: ch.qos.logback.classic.spi.LogbackServiceProvider not a subtype
SLF4J: A SLF4J service provider failed to instantiate:
org.slf4j.spi.SLF4JServiceProvider: org.slf4j.reload4j.Reload4jServiceProvider not a subtype
SLF4J: No SLF4J providers were found.
SLF4J: Defaulting to no-operation (NOP) logger implementation
SLF4J: See https://www.slf4j.org/codes.html#noProviders for further details.
[2024-04-09 03:11:36,078]  - Start plugin 'postgres-plugin@1.0-SNAPSHOT'
SLF4J: A SLF4J service provider failed to instantiate:
org.slf4j.spi.SLF4JServiceProvider: ch.qos.logback.classic.spi.LogbackServiceProvider not a subtype
SLF4J: A SLF4J service provider failed to instantiate:
org.slf4j.spi.SLF4JServiceProvider: org.slf4j.reload4j.Reload4jServiceProvider not a subtype
SLF4J: No SLF4J providers were found.
SLF4J: Defaulting to no-operation (NOP) logger implementation
SLF4J: See https://www.slf4j.org/codes.html#noProviders for further details.
[2024-04-09 03:11:36,099]  - Start plugin 'googleai-plugin@1.0-SNAPSHOT'
[2024-04-09 03:11:36,101]  - Start plugin 'snowflake-plugin@1.0-SNAPSHOT'
SLF4J: A SLF4J service provider failed to instantiate:
org.slf4j.spi.SLF4JServiceProvider: ch.qos.logback.classic.spi.LogbackServiceProvider not a subtype
SLF4J: A SLF4J service provider failed to instantiate:
org.slf4j.spi.SLF4JServiceProvider: org.slf4j.reload4j.Reload4jServiceProvider not a subtype
SLF4J: No SLF4J providers were found.
SLF4J: Defaulting to no-operation (NOP) logger implementation
SLF4J: See https://www.slf4j.org/codes.html#noProviders for further details.
[2024-04-09 03:11:36,123]  - Start plugin 'anthropic-plugin@1.0-SNAPSHOT'
[2024-04-09 03:11:36,126]  - Start plugin 'elasticsearch-plugin@1.0-SNAPSHOT'
[2024-04-09 03:11:36,128]  - Start plugin 'mssql-plugin@1.0-SNAPSHOT'
SLF4J: A SLF4J service provider failed to instantiate:
org.slf4j.spi.SLF4JServiceProvider: ch.qos.logback.classic.spi.LogbackServiceProvider not a subtype
SLF4J: A SLF4J service provider failed to instantiate:
org.slf4j.spi.SLF4JServiceProvider: org.slf4j.reload4j.Reload4jServiceProvider not a subtype
SLF4J: No SLF4J providers were found.
SLF4J: Defaulting to no-operation (NOP) logger implementation
SLF4J: See https://www.slf4j.org/codes.html#noProviders for further details.
[2024-04-09 03:11:36,146]  - Start plugin 'redshift-plugin@1.0-SNAPSHOT'
SLF4J: A SLF4J service provider failed to instantiate:
org.slf4j.spi.SLF4JServiceProvider: ch.qos.logback.classic.spi.LogbackServiceProvider not a subtype
SLF4J: A SLF4J service provider failed to instantiate:
org.slf4j.spi.SLF4JServiceProvider: org.slf4j.reload4j.Reload4jServiceProvider not a subtype
SLF4J: No SLF4J providers were found.
SLF4J: Defaulting to no-operation (NOP) logger implementation
SLF4J: See https://www.slf4j.org/codes.html#noProviders for further details.
[2024-04-09 03:11:36,162]  - Start plugin 'databricks-plugin@1.0-SNAPSHOT'
[2024-04-09 03:11:36,172]  - Start plugin 'mongo-plugin@1.0-SNAPSHOT'
[2024-04-09 03:11:36,177]  - Start plugin 'graphql-plugin@1.0-SNAPSHOT'
[2024-04-09 03:11:36,180]  - Start plugin 'aws-lambda-plugin@1.0-SNAPSHOT'
[2024-04-09 03:11:36,225]  - Start plugin 'openai-plugin@1.0-SNAPSHOT'
SLF4J: A SLF4J service provider failed to instantiate:
org.slf4j.spi.SLF4JServiceProvider: ch.qos.logback.classic.spi.LogbackServiceProvider not a subtype
SLF4J: A SLF4J service provider failed to instantiate:
org.slf4j.spi.SLF4JServiceProvider: org.slf4j.reload4j.Reload4jServiceProvider not a subtype
SLF4J: No SLF4J providers were found.
SLF4J: Defaulting to no-operation (NOP) logger implementation
SLF4J: See https://www.slf4j.org/codes.html#noProviders for further details.
[2024-04-09 03:11:36,240]  - Start plugin 'dynamo-plugin@1.0-SNAPSHOT'
SLF4J: A SLF4J service provider failed to instantiate:
org.slf4j.spi.SLF4JServiceProvider: ch.qos.logback.classic.spi.LogbackServiceProvider not a subtype
SLF4J: A SLF4J service provider failed to instantiate:
org.slf4j.spi.SLF4JServiceProvider: org.slf4j.reload4j.Reload4jServiceProvider not a subtype
SLF4J: No SLF4J providers were found.
SLF4J: Defaulting to no-operation (NOP) logger implementation
SLF4J: See https://www.slf4j.org/codes.html#noProviders for further details.
[2024-04-09 03:11:36,255]  - Start plugin 'arangodb-plugin@1.0-SNAPSHOT'
[2024-04-09 03:11:36,257]  - Start plugin 'redis-plugin@1.0-SNAPSHOT'
SLF4J: A SLF4J service provider failed to instantiate:
org.slf4j.spi.SLF4JServiceProvider: ch.qos.logback.classic.spi.LogbackServiceProvider not a subtype
SLF4J: A SLF4J service provider failed to instantiate:
org.slf4j.spi.SLF4JServiceProvider: org.slf4j.reload4j.Reload4jServiceProvider not a subtype
SLF4J: No SLF4J providers were found.
SLF4J: Defaulting to no-operation (NOP) logger implementation
SLF4J: See https://www.slf4j.org/codes.html#noProviders for further details.
[2024-04-09 03:11:36,269]  - Start plugin 'firestore-plugin@1.0-SNAPSHOT'
SLF4J: A SLF4J service provider failed to instantiate:
org.slf4j.spi.SLF4JServiceProvider: ch.qos.logback.classic.spi.LogbackServiceProvider not a subtype
SLF4J: A SLF4J service provider failed to instantiate:
org.slf4j.spi.SLF4JServiceProvider: org.slf4j.reload4j.Reload4jServiceProvider not a subtype
SLF4J: No SLF4J providers were found.
SLF4J: Defaulting to no-operation (NOP) logger implementation
SLF4J: See https://www.slf4j.org/codes.html#noProviders for further details.
SLF4J: A SLF4J service provider failed to instantiate:
org.slf4j.spi.SLF4JServiceProvider: ch.qos.logback.classic.spi.LogbackServiceProvider not a subtype
SLF4J: A SLF4J service provider failed to instantiate:
org.slf4j.spi.SLF4JServiceProvider: org.slf4j.reload4j.Reload4jServiceProvider not a subtype
SLF4J: No SLF4J providers were found.
SLF4J: Defaulting to no-operation (NOP) logger implementation
SLF4J: See https://www.slf4j.org/codes.html#noProviders for further details.
SLF4J: A SLF4J service provider failed to instantiate:
org.slf4j.spi.SLF4JServiceProvider: ch.qos.logback.classic.spi.LogbackServiceProvider not a subtype
SLF4J: A SLF4J service provider failed to instantiate:
org.slf4j.spi.SLF4JServiceProvider: org.slf4j.reload4j.Reload4jServiceProvider not a subtype
SLF4J: No SLF4J providers were found.
SLF4J: Defaulting to no-operation (NOP) logger implementation
SLF4J: See https://www.slf4j.org/codes.html#noProviders for further details.
SLF4J: A SLF4J service provider failed to instantiate:
org.slf4j.spi.SLF4JServiceProvider: ch.qos.logback.classic.spi.LogbackServiceProvider not a subtype
SLF4J: A SLF4J service provider failed to instantiate:
org.slf4j.spi.SLF4JServiceProvider: org.slf4j.reload4j.Reload4jServiceProvider not a subtype
SLF4J: No SLF4J providers were found.
SLF4J: Defaulting to no-operation (NOP) logger implementation
SLF4J: See https://www.slf4j.org/codes.html#noProviders for further details.
SLF4J: A SLF4J service provider failed to instantiate:
org.slf4j.spi.SLF4JServiceProvider: ch.qos.logback.classic.spi.LogbackServiceProvider not a subtype
SLF4J: A SLF4J service provider failed to instantiate:
org.slf4j.spi.SLF4JServiceProvider: org.slf4j.reload4j.Reload4jServiceProvider not a subtype
SLF4J: No SLF4J providers were found.
SLF4J: Defaulting to no-operation (NOP) logger implementation
SLF4J: See https://www.slf4j.org/codes.html#noProviders for further details.
SLF4J: A SLF4J service provider failed to instantiate:
org.slf4j.spi.SLF4JServiceProvider: ch.qos.logback.classic.spi.LogbackServiceProvider not a subtype
SLF4J: A SLF4J service provider failed to instantiate:
org.slf4j.spi.SLF4JServiceProvider: org.slf4j.reload4j.Reload4jServiceProvider not a subtype
SLF4J: No SLF4J providers were found.
SLF4J: Defaulting to no-operation (NOP) logger implementation
SLF4J: See https://www.slf4j.org/codes.html#noProviders for further details.
[2024-04-09 03:11:40,096]  - Mongock runner COMMUNITY version[5.1.6]
[2024-04-09 03:11:40,096]  - Running Mongock with NO metadata
[2024-04-09 03:11:40,096]  - Property transaction-enabled not provided. It will become true as default in next versions. Set explicit value to false in case transaction are not desired.
[2024-04-09 03:11:40,096]  - Property transaction-enabled not provided and is unknown if driver is transactionable. BY DEFAULT MONGOCK WILL RUN IN NO-TRANSACTION MODE.
[2024-04-09 03:11:40,265]  - Reflections took 128 ms to scan 1 urls, producing 5 keys and 60 values
[2024-04-09 03:11:40,309]  - Reflections took 19 ms to scan 1 urls, producing 5 keys and 60 values
[2024-04-09 03:11:40,782]  - Mongock trying to acquire the lock
[2024-04-09 03:11:40,836]  - Mongock acquired the lock until: Tue Apr 09 03:12:40 UTC 2024
[2024-04-09 03:11:40,838]  - Starting mongock lock daemon...
[2024-04-09 03:11:40,863]  - Mongock starting the data migration sequence id[2024-04-09T03:11:39.973077694-758]...
[2024-04-09 03:11:40,869]  - PASSED OVER - {"id"="initialize-schema-version", "type"="execution", "author"="", "class"="DatabaseChangelog0", "method"="initializeSchemaVersion"}
[2024-04-09 03:11:40,869]  - PASSED OVER - {"id"="initial-plugins", "type"="execution", "author"="", "class"="DatabaseChangelog1", "method"="initialPlugins"}
[2024-04-09 03:11:40,869]  - PASSED OVER - {"id"="initial-indexes", "type"="execution", "author"="", "class"="DatabaseChangelog1", "method"="addInitialIndexes"}
[2024-04-09 03:11:40,869]  - PASSED OVER - {"id"="set-initial-sequence-for-datasource", "type"="execution", "author"="", "class"="DatabaseChangelog1", "method"="setInitialSequenceForDatasource"}
[2024-04-09 03:11:40,869]  - PASSED OVER - {"id"="set-plugin-image-and-docs-link", "type"="execution", "author"="", "class"="DatabaseChangelog1", "method"="setPluginImageAndDocsLink"}
[2024-04-09 03:11:40,870]  - PASSED OVER - {"id"="install-mysql-plugins", "type"="execution", "author"="", "class"="DatabaseChangelog1", "method"="mysqlPlugin"}
[2024-04-09 03:11:40,870]  - PASSED OVER - {"id"="update-database-documentation-links", "type"="execution", "author"="", "class"="DatabaseChangelog1", "method"="updateDatabaseDocumentationLinks"}
[2024-04-09 03:11:40,870]  - PASSED OVER - {"id"="generate-unique-id-for-instance", "type"="execution", "author"="", "class"="DatabaseChangelog1", "method"="generateUniqueIdForInstance"}
[2024-04-09 03:11:40,870]  - PASSED OVER - {"id"="fix-password-reset-token-expiration", "type"="execution", "author"="", "class"="DatabaseChangelog1", "method"="fixTokenExpiration"}
[2024-04-09 03:11:40,870]  - PASSED OVER - {"id"="add-elastic-search-plugin", "type"="execution", "author"="", "class"="DatabaseChangelog1", "method"="addElasticSearchPlugin"}
[2024-04-09 03:11:40,870]  - PASSED OVER - {"id"="add-dynamo-plugin", "type"="execution", "author"="", "class"="DatabaseChangelog1", "method"="addDynamoPlugin"}
[2024-04-09 03:11:40,870]  - PASSED OVER - {"id"="use-png-logos", "type"="execution", "author"="", "class"="DatabaseChangelog1", "method"="usePngLogos"}
[2024-04-09 03:11:40,870]  - PASSED OVER - {"id"="add-redis-plugin", "type"="execution", "author"="", "class"="DatabaseChangelog1", "method"="addRedisPlugin"}
[2024-04-09 03:11:40,870]  - PASSED OVER - {"id"="add-msSql-plugin", "type"="execution", "author"="", "class"="DatabaseChangelog1", "method"="addMsSqlPlugin"}
[2024-04-09 03:11:40,870]  - PASSED OVER - {"id"="createNewPageIndexAfterDroppingNewPage", "type"="execution", "author"="", "class"="DatabaseChangelog1", "method"="addNewPageIndexAfterDroppingNewPage"}
[2024-04-09 03:11:40,870]  - PASSED OVER - {"id"="createNewActionIndexAfterDroppingNewAction", "type"="execution", "author"="", "class"="DatabaseChangelog1", "method"="addNewActionIndexAfterDroppingNewAction"}
[2024-04-09 03:11:40,870]  - PASSED OVER - {"id"="new-page-new-action-add-indexes", "type"="execution", "author"="", "class"="DatabaseChangelog1", "method"="addNewPageAndNewActionNewIndexes"}
[2024-04-09 03:11:40,870]  - PASSED OVER - {"id"="update-action-index-to-single-multiple-indices", "type"="execution", "author"="", "class"="DatabaseChangelog1", "method"="updateActionIndexToSingleMultipleIndices"}
[2024-04-09 03:11:40,870]  - PASSED OVER - {"id"="add-firestore-plugin", "type"="execution", "author"="", "class"="DatabaseChangelog1", "method"="addFirestorePlugin"}
[2024-04-09 03:11:40,871]  - PASSED OVER - {"id"="add-redshift-plugin", "type"="execution", "author"="", "class"="DatabaseChangelog1", "method"="addRedshiftPlugin"}
[2024-04-09 03:11:40,871]  - PASSED OVER - {"id"="clear-userdata-collection", "type"="execution", "author"="", "class"="DatabaseChangelog1", "method"="clearUserDataCollection"}
[2024-04-09 03:11:40,871]  - PASSED OVER - {"id"="update-database-documentation-links-v1-2-1", "type"="execution", "author"="", "class"="DatabaseChangelog1", "method"="updateDatabaseDocumentationLinks_v1_2_1"}
[2024-04-09 03:11:40,871]  - PASSED OVER - {"id"="add-amazons3-plugin", "type"="execution", "author"="", "class"="DatabaseChangelog1", "method"="addAmazonS3Plugin"}
[2024-04-09 03:11:40,871]  - PASSED OVER - {"id"="update-plugin-datasource-form-components", "type"="execution", "author"="", "class"="DatabaseChangelog1", "method"="updatePluginDatasourceFormComponents"}
[2024-04-09 03:11:40,871]  - PASSED OVER - {"id"="update-s3-datasource-configuration-and-label", "type"="execution", "author"="", "class"="DatabaseChangelog1", "method"="updateS3DatasourceConfigurationAndLabel"}
[2024-04-09 03:11:40,871]  - PASSED OVER - {"id"="add-google-sheets-plugin", "type"="execution", "author"="", "class"="DatabaseChangelog1", "method"="addGoogleSheetsPlugin"}
[2024-04-09 03:11:40,871]  - PASSED OVER - {"id"="mark-instance-unregistered", "type"="execution", "author"="", "class"="DatabaseChangelog1", "method"="markInstanceAsUnregistered"}
[2024-04-09 03:11:40,871]  - PASSED OVER - {"id"="add-snowflake-plugin", "type"="execution", "author"="", "class"="DatabaseChangelog1", "method"="addSnowflakePlugin"}
[2024-04-09 03:11:40,871]  - PASSED OVER - {"id"="add-arangodb-plugin", "type"="execution", "author"="", "class"="DatabaseChangelog1", "method"="addArangoDBPlugin"}
[2024-04-09 03:11:40,871]  - PASSED OVER - {"id"="set-svg-logo-to-plugins", "type"="execution", "author"="", "class"="DatabaseChangelog1", "method"="setSvgLogoToPluginIcons"}
[2024-04-09 03:11:40,871]  - PASSED OVER - {"id"="create-plugin-reference-for-genarate-CRUD-page", "type"="execution", "author"="", "class"="DatabaseChangelog1", "method"="createPluginReferenceForGenerateCRUDPage"}
[2024-04-09 03:11:40,871]  - PASSED OVER - {"id"="create-plugin-reference-for-S3-GSheet-genarate-CRUD-page", "type"="execution", "author"="", "class"="DatabaseChangelog1", "method"="createPluginReferenceForS3AndGSheetGenerateCRUDPage"}
[2024-04-09 03:11:40,871]  - PASSED OVER - {"id"="add-js-plugin", "type"="execution", "author"="", "class"="DatabaseChangelog1", "method"="addJSPlugin"}
[2024-04-09 03:11:40,871]  - PASSED OVER - {"id"="update-plugin-package-name-index", "type"="execution", "author"="", "class"="DatabaseChangelog1", "method"="updatePluginPackageNameIndexToPluginNamePackageNameAndVersion"}
[2024-04-09 03:11:40,872]  - PASSED OVER - {"id"="migrate-s3-to-uqi", "type"="execution", "author"="", "class"="DatabaseChangelog1", "method"="migrateS3PluginToUqi"}
[2024-04-09 03:11:40,872]  - PASSED OVER - {"id"="add-smtp-plugin", "type"="execution", "author"="", "class"="DatabaseChangelog1", "method"="addSmtpPluginPlugin"}
[2024-04-09 03:11:40,872]  - PASSED OVER - {"id"="add-google-sheets-plugin-name", "type"="execution", "author"="", "class"="DatabaseChangelog1", "method"="addPluginNameForGoogleSheets"}
[2024-04-09 03:11:40,872]  - PASSED OVER - {"id"="migrate-firestore-to-uqi", "type"="execution", "author"="", "class"="DatabaseChangelog1", "method"="migrateFirestorePluginToUqi"}
[2024-04-09 03:11:40,872]  - PASSED OVER - {"id"="migrate-firestore-to-uqi-3", "type"="execution", "author"="", "class"="DatabaseChangelog1", "method"="migrateFirestorePluginToUqi3"}
[2024-04-09 03:11:40,872]  - PASSED OVER - {"id"="update-index-for-git", "type"="execution", "author"="", "class"="DatabaseChangelog1", "method"="updateGitIndexes"}
[2024-04-09 03:11:40,872]  - PASSED OVER - {"id"="use-assets-cdn-for-plugin-icons", "type"="execution", "author"="", "class"="DatabaseChangelog1", "method"="useAssetsCDNForPluginIcons"}
[2024-04-09 03:11:40,872]  - PASSED OVER - {"id"="update-index-for-newAction-actionCollection-userData", "type"="execution", "author"="", "class"="DatabaseChangelog1", "method"="updateNewActionActionCollectionAndUserDataIndexes"}
[2024-04-09 03:11:40,873]  - PASSED OVER - {"id"="mark-mssql-crud-unavailable", "type"="execution", "author"="", "class"="DatabaseChangelog1", "method"="markMSSQLCrudUnavailable"}
[2024-04-09 03:11:40,873]  - PASSED OVER - {"id"="update-index-for-newAction-actionCollection", "type"="execution", "author"="", "class"="DatabaseChangelog1", "method"="updateNewActionActionCollectionIndexes"}
[2024-04-09 03:11:40,875]  - PASSED OVER - {"id"="fix-plugin-title-casing", "type"="execution", "author"="", "class"="DatabaseChangelog2", "method"="fixPluginTitleCasing"}
[2024-04-09 03:11:40,875]  - PASSED OVER - {"id"="update-git-indexes", "type"="execution", "author"="", "class"="DatabaseChangelog2", "method"="addIndexesForGit"}
[2024-04-09 03:11:40,876]  - PASSED OVER - {"id"="add-workspace-indexes", "type"="execution", "author"="", "class"="DatabaseChangelog2", "method"="addWorkspaceIndexes"}
[2024-04-09 03:11:40,876]  - PASSED OVER - {"id"="add-default-tenant", "type"="execution", "author"="", "class"="DatabaseChangelog2", "method"="addDefaultTenant"}
[2024-04-09 03:11:40,876]  - PASSED OVER - {"id"="organization-to-workspace-indexes-recreate", "type"="execution", "author"="", "class"="DatabaseChangelog2", "method"="organizationToWorkspaceIndexesRecreate"}
[2024-04-09 03:11:40,876]  - PASSED OVER - {"id"="flush-spring-redis-keys-2a", "type"="execution", "author"="", "class"="DatabaseChangelog2", "method"="clearRedisCache2"}
[2024-04-09 03:11:40,876]  - PASSED OVER - {"id"="add-anonymousUser", "type"="execution", "author"="", "class"="DatabaseChangelog2", "method"="addAnonymousUser"}
[2024-04-09 03:11:40,876]  - PASSED OVER - {"id"="add-instance-config-object", "type"="execution", "author"="", "class"="DatabaseChangelog2", "method"="addInstanceConfigurationPlaceHolder"}
[2024-04-09 03:11:40,876]  - PASSED OVER - {"id"="add-anonymous-user-permission-group", "type"="execution", "author"="", "class"="DatabaseChangelog2", "method"="addAnonymousUserPermissionGroup"}
[2024-04-09 03:11:40,876]  - PASSED OVER - {"id"="create-themes-indices", "type"="execution", "author"="", "class"="DatabaseChangelog2", "method"="createThemesIndices"}
[2024-04-09 03:11:40,980]  - method[createSystemThemes3] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 03:11:41,346]  - RE-APPLIED - {"id"="create-system-themes-v3", "type"="execution", "author"="", "class"="DatabaseChangelog2", "method"="createSystemThemes3"}
[2024-04-09 03:11:41,346]  - PASSED OVER - {"id"="create-indices-on-permissions-for-performance", "type"="execution", "author"="", "class"="DatabaseChangelog2", "method"="addPermissionGroupIndex"}
[2024-04-09 03:11:41,346]  - PASSED OVER - {"id"="update-bad-theme-state", "type"="execution", "author"="", "class"="DatabaseChangelog2", "method"="updateBadThemeState"}
[2024-04-09 03:11:41,347]  - PASSED OVER - {"id"="add-tenant-admin-permissions-instance-admin", "type"="execution", "author"="", "class"="DatabaseChangelog2", "method"="addTenantAdminPermissionsToInstanceAdmin"}
[2024-04-09 03:11:41,347]  - PASSED OVER - {"id"="add-graphql-plugin", "type"="execution", "author"="", "class"="DatabaseChangelog2", "method"="addGraphQLPlugin"}
[2024-04-09 03:11:41,347]  - PASSED OVER - {"id"="indices-recommended-by-mongodb-cloud", "type"="execution", "author"="", "class"="DatabaseChangelog2", "method"="addIndicesRecommendedByMongoCloud"}
[2024-04-09 03:11:41,347]  - PASSED OVER - {"id"="add-unique-index-for-uidstring", "type"="execution", "author"="", "class"="DatabaseChangelog2", "method"="addUniqueIndexOnUidString"}
[2024-04-09 03:11:41,347]  - PASSED OVER - {"id"="change-readPermissionGroup-to-readPermissionGroupMembers", "type"="execution", "author"="", "class"="DatabaseChangelog2", "method"="modifyReadPermissionGroupToReadPermissionGroupMembers"}
[2024-04-09 03:11:41,347]  - PASSED OVER - {"id"="delete-permissions-in-permissionGroups", "type"="execution", "author"="", "class"="DatabaseChangelog2", "method"="deletePermissionsInPermissionGroups"}
[2024-04-09 03:11:41,347]  - PASSED OVER - {"id"="remove-usage-pulses-for-appsmith-cloud", "type"="execution", "author"="", "class"="DatabaseChangelog2", "method"="removeUsagePulsesForAppsmithCloud"}
[2024-04-09 03:11:41,347]  - PASSED OVER - {"id"="add-ssl-mode-settings-for-existing-mssql-datasources", "type"="execution", "author"="", "class"="DatabaseChangelog2", "method"="addSslModeSettingsForExistingMssqlDatasource"}
[2024-04-09 03:11:41,347]  - PASSED OVER - {"id"="add-oracle-plugin", "type"="execution", "author"="", "class"="DatabaseChangelog2", "method"="addOraclePlugin"}
[2024-04-09 03:11:41,347]  - PASSED OVER - {"id"="update-oracle-plugin-name", "type"="execution", "author"="", "class"="DatabaseChangelog2", "method"="updateOraclePluginName"}
[2024-04-09 03:11:41,361]  - method[updateSuperUsers] with arguments: [org.springframework.data.mongodb.core.MongoTemplate, com.appsmith.server.repositories.CacheableRepositoryHelper, com.appsmith.server.solutions.PolicySolution, com.appsmith.server.acl.PolicyGenerator]
[2024-04-09 03:11:41,443]  - RE-APPLIED - {"id"="update-super-users", "type"="execution", "author"="", "class"="DatabaseChangelog2", "method"="updateSuperUsers"}
[2024-04-09 03:11:41,448]  - method[com.appsmith.server.migrations.db.ce.Migration003AddInstanceNameToTenantConfiguration] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 03:11:41,448]  - PASSED OVER - {"id"="add-instance-name-env-variable-tenant-configuration", "type"="execution", "author"="default_author", "class"="Migration003AddInstanceNameToTenantConfiguration", "method"="addInstanceNameEnvVarToTenantConfiguration"}
[2024-04-09 03:11:41,450]  - method[com.appsmith.server.migrations.db.ce.Migration004PermissionGroupDefaultWorkspaceIdMigration] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 03:11:41,451]  - PASSED OVER - {"id"="migrate-default-workspace-id-to-default-domain-id", "type"="execution", "author"="default_author", "class"="Migration004PermissionGroupDefaultWorkspaceIdMigration", "method"="defaultWorkspaceIdMigration"}
[2024-04-09 03:11:41,454]  - method[com.appsmith.server.migrations.db.ce.Migration005CreateIndexForApplicationSnapshotMigration] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 03:11:41,455]  - PASSED OVER - {"id"="create-index-for-application-snapshot-collection", "type"="execution", "author"="default_author", "class"="Migration005CreateIndexForApplicationSnapshotMigration", "method"="addIndexOnApplicationIdAndChunkOrder"}
[2024-04-09 03:11:41,458]  - method[com.appsmith.server.migrations.db.ce.Migration006ResetOnPageLoadEdgesInLayouts] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 03:11:41,458]  - PASSED OVER - {"id"="reset-on-page-load-edges-in-layouts", "type"="execution", "author"="default_author", "class"="Migration006ResetOnPageLoadEdgesInLayouts", "method"="executeMigration"}
[2024-04-09 03:11:41,461]  - method[com.appsmith.server.migrations.db.ce.Migration007OptOutUnsupportedPluginsForAirGap] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 03:11:41,462]  - PASSED OVER - {"id"="opt-out-unsupported-plugins-airgap-instance", "type"="execution", "author"="default_author", "class"="Migration007OptOutUnsupportedPluginsForAirGap", "method"="optOutUnsupportedPluginsForAirGapInstance"}
[2024-04-09 03:11:41,465]  - method[com.appsmith.server.migrations.db.ce.Migration008SupportNonHostedPluginsForAirgap] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 03:11:41,465]  - PASSED OVER - {"id"="support-non-self-hosted-plugins-for-airgap", "type"="execution", "author"="default_author", "class"="Migration008SupportNonHostedPluginsForAirgap", "method"="supportNonHostedPluginsForAirgap"}
[2024-04-09 03:11:41,468]  - method[com.appsmith.server.migrations.db.ce.Migration009UpdateOracleLogoToSVG] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 03:11:41,468]  - PASSED OVER - {"id"="update-oracle-logo-to-svg", "type"="execution", "author"="default_author", "class"="Migration009UpdateOracleLogoToSVG", "method"="updateOracleLogoToSVG"}
[2024-04-09 03:11:41,472]  - method[com.appsmith.server.migrations.db.ce.Migration010UpdatePluginDocsLink] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 03:11:41,472]  - PASSED OVER - {"id"="update-plugins-docs-link", "type"="execution", "author"="default_author", "class"="Migration010UpdatePluginDocsLink", "method"="updatePluginDocumentationLinks"}
[2024-04-09 03:11:41,475]  - method[com.appsmith.server.migrations.db.ce.Migration011CreateIndexDefaultDomainIdDefaultDomainTypeDropIndexDefaultWorkspaceId] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 03:11:41,476]  - PASSED OVER - {"id"="create-index-default-domain-id-default-domain-type", "type"="execution", "author"="default_author", "class"="Migration011CreateIndexDefaultDomainIdDefaultDomainTypeDropIndexDefaultWorkspaceId", "method"="createNewIndexDefaultDomainIdDefaultDomainTypeAndDropOldIndexDefaultWorkspaceId"}
[2024-04-09 03:11:41,490]  - method[com.appsmith.server.migrations.db.ce.Migration012RemoveStructureFromWithinDatasource] with arguments: [org.springframework.data.mongodb.core.MongoOperations, org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 03:11:41,490]  - PASSED OVER - {"id"="remove-structure-from-within-datasource-modified", "type"="execution", "author"="default_author", "class"="Migration012RemoveStructureFromWithinDatasource", "method"="executeMigration"}
[2024-04-09 03:11:41,493]  - method[com.appsmith.server.migrations.db.ce.Migration013AddEmailBodyTypeToSMTPPlugin] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 03:11:41,493]  - PASSED OVER - {"id"="add-smtp-email-body-type", "type"="execution", "author"="default_author", "class"="Migration013AddEmailBodyTypeToSMTPPlugin", "method"="addSmtpEmailBodyType"}
[2024-04-09 03:11:41,496]  - method[com.appsmith.server.migrations.db.ce.Migration014AddIndexToDatasourceStorage] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 03:11:41,497]  - PASSED OVER - {"id"="index-for-datasource-storage", "type"="execution", "author"="default_author", "class"="Migration014AddIndexToDatasourceStorage", "method"="addingIndexToDatasourceStorage"}
[2024-04-09 03:11:41,499]  - method[com.appsmith.server.migrations.db.ce.Migration015AddPluginTypeIndexToNewActionCollection] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 03:11:41,499]  - PASSED OVER - {"id"="app-id-plugin-type-index-for-new-action", "type"="execution", "author"="default_author", "class"="Migration015AddPluginTypeIndexToNewActionCollection", "method"="addingIndexToNewAction"}
[2024-04-09 03:11:41,502]  - method[com.appsmith.server.migrations.db.ce.Migration016RenameIndexesWithLongNames] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 03:11:41,503]  - PASSED OVER - {"id"="rename-indexes-with-long-names", "type"="execution", "author"="default_author", "class"="Migration016RenameIndexesWithLongNames", "method"="executeMigration"}
[2024-04-09 03:11:41,505]  - method[com.appsmith.server.migrations.db.ce.Migration017UnsetEncryptionVersion2Fields] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 03:11:41,506]  - PASSED OVER - {"id"="unset-not-encrypted-encryption-version-2-fields", "type"="execution", "author"="default_author", "class"="Migration017UnsetEncryptionVersion2Fields", "method"="executeMigration"}
[2024-04-09 03:11:41,509]  - method[com.appsmith.server.migrations.db.ce.Migration018UpdateOraclePluginDocumentationLink] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 03:11:41,509]  - PASSED OVER - {"id"="update-oracle-doc-link", "type"="execution", "author"="default_author", "class"="Migration018UpdateOraclePluginDocumentationLink", "method"="updateOracleDocumentationLink"}
[2024-04-09 03:11:41,512]  - method[com.appsmith.server.migrations.db.ce.Migration019RemoveNullEnvIdDatasourceStructureDocuments] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 03:11:41,512]  - PASSED OVER - {"id"="delete-null-envId-key-document", "type"="execution", "author"="default_author", "class"="Migration019RemoveNullEnvIdDatasourceStructureDocuments", "method"="executeMigration"}
[2024-04-09 03:11:41,520]  - method[com.appsmith.server.migrations.db.ce.Migration021MoveGoogleMapsKeyToTenantConfiguration] with arguments: [org.springframework.data.mongodb.core.MongoTemplate, com.appsmith.server.configurations.CommonConfig]
[2024-04-09 03:11:41,521]  - PASSED OVER - {"id"="move-google-maps-key-to-tenant-configuration", "type"="execution", "author"="default_author", "class"="Migration021MoveGoogleMapsKeyToTenantConfiguration", "method"="executeMigration"}
[2024-04-09 03:11:41,523]  - method[com.appsmith.server.migrations.db.ce.Migration022AddConnectionMethodDefaultValueToAllMySQLDatasources] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 03:11:41,524]  - PASSED OVER - {"id"="add-connection-method-default-value-for-mysql", "type"="execution", "author"="default_author", "class"="Migration022AddConnectionMethodDefaultValueToAllMySQLDatasources", "method"="updateConnectionMethodDefaultValueForMySQL"}
[2024-04-09 03:11:41,526]  - method[com.appsmith.server.migrations.db.ce.Migration023UpdateJSPluginIcon] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 03:11:41,527]  - PASSED OVER - {"id"="update-js-plugin-icon", "type"="execution", "author"="default_author", "class"="Migration023UpdateJSPluginIcon", "method"="executeMigration"}
[2024-04-09 03:11:41,530]  - method[com.appsmith.server.migrations.db.ce.Migration024EnableGenerateCRUDPageToggleForMssqlPlugin] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 03:11:41,531]  - PASSED OVER - {"id"="enable-generate-crud-page-for-mssql", "type"="execution", "author"="default_author", "class"="Migration024EnableGenerateCRUDPageToggleForMssqlPlugin", "method"="enableGenerateCRUDPageToggle"}
[2024-04-09 03:11:41,533]  - method[com.appsmith.server.migrations.db.ce.Migration025AddIndexDeletedInApplication] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 03:11:41,534]  - PASSED OVER - {"id"="add-index-application-deleted", "type"="execution", "author"="default_author", "class"="Migration025AddIndexDeletedInApplication", "method"="createIndexInApplicationCollection"}
[2024-04-09 03:11:41,537]  - method[com.appsmith.server.migrations.db.ce.Migration025RemoveUnassignPermissionFromUnnecessaryRoles] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 03:11:41,537]  - PASSED OVER - {"id"="remove-unassign-permission-from-workspace-dev-viewer-roles", "type"="execution", "author"="default_author", "class"="Migration025RemoveUnassignPermissionFromUnnecessaryRoles", "method"="executeMigration"}
[2024-04-09 03:11:41,540]  - method[com.appsmith.server.migrations.db.ce.Migration026AddIndexTenantAndDeletedInWorkspace] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 03:11:41,540]  - PASSED OVER - {"id"="add-index-workspace-tenant-deleted", "type"="execution", "author"="default_author", "class"="Migration026AddIndexTenantAndDeletedInWorkspace", "method"="addIndexInWorkspaceCollection"}
[2024-04-09 03:11:41,543]  - method[com.appsmith.server.migrations.db.ce.Migration027AddIndexDatasourceIdAndDeletedInAction] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 03:11:41,543]  - PASSED OVER - {"id"="new-action-compound-index-datasource-id", "type"="execution", "author"="default_author", "class"="Migration027AddIndexDatasourceIdAndDeletedInAction", "method"="addIndexInNewActionCollection"}
[2024-04-09 03:11:41,546]  - method[com.appsmith.server.migrations.db.ce.Migration028TagUserManagementRolesWithoutDefaultDomainTypeAndId] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 03:11:41,546]  - PASSED OVER - {"id"="tag-user-management-roles-without-default-domain-type-and-id", "type"="execution", "author"="default_author", "class"="Migration028TagUserManagementRolesWithoutDefaultDomainTypeAndId", "method"="tagUserManagementRolesWithoutDefaultDomainTypeAndId"}
[2024-04-09 03:11:41,548]  - method[com.appsmith.server.migrations.db.ce.Migration029PopulateDefaultDomainIdInUserManagementRoles] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 03:11:41,548]  - PASSED OVER - {"id"="populate-default-domain-id-in-user-management-roles", "type"="execution", "author"="default_author", "class"="Migration029PopulateDefaultDomainIdInUserManagementRoles", "method"="populateDefaultDomainIdInUserManagementRoles"}
[2024-04-09 03:11:41,551]  - method[com.appsmith.server.migrations.db.ce.Migration030TagUsersWithNoUserManagementRoles] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 03:11:41,551]  - PASSED OVER - {"id"="tag-users-with-no-user-management-roles", "type"="execution", "author"="default_author", "class"="Migration030TagUsersWithNoUserManagementRoles", "method"="tagUsersWithNoUserManagementRoles"}
[2024-04-09 03:11:41,554]  - method[com.appsmith.server.migrations.db.ce.Migration031CreateUserManagementRolesForUsersTaggedIn030] with arguments: [org.springframework.data.mongodb.core.MongoTemplate, com.appsmith.server.solutions.PolicySolution]
[2024-04-09 03:11:41,554]  - PASSED OVER - {"id"="create-user-management-roles-for-users-tagged-in-migration-030", "type"="execution", "author"="default_author", "class"="Migration031CreateUserManagementRolesForUsersTaggedIn030", "method"="createUserManagementRolesForUsersTaggedInMigration030"}
[2024-04-09 03:11:41,556]  - method[com.appsmith.server.migrations.db.ce.Migration032AddingXmlParserToApplicationLibraries] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 03:11:41,557]  - PASSED OVER - {"id"="add-xml-parser-to-application-jslibs", "type"="execution", "author"="default_author", "class"="Migration032AddingXmlParserToApplicationLibraries", "method"="addXmlParserEntryToEachApplication"}
[2024-04-09 03:11:41,559]  - method[com.appsmith.server.migrations.db.ce.Migration033AddOpenAIPlugin] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 03:11:41,560]  - PASSED OVER - {"id"="add-open-ai-plugin", "type"="execution", "author"="default_author", "class"="Migration033AddOpenAIPlugin", "method"="addPluginToDbAndWorkspace"}
[2024-04-09 03:11:41,562]  - method[com.appsmith.server.migrations.db.ce.Migration034ChangeOpenAIIntegrationDocumentationLink] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 03:11:41,562]  - PASSED OVER - {"id"="change-open-ai-integration-documentation-link", "type"="execution", "author"="default_author", "class"="Migration034ChangeOpenAIIntegrationDocumentationLink", "method"="changeDocumentationLink"}
[2024-04-09 03:11:41,566]  - method[com.appsmith.server.migrations.db.ce.Migration035AddAnthropicPlugin] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 03:11:41,567]  - PASSED OVER - {"id"="add-anthropic-plugin", "type"="execution", "author"="default_author", "class"="Migration035AddAnthropicPlugin", "method"="addPluginToDbAndWorkspace"}
[2024-04-09 03:11:41,577]  - method[com.appsmith.server.migrations.db.ce.Migration035RemoveMockDbEndPointInDatasourceInSelfHostedInstance] with arguments: [org.springframework.data.mongodb.core.MongoTemplate, com.appsmith.server.configurations.CommonConfig, com.appsmith.server.solutions.EnvManager]
[2024-04-09 03:11:41,578]  - PASSED OVER - {"id"="remove-mockdb-endpoint-in-datasource-self-hosted-instance", "type"="execution", "author"="default_author", "class"="Migration035RemoveMockDbEndPointInDatasourceInSelfHostedInstance", "method"="removeMockDbEndpointInDatasource"}
[2024-04-09 03:11:41,581]  - method[com.appsmith.server.migrations.db.ce.Migration036AddRecentlyUsedEntitiesForUserData] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 03:11:41,582]  - PASSED OVER - {"id"="add-recently-used-entities-for-user", "type"="execution", "author"="default_author", "class"="Migration036AddRecentlyUsedEntitiesForUserData", "method"="addRecentlyUsedEntitiesForUserData"}
[2024-04-09 03:11:41,584]  - method[com.appsmith.server.migrations.db.ce.Migration037AddCompoundIndexForNameAndDeletedAt] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 03:11:41,584]  - PASSED OVER - {"id"="add-compound-index-name-deleted", "type"="execution", "author"="default_author", "class"="Migration037AddCompoundIndexForNameAndDeletedAt", "method"="addIndexInWorkspaceAndApplicationsCollection"}
[2024-04-09 03:11:41,587]  - method[com.appsmith.server.migrations.db.ce.Migration037MarkAnonymousUserAsSystemGenerated] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 03:11:41,588]  - PASSED OVER - {"id"="mark-anonymous-user-as-system-generated", "type"="execution", "author"="default_author", "class"="Migration037MarkAnonymousUserAsSystemGenerated", "method"="executeMigration"}
[2024-04-09 03:11:41,592]  - method[com.appsmith.server.migrations.db.ce.Migration038AddCompoundIndexForActionCollection] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 03:11:41,593]  - PASSED OVER - {"id"="add-compound-index-action-collection", "type"="execution", "author"="default_author", "class"="Migration038AddCompoundIndexForActionCollection", "method"="addIndexInActionCollection"}
[2024-04-09 03:11:41,595]  - method[com.appsmith.server.migrations.db.ce.Migration038AddGoogleAIPlugin] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 03:11:41,596]  - PASSED OVER - {"id"="add-google-ai-plugin", "type"="execution", "author"="default_author", "class"="Migration038AddGoogleAIPlugin", "method"="addPluginToDbAndWorkspace"}
[2024-04-09 03:11:41,598]  - method[com.appsmith.server.migrations.db.ce.Migration039AddCompoundIndexForDatasourceStorage] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 03:11:41,599]  - PASSED OVER - {"id"="add-compound-index-datasource-storage", "type"="execution", "author"="default_author", "class"="Migration039AddCompoundIndexForDatasourceStorage", "method"="addIndexInDatasourceStorageCollection"}
[2024-04-09 03:11:41,601]  - method[com.appsmith.server.migrations.db.ce.Migration039AddDatabricksPlugin] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 03:11:41,602]  - PASSED OVER - {"id"="add-databricks-plugin", "type"="execution", "author"="default_author", "class"="Migration039AddDatabricksPlugin", "method"="addPluginToDbAndWorkspace"}
[2024-04-09 03:11:41,605]  - method[com.appsmith.server.migrations.db.ce.Migration039OpenAIMessagesJsToggle] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 03:11:41,606]  - PASSED OVER - {"id"="move-messages-to-data-key-in-openai", "type"="execution", "author"="default_author", "class"="Migration039OpenAIMessagesJsToggle", "method"="moveMessagesToDataKeyForSupportingJsToggle"}
[2024-04-09 03:11:41,610]  - method[com.appsmith.server.migrations.db.ce.Migration040AddAWSLambdaPlugin] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 03:11:41,610]  - PASSED OVER - {"id"="add-aws-lambda-plugin", "type"="execution", "author"="default_author", "class"="Migration040AddAWSLambdaPlugin", "method"="addPluginToDbAndWorkspace"}
[2024-04-09 03:11:41,612]  - method[com.appsmith.server.migrations.db.ce.Migration040AddAppsmithAiPlugin] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 03:11:41,612]  - PASSED OVER - {"id"="add-appsmith-ai-plugin", "type"="execution", "author"="default_author", "class"="Migration040AddAppsmithAiPlugin", "method"="addPluginToDbAndWorkspace"}
[2024-04-09 03:11:41,615]  - method[com.appsmith.server.migrations.db.ce.Migration041TagWorkspacesForGitOperationsPermissionMigration] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 03:11:41,616]  - PASSED OVER - {"id"="tag-workspaces-to-migrate-adding-git-permissions", "type"="execution", "author"="default_author", "class"="Migration041TagWorkspacesForGitOperationsPermissionMigration", "method"="addPermissionForGitOperationsToExistingWorkspaces"}
[2024-04-09 03:11:41,619]  - method[com.appsmith.server.migrations.db.ce.Migration042AddPermissionsForGitOperations] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 03:11:41,619]  - PASSED OVER - {"id"="add-permissions-for-git-operations", "type"="execution", "author"="default_author", "class"="Migration042AddPermissionsForGitOperations", "method"="addPermissionForGitOperationsToExistingApplications"}
[2024-04-09 03:11:41,621]  - method[com.appsmith.server.migrations.db.ce.Migration043AddIndexActionCollectionPageID] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 03:11:41,621]  - PASSED OVER - {"id"="add-index-to-action-collection-default-resources-pageid", "type"="execution", "author"="default_author", "class"="Migration043AddIndexActionCollectionPageID", "method"="addIndexToActionCollectionCollection"}
[2024-04-09 03:11:41,624]  - method[com.appsmith.server.migrations.db.ce.Migration044AddIndexesToDomainObjects] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 03:11:41,624]  - PASSED OVER - {"id"="add-index-to-domain-objects", "type"="execution", "author"="default_author", "class"="Migration044AddIndexesToDomainObjects", "method"="addIndexToDomainObjects"}
[2024-04-09 03:11:41,627]  - method[com.appsmith.server.migrations.db.ce.Migration045AddDefaultAppsmithAiDatasourceForOrphanActions] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 03:11:41,627]  - PASSED OVER - {"id"="add-default-appsmith-datasource", "type"="execution", "author"="default_author", "class"="Migration045AddDefaultAppsmithAiDatasourceForOrphanActions", "method"="addDefaultAppsmithAiDatasourceForOrphanActions"}
[2024-04-09 03:11:41,630]  - method[com.appsmith.server.migrations.db.ce.Migration046DeleteArchivedPlugins] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 03:11:41,630]  - PASSED OVER - {"id"="delete-archived-pluginsd", "type"="execution", "author"="default_author", "class"="Migration046DeleteArchivedPlugins", "method"="deleteArchivedPlugins"}
[2024-04-09 03:11:41,632]  - method[com.appsmith.server.migrations.db.ce.Migration047AddMissingFieldsInDefaultAppsmithAiDatasource] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 03:11:41,633]  - PASSED OVER - {"id"="add-missing-fields-in-default-appsmith-datasource", "type"="execution", "author"="default_author", "class"="Migration047AddMissingFieldsInDefaultAppsmithAiDatasource", "method"="addMissingFieldsInDefaultAppsmithAiDatasource"}
[2024-04-09 03:11:41,635]  - method[com.appsmith.server.migrations.db.ce.Migration048AddCompoundIndexToUserEntity] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 03:11:41,635]  - PASSED OVER - {"id"="add-compound-index-in-user-collection", "type"="execution", "author"="default_author", "class"="Migration048AddCompoundIndexToUserEntity", "method"="addMissingIndexInUserCollection"}
[2024-04-09 03:11:41,638]  - method[com.appsmith.server.migrations.db.ce.Migration049RemoveLayoutBaseFields] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 03:11:41,638]  - PASSED OVER - {"id"="remove-layout-base-fields", "type"="execution", "author"="default_author", "class"="Migration049RemoveLayoutBaseFields", "method"="execute"}
[2024-04-09 03:11:41,640]  - method[com.appsmith.server.migrations.db.ce.Migration020TransferToDatasourceStorage] with arguments: [org.springframework.data.mongodb.core.MongoTemplate]
[2024-04-09 03:11:41,640]  - PASSED OVER - {"id"="migrate-configurations-to-data-storage-v2", "type"="execution", "author"="default_author", "class"="Migration020TransferToDatasourceStorage", "method"="executeMigration"}
[2024-04-09 03:11:41,641]  - Mongock releasing the lock
[2024-04-09 03:11:41,641]  - Mongock releasing the lock
[2024-04-09 03:11:41,648]  - Mongock released the lock
[2024-04-09 03:11:41,648]  - Mongock has finished
[2024-04-09 03:11:42,727]  - Exposing 2 endpoint(s) beneath base path '/actuator'
[2024-04-09 03:11:43,231]  - Netty started on port 8080
[2024-04-09 03:11:43,254]  - Started ServerApplication in 19.074 seconds (process running for 21.665)
[2024-04-09 03:11:43,274]  - Performing RTS health check of this instance...
[2024-04-09 03:11:43,305]  - In memory cache miss for anonymous user permission groups. Fetching from DB and adding it to in memory storage.
[2024-04-09 03:11:43,410]  - Fetched and set conenncted mongo db version as: 5.0.26
[2024-04-09 03:11:43,680]  - RTS health check succeeded
[2024-04-09 03:11:43,691]  - License verification completed with status: valid
[2024-04-09 03:11:53,254]  - Fetching features for default tenant
[2024-04-09 03:12:00,738]  - Cancelled mongock lock daemon
[2024-04-09 03:15:18,886] userEmail=<EMAIL>, sessionId=65e970a7-e395-4475-821a-7440618a868b, thread=reactor-http-epoll-1, requestId=2c46c526-7bdb-40a0-a2ce-4044580e894e - Going to fetch consolidatedAPI response for applicationId: null, defaultPageId: null, branchName: null, mode: PUBLISHED
[2024-04-09 03:15:20,592] userEmail=<EMAIL>, sessionId=65e970a7-e395-4475-821a-7440618a868b, thread=lettuce-epollEventLoop-10-1, requestId=1f0d553d-0a98-4b4c-9316-e176dee1846f - Going to get all applications by workspace id 6614a802cb467e382bcada24
[2024-04-09 03:15:34,838] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=lettuce-epollEventLoop-10-1, requestId=7bb55cbe-4fb2-4b97-bb35-d236182064fc - Going to get all applications by workspace id 6614aaf6cb467e382bcada2f
[2024-04-09 03:15:38,453] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=lettuce-epollEventLoop-10-1, requestId=6fa5610a-3d0f-4157-a4e1-a0f1ba2c6896 - Going to fetch consolidatedAPI response for applicationId: null, defaultPageId: 6614aaf6cb467e382bcada37, branchName: null, mode: EDIT
[2024-04-09 03:15:38,556] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=nioEventLoopGroup-3-7, requestId=6fa5610a-3d0f-4157-a4e1-a0f1ba2c6896 - Retrieved possible application ids for page, picking the appropriate one now
[2024-04-09 03:15:38,719] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=nioEventLoopGroup-3-4, requestId=6fa5610a-3d0f-4157-a4e1-a0f1ba2c6896 - Fetched application data for id: 6614aaf6cb467e382bcada34
[2024-04-09 03:15:38,846] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=nioEventLoopGroup-3-7, requestId=6fa5610a-3d0f-4157-a4e1-a0f1ba2c6896 - Retrieved Page DTOs from DB ...
[2024-04-09 03:15:38,847] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=nioEventLoopGroup-3-7, requestId=6fa5610a-3d0f-4157-a4e1-a0f1ba2c6896 - Populating applicationPagesDTO ...
[2024-04-09 03:15:38,896] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=nioEventLoopGroup-3-7, requestId=6fa5610a-3d0f-4157-a4e1-a0f1ba2c6896 - Fetching plugins by params: {workspaceId=[6614aaf6cb467e382bcada2f]} for org: 1030741583's apps
[2024-04-09 03:15:38,927] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=nioEventLoopGroup-3-7, requestId=6fa5610a-3d0f-4157-a4e1-a0f1ba2c6896 - Error loading templates metadata in plugin restapi-plugin
[2024-04-09 03:15:38,944] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=nioEventLoopGroup-3-7, requestId=6fa5610a-3d0f-4157-a4e1-a0f1ba2c6896 - Error loading templates metadata in plugin elasticsearch-plugin
[2024-04-09 03:15:38,948] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=nioEventLoopGroup-3-7, requestId=6fa5610a-3d0f-4157-a4e1-a0f1ba2c6896 - Error loading templates metadata in plugin dynamo-plugin
[2024-04-09 03:15:38,950] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=nioEventLoopGroup-3-7, requestId=6fa5610a-3d0f-4157-a4e1-a0f1ba2c6896 - Error loading templates metadata in plugin redis-plugin
[2024-04-09 03:15:38,958] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=nioEventLoopGroup-3-7, requestId=6fa5610a-3d0f-4157-a4e1-a0f1ba2c6896 - Error loading templates metadata in plugin firestore-plugin
[2024-04-09 03:15:38,960] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=nioEventLoopGroup-3-7, requestId=6fa5610a-3d0f-4157-a4e1-a0f1ba2c6896 - Error loading templates metadata in plugin redshift-plugin
[2024-04-09 03:15:38,963] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=nioEventLoopGroup-3-7, requestId=6fa5610a-3d0f-4157-a4e1-a0f1ba2c6896 - Error loading templates metadata in plugin amazons3-plugin
[2024-04-09 03:15:38,966] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=nioEventLoopGroup-3-7, requestId=6fa5610a-3d0f-4157-a4e1-a0f1ba2c6896 - Error loading templates metadata in plugin google-sheets-plugin
[2024-04-09 03:15:38,973] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=nioEventLoopGroup-3-7, requestId=6fa5610a-3d0f-4157-a4e1-a0f1ba2c6896 - Error loading templates metadata in plugin js-plugin
[2024-04-09 03:15:38,975] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=nioEventLoopGroup-3-7, requestId=6fa5610a-3d0f-4157-a4e1-a0f1ba2c6896 - Error loading templates metadata in plugin smtp-plugin
[2024-04-09 03:15:38,979] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=nioEventLoopGroup-3-7, requestId=6fa5610a-3d0f-4157-a4e1-a0f1ba2c6896 - Error loading templates metadata in plugin graphql-plugin
[2024-04-09 03:15:38,985] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=nioEventLoopGroup-3-7, requestId=6fa5610a-3d0f-4157-a4e1-a0f1ba2c6896 - Error loading templates metadata in plugin openai-plugin
[2024-04-09 03:15:38,987] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=nioEventLoopGroup-3-7, requestId=6fa5610a-3d0f-4157-a4e1-a0f1ba2c6896 - Error loading templates metadata in plugin anthropic-plugin
[2024-04-09 03:15:38,989] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=nioEventLoopGroup-3-7, requestId=6fa5610a-3d0f-4157-a4e1-a0f1ba2c6896 - Error loading templates metadata in plugin googleai-plugin
[2024-04-09 03:15:38,992] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=nioEventLoopGroup-3-7, requestId=6fa5610a-3d0f-4157-a4e1-a0f1ba2c6896 - Error loading templates metadata in plugin databricks-plugin
[2024-04-09 03:15:38,995] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=nioEventLoopGroup-3-7, requestId=6fa5610a-3d0f-4157-a4e1-a0f1ba2c6896 - Error loading templates metadata in plugin aws-lambda-plugin
[2024-04-09 03:15:38,999] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=nioEventLoopGroup-3-7, requestId=6fa5610a-3d0f-4157-a4e1-a0f1ba2c6896 - Error loading templates metadata in plugin appsmithai-plugin
[2024-04-09 03:15:39,007] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=nioEventLoopGroup-3-7, requestId=6fa5610a-3d0f-4157-a4e1-a0f1ba2c6896 - Error loading templates metadata in plugin saas-plugin
[2024-04-09 03:15:39,011] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=nioEventLoopGroup-3-7, requestId=6fa5610a-3d0f-4157-a4e1-a0f1ba2c6896 - Error loading templates metadata in plugin saas-plugin
[2024-04-09 03:15:39,014] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=nioEventLoopGroup-3-7, requestId=6fa5610a-3d0f-4157-a4e1-a0f1ba2c6896 - Error loading templates metadata in plugin saas-plugin
[2024-04-09 03:15:40,421] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=reactor-http-epoll-1, requestId=418d87b8-30f8-4a04-a38e-117c98f9fd15 - Going to get snapshot with application id: 6614aaf6cb467e382bcada34, branch: null
[2024-04-09 03:15:41,886] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=lettuce-epollEventLoop-10-1, requestId=a89412a9-4c60-4f3c-a0d9-1444b68a3705 - Going to get resource from base controller for id: 6614aaf6cb467e382bcada2f
[2024-04-09 03:16:08,766] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=reactor-http-epoll-4, requestId=ef0c6135-6673-4e09-9448-cb9763cccc1a - update layout received for page 6614aaf6cb467e382bcada37
[2024-04-09 03:16:10,049] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=reactor-http-epoll-4, requestId=800033e6-db4e-4cca-a777-c5e1ca270906 - update layout received for page 6614aaf6cb467e382bcada37
[2024-04-09 03:16:13,571] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=reactor-http-epoll-4, requestId=880874f2-4608-4d41-aac3-547d73047d7a - onSubscribe(MonoUsingWhen.ResourceSubscriber)
[2024-04-09 03:16:13,574] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=reactor-http-epoll-4, requestId=880874f2-4608-4d41-aac3-547d73047d7a - request(unbounded)
[2024-04-09 03:16:13,587] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=nioEventLoopGroup-3-5, requestId=880874f2-4608-4d41-aac3-547d73047d7a - onNext(Workspace(domain=null, name=1030741583's apps, website=null, email=<EMAIL>, plugins=[WorkspacePlugin(pluginId=6614a753cb467e382bcada1e, status=FREE), WorkspacePlugin(pluginId=6614a74dcb467e382bcad9f4, status=FREE), WorkspacePlugin(pluginId=6614a74fcb467e382bcad9fc, status=FREE), WorkspacePlugin(pluginId=6614a74fcb467e382bcada00, status=FREE), WorkspacePlugin(pluginId=6614a74fcb467e382bcad9fd, status=FREE), WorkspacePlugin(pluginId=6614a750cb467e382bcada06, status=FREE), WorkspacePlugin(pluginId=6614a753cb467e382bcada1c, status=FREE), WorkspacePlugin(pluginId=6614a750cb467e382bcada05, status=FREE), WorkspacePlugin(pluginId=6614a753cb467e382bcada1d, status=FREE), WorkspacePlugin(pluginId=6614a74fcb467e382bcad9fe, status=FREE), WorkspacePlugin(pluginId=6614a74fcb467e382bcada04, status=FREE), WorkspacePlugin(pluginId=6614a752cb467e382bcada17, status=FREE), WorkspacePlugin(pluginId=6614a775cb467e382bcada1f, status=FREE), WorkspacePlugin(pluginId=6614a74fcb467e382bcada03, status=FREE), WorkspacePlugin(pluginId=6614a74fcb467e382bcad9fa, status=FREE), WorkspacePlugin(pluginId=6614a775cb467e382bcada20, status=FREE), WorkspacePlugin(pluginId=6614a74fcb467e382bcad9fb, status=FREE), WorkspacePlugin(pluginId=6614a74fcb467e382bcada01, status=FREE), WorkspacePlugin(pluginId=6614a775cb467e382bcada21, status=FREE), WorkspacePlugin(pluginId=6614a753cb467e382bcada19, status=FREE), WorkspacePlugin(pluginId=6614a74fcb467e382bcad9f7, status=FREE), WorkspacePlugin(pluginId=6614a752cb467e382bcada16, status=FREE), WorkspacePlugin(pluginId=6614a74fcb467e382bcad9ff, status=FREE), WorkspacePlugin(pluginId=6614a74dcb467e382bcad9f6, status=FREE), WorkspacePlugin(pluginId=6614a753cb467e382bcada1b, status=FREE), WorkspacePlugin(pluginId=6614a74dcb467e382bcad9f5, status=FREE), WorkspacePlugin(pluginId=6614a753cb467e382bcada1a, status=FREE)], slug=1030741583-s-apps, isAutoGeneratedWorkspace=true, logoAssetId=null, tenantId=6614a751cb467e382bcada07, hasEnvironments=null, defaultPermissionGroups=[6614aaf6cb467e382bcada31, 6614aaf6cb467e382bcada32, 6614aaf6cb467e382bcada30]))
[2024-04-09 03:16:13,624] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=nioEventLoopGroup-3-5, requestId=880874f2-4608-4d41-aac3-547d73047d7a - onComplete()
[2024-04-09 03:16:13,891] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=lettuce-epollEventLoop-10-1, requestId=7de4a17d-5d37-4579-8d80-f4061f269701 - Going to get structure for datasource with id: '6614b2fd8812dc3523a96de5'.
[2024-04-09 03:16:13,938] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=lettuce-epollEventLoop-10-1, requestId=29820ee1-b843-488b-ae2d-d3e7bc9770aa - Going to get all resources from datasource controller {workspaceId=[6614aaf6cb467e382bcada2f]}
[2024-04-09 03:16:13,977] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=lettuce-epollEventLoop-10-1, requestId=deeb0cd6-1b42-4b27-bbdf-6f2eff65636b - Going to get all resources from base controller {workspaceId=[6614aaf6cb467e382bcada2f]}
[2024-04-09 03:16:14,051] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=nioEventLoopGroup-3-5, requestId=7de4a17d-5d37-4579-8d80-f4061f269701 - nioEventLoopGroup-3-5: Datasource context doesn't exist. Creating connection.
[2024-04-09 03:16:14,058] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=nioEventLoopGroup-3-4, requestId=deeb0cd6-1b42-4b27-bbdf-6f2eff65636b - Fetching plugins by params: {workspaceId=[6614aaf6cb467e382bcada2f]} for org: 1030741583's apps
[2024-04-09 03:16:21,857] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=lettuce-epollEventLoop-10-1, requestId=ba89a3df-1cff-418e-94fe-076d8f43f33c - Going to get structure for datasource with id: '6614b2fd8812dc3523a96de5'.
[2024-04-09 03:16:21,903]  - Resource context exists. Returning the same.
[2024-04-09 03:16:22,626] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=lettuce-epollEventLoop-10-1, requestId=9fbee806-08d1-47f4-8da7-936c43abe36c - Going to get structure for datasource with id: '6614b2fd8812dc3523a96de5'.
[2024-04-09 03:16:22,659]  - Resource context exists. Returning the same.
[2024-04-09 03:16:23,217] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=reactor-http-epoll-1, requestId=c0d17479-1d7c-49f0-a010-81ecff51bf88 - update layout received for page 6614aaf6cb467e382bcada37
[2024-04-09 03:16:25,861] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=reactor-http-epoll-4, requestId=07d6649c-2722-4deb-89ef-f9284d1db545 - Going to get schema preview data for datasource with id: '6614b2fd8812dc3523a96de5'.
[2024-04-09 03:16:25,890]  - Resource context exists. Returning the same.
[2024-04-09 03:16:29,975] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=lettuce-epollEventLoop-10-1, requestId=a59b37ac-4f36-47f8-88e8-de6b151785ee - Going to get all applications by workspace id 6614aaf6cb467e382bcada2f
[2024-04-09 03:16:31,639] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=lettuce-epollEventLoop-10-1, requestId=2a92456a-9e52-43e3-aa0d-8933ff2e1828 - Going to fetch consolidatedAPI response for applicationId: null, defaultPageId: 6614aaf6cb467e382bcada37, branchName: null, mode: EDIT
[2024-04-09 03:16:31,674] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=nioEventLoopGroup-3-5, requestId=2a92456a-9e52-43e3-aa0d-8933ff2e1828 - Retrieved possible application ids for page, picking the appropriate one now
[2024-04-09 03:16:31,698] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=nioEventLoopGroup-3-4, requestId=2a92456a-9e52-43e3-aa0d-8933ff2e1828 - Fetched application data for id: 6614aaf6cb467e382bcada34
[2024-04-09 03:16:31,753] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=nioEventLoopGroup-3-5, requestId=2a92456a-9e52-43e3-aa0d-8933ff2e1828 - Retrieved Page DTOs from DB ...
[2024-04-09 03:16:31,754] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=nioEventLoopGroup-3-5, requestId=2a92456a-9e52-43e3-aa0d-8933ff2e1828 - Populating applicationPagesDTO ...
[2024-04-09 03:16:31,772] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=nioEventLoopGroup-3-6, requestId=2a92456a-9e52-43e3-aa0d-8933ff2e1828 - Fetching plugins by params: {workspaceId=[6614aaf6cb467e382bcada2f]} for org: 1030741583's apps
[2024-04-09 03:16:33,419] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=lettuce-epollEventLoop-10-1, requestId=196ce9eb-8ca6-46bc-9eef-27fd8b3b9eee - Going to get snapshot with application id: 6614aaf6cb467e382bcada34, branch: null
[2024-04-09 03:16:34,374] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=lettuce-epollEventLoop-10-1, requestId=1072dff7-9028-4d60-887f-5db19cc64ae9 - Going to get structure for datasource with id: '6614b2fd8812dc3523a96de5'.
[2024-04-09 03:16:34,416]  - Resource context exists. Returning the same.
[2024-04-09 03:16:34,743] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=lettuce-epollEventLoop-10-1, requestId=7bbd4590-60ce-4ce4-88d7-0d663f9a92f8 - Going to get resource from base controller for id: 6614aaf6cb467e382bcada2f
[2024-04-09 03:16:40,539] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=reactor-http-epoll-4, requestId=afed3bd6-4f42-4c5c-9cc7-dde28a6d66b9 - update layout received for page 6614aaf6cb467e382bcada37
[2024-04-09 03:16:45,246] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=reactor-http-epoll-4, requestId=de8e5cbc-bcce-4f8a-8057-ca8c7d8a0241 - update layout received for page 6614aaf6cb467e382bcada37
[2024-04-09 03:16:53,906] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=reactor-http-epoll-4, requestId=3f1ecd81-07f1-49cb-964d-2d76bb7b20f5 - update layout received for page 6614aaf6cb467e382bcada37
[2024-04-09 03:17:00,970] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=reactor-http-epoll-4, requestId=2f093181-20fe-46a9-b754-3df18c1ba980 - onSubscribe(MonoUsingWhen.ResourceSubscriber)
[2024-04-09 03:17:00,970] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=reactor-http-epoll-4, requestId=2f093181-20fe-46a9-b754-3df18c1ba980 - request(unbounded)
[2024-04-09 03:17:00,986] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=nioEventLoopGroup-3-4, requestId=2f093181-20fe-46a9-b754-3df18c1ba980 - onNext(Workspace(domain=null, name=1030741583's apps, website=null, email=<EMAIL>, plugins=[WorkspacePlugin(pluginId=6614a753cb467e382bcada1e, status=FREE), WorkspacePlugin(pluginId=6614a74dcb467e382bcad9f4, status=FREE), WorkspacePlugin(pluginId=6614a74fcb467e382bcad9fc, status=FREE), WorkspacePlugin(pluginId=6614a74fcb467e382bcada00, status=FREE), WorkspacePlugin(pluginId=6614a74fcb467e382bcad9fd, status=FREE), WorkspacePlugin(pluginId=6614a750cb467e382bcada06, status=FREE), WorkspacePlugin(pluginId=6614a753cb467e382bcada1c, status=FREE), WorkspacePlugin(pluginId=6614a750cb467e382bcada05, status=FREE), WorkspacePlugin(pluginId=6614a753cb467e382bcada1d, status=FREE), WorkspacePlugin(pluginId=6614a74fcb467e382bcad9fe, status=FREE), WorkspacePlugin(pluginId=6614a74fcb467e382bcada04, status=FREE), WorkspacePlugin(pluginId=6614a752cb467e382bcada17, status=FREE), WorkspacePlugin(pluginId=6614a775cb467e382bcada1f, status=FREE), WorkspacePlugin(pluginId=6614a74fcb467e382bcada03, status=FREE), WorkspacePlugin(pluginId=6614a74fcb467e382bcad9fa, status=FREE), WorkspacePlugin(pluginId=6614a775cb467e382bcada20, status=FREE), WorkspacePlugin(pluginId=6614a74fcb467e382bcad9fb, status=FREE), WorkspacePlugin(pluginId=6614a74fcb467e382bcada01, status=FREE), WorkspacePlugin(pluginId=6614a775cb467e382bcada21, status=FREE), WorkspacePlugin(pluginId=6614a753cb467e382bcada19, status=FREE), WorkspacePlugin(pluginId=6614a74fcb467e382bcad9f7, status=FREE), WorkspacePlugin(pluginId=6614a752cb467e382bcada16, status=FREE), WorkspacePlugin(pluginId=6614a74fcb467e382bcad9ff, status=FREE), WorkspacePlugin(pluginId=6614a74dcb467e382bcad9f6, status=FREE), WorkspacePlugin(pluginId=6614a753cb467e382bcada1b, status=FREE), WorkspacePlugin(pluginId=6614a74dcb467e382bcad9f5, status=FREE), WorkspacePlugin(pluginId=6614a753cb467e382bcada1a, status=FREE)], slug=1030741583-s-apps, isAutoGeneratedWorkspace=true, logoAssetId=null, tenantId=6614a751cb467e382bcada07, hasEnvironments=null, defaultPermissionGroups=[6614aaf6cb467e382bcada31, 6614aaf6cb467e382bcada32, 6614aaf6cb467e382bcada30]))
[2024-04-09 03:17:00,991] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=nioEventLoopGroup-3-4, requestId=2f093181-20fe-46a9-b754-3df18c1ba980 - onComplete()
[2024-04-09 03:17:01,122] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=lettuce-epollEventLoop-10-1, requestId=d30e4ee0-1baf-4f32-9d50-ceff37d44245 - Going to get all resources from datasource controller {workspaceId=[6614aaf6cb467e382bcada2f]}
[2024-04-09 03:17:01,146] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=lettuce-epollEventLoop-10-1, requestId=cf4b2db1-da28-4b61-bb47-b04fc2c03ac3 - Going to get structure for datasource with id: '6614b32d8812dc3523a96dea'.
[2024-04-09 03:17:01,157] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=lettuce-epollEventLoop-10-1, requestId=8e4a03f7-3006-48eb-8cee-5b5b3f6937db - Going to get all resources from base controller {workspaceId=[6614aaf6cb467e382bcada2f]}
[2024-04-09 03:17:01,179] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=nioEventLoopGroup-3-6, requestId=8e4a03f7-3006-48eb-8cee-5b5b3f6937db - Fetching plugins by params: {workspaceId=[6614aaf6cb467e382bcada2f]} for org: 1030741583's apps
[2024-04-09 03:17:01,207] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=lettuce-epollEventLoop-10-1, requestId=cf4b2db1-da28-4b61-bb47-b04fc2c03ac3 - lettuce-epollEventLoop-10-1: Datasource context doesn't exist. Creating connection.
[2024-04-09 03:17:01,281] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=lettuce-epollEventLoop-10-1, requestId=2014e1d5-baa7-4d9c-8593-684a8b2dfd9e - Going to get structure for datasource with id: '6614b2fd8812dc3523a96de5'.
[2024-04-09 03:17:01,316]  - Resource context exists. Returning the same.
[2024-04-09 03:17:01,626] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=boundedElastic-5, requestId=cf4b2db1-da28-4b61-bb47-b04fc2c03ac3 - MongoClient with metadata {"driver": {"name": "mongo-java-driver|reactive-streams", "version": "4.8.2"}, "os": {"type": "Linux", "name": "Linux", "architecture": "amd64", "version": "5.15.49-linuxkit"}, "platform": "Java/Eclipse Adoptium/17.0.9+9"} created with settings MongoClientSettings{readPreference=primary, writeConcern=WriteConcern{w=majority, wTimeout=null ms, journal=null}, retryWrites=true, retryReads=true, readConcern=ReadConcern{level=null}, credential=MongoCredential{mechanism=null, userName='mockdb-admin', source='admin', password=<hidden>, mechanismProperties=<hidden>}, streamFactoryFactory=null, commandListeners=[], codecRegistry=ProvidersCodecRegistry{codecProviders=[ValueCodecProvider{}, BsonValueCodecProvider{}, DBRefCodecProvider{}, DBObjectCodecProvider{}, DocumentCodecProvider{}, CollectionCodecProvider{}, IterableCodecProvider{}, MapCodecProvider{}, GeoJsonCodecProvider{}, GridFSFileCodecProvider{}, Jsr310CodecProvider{}, JsonObjectCodecProvider{}, BsonCodecProvider{}, EnumCodecProvider{}, com.mongodb.Jep395RecordCodecProvider@4647881c]}, clusterSettings={hosts=[127.0.0.1:27017], srvHost=mockdb.kce5o.mongodb.net, srvServiceName=mongodb, mode=MULTIPLE, requiredClusterType=REPLICA_SET, requiredReplicaSetName='atlas-12isch-shard-0', serverSelector='null', clusterListeners='[]', serverSelectionTimeout='30000 ms', localThreshold='30000 ms'}, socketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=0, receiveBufferSize=0, sendBufferSize=0}, heartbeatSocketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=10000, receiveBufferSize=0, sendBufferSize=0}, connectionPoolSettings=ConnectionPoolSettings{maxSize=100, minSize=0, maxWaitTimeMS=120000, maxConnectionLifeTimeMS=0, maxConnectionIdleTimeMS=0, maintenanceInitialDelayMS=0, maintenanceFrequencyMS=60000, connectionPoolListeners=[], maxConnecting=2}, serverSettings=ServerSettings{heartbeatFrequencyMS=10000, minHeartbeatFrequencyMS=500, serverListeners='[]', serverMonitorListeners='[]'}, sslSettings=SslSettings{enabled=true, invalidHostNameAllowed=false, context=null}, applicationName='null', compressorList=[], uuidRepresentation=UNSPECIFIED, serverApi=null, autoEncryptionSettings=null, contextProvider=null}
[2024-04-09 03:17:01,650] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=boundedElastic-7, requestId=cf4b2db1-da28-4b61-bb47-b04fc2c03ac3 - No server chosen by com.mongodb.reactivestreams.client.internal.ClientSessionHelper$$Lambda$3301/0x00007f567ce1cc40@745dbdc5 from cluster description ClusterDescription{type=UNKNOWN, connectionMode=MULTIPLE, serverDescriptions=[]}. Waiting for 30000 ms before timing out
[2024-04-09 03:17:02,284]  - Adding discovered server mockdb-shard-00-01.kce5o.mongodb.net:27017 to client view of cluster
[2024-04-09 03:17:02,293]  - Adding discovered server mockdb-shard-00-00.kce5o.mongodb.net:27017 to client view of cluster
[2024-04-09 03:17:02,296]  - Adding discovered server mockdb-shard-00-02.kce5o.mongodb.net:27017 to client view of cluster
[2024-04-09 03:17:04,038]  - Monitor thread successfully connected to server with description ServerDescription{address=mockdb-shard-00-01.kce5o.mongodb.net:27017, type=REPLICA_SET_PRIMARY, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=13, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=981577367, setName='atlas-12isch-shard-0', canonicalAddress=mockdb-shard-00-01.kce5o.mongodb.net:27017, hosts=[mockdb-shard-00-02.kce5o.mongodb.net:27017, mockdb-shard-00-01.kce5o.mongodb.net:27017, mockdb-shard-00-00.kce5o.mongodb.net:27017], passives=[], arbiters=[], primary='mockdb-shard-00-01.kce5o.mongodb.net:27017', tagSet=TagSet{[Tag{name='availabilityZone', value='use2-az2'}, Tag{name='diskState', value='READY'}, Tag{name='nodeType', value='ELECTABLE'}, Tag{name='provider', value='AWS'}, Tag{name='region', value='US_EAST_2'}, Tag{name='workloadType', value='OPERATIONAL'}]}, electionId=7fffffff0000000000000053, setVersion=19, topologyVersion=TopologyVersion{processId=6605b3418dc1e1ac9bbaccbc, counter=6}, lastWriteDate=Tue Apr 09 03:17:00 UTC 2024, lastUpdateTimeNanos=6410817613067}
[2024-04-09 03:17:04,041]  - Discovered replica set primary mockdb-shard-00-01.kce5o.mongodb.net:27017 with max election id 7fffffff0000000000000053 and max set version 19
[2024-04-09 03:17:04,124]  - Monitor thread successfully connected to server with description ServerDescription{address=mockdb-shard-00-00.kce5o.mongodb.net:27017, type=REPLICA_SET_SECONDARY, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=13, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=957938552, setName='atlas-12isch-shard-0', canonicalAddress=mockdb-shard-00-00.kce5o.mongodb.net:27017, hosts=[mockdb-shard-00-02.kce5o.mongodb.net:27017, mockdb-shard-00-01.kce5o.mongodb.net:27017, mockdb-shard-00-00.kce5o.mongodb.net:27017], passives=[], arbiters=[], primary='mockdb-shard-00-01.kce5o.mongodb.net:27017', tagSet=TagSet{[Tag{name='availabilityZone', value='use2-az1'}, Tag{name='diskState', value='READY'}, Tag{name='nodeType', value='ELECTABLE'}, Tag{name='provider', value='AWS'}, Tag{name='region', value='US_EAST_2'}, Tag{name='workloadType', value='OPERATIONAL'}]}, electionId=null, setVersion=19, topologyVersion=TopologyVersion{processId=6605b2817f9bc0966b4768af, counter=5}, lastWriteDate=Tue Apr 09 03:17:00 UTC 2024, lastUpdateTimeNanos=6410904061702}
[2024-04-09 03:17:04,130]  - Monitor thread successfully connected to server with description ServerDescription{address=mockdb-shard-00-02.kce5o.mongodb.net:27017, type=REPLICA_SET_SECONDARY, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=13, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=968616134, setName='atlas-12isch-shard-0', canonicalAddress=mockdb-shard-00-02.kce5o.mongodb.net:27017, hosts=[mockdb-shard-00-02.kce5o.mongodb.net:27017, mockdb-shard-00-01.kce5o.mongodb.net:27017, mockdb-shard-00-00.kce5o.mongodb.net:27017], passives=[], arbiters=[], primary='mockdb-shard-00-01.kce5o.mongodb.net:27017', tagSet=TagSet{[Tag{name='availabilityZone', value='use2-az3'}, Tag{name='diskState', value='READY'}, Tag{name='nodeType', value='ELECTABLE'}, Tag{name='provider', value='AWS'}, Tag{name='region', value='US_EAST_2'}, Tag{name='workloadType', value='OPERATIONAL'}]}, electionId=null, setVersion=19, topologyVersion=TopologyVersion{processId=6605b40c8b733a65ff4f4738, counter=3}, lastWriteDate=Tue Apr 09 03:17:00 UTC 2024, lastUpdateTimeNanos=6410909840794}
[2024-04-09 03:17:04,741] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=lettuce-epollEventLoop-10-1, requestId=ee8b9ce7-fdc9-4d59-b5b0-28b143cae26e - Going to get structure for datasource with id: '6614b2fd8812dc3523a96de5'.
[2024-04-09 03:17:04,771]  - Resource context exists. Returning the same.
[2024-04-09 03:17:05,715] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=lettuce-epollEventLoop-10-1, requestId=26888e6d-888e-4738-b29f-aefac626516a - Going to get structure for datasource with id: '6614b32d8812dc3523a96dea'.
[2024-04-09 03:17:05,727]  - Resource context exists. Returning the same.
[2024-04-09 03:17:06,225] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=lettuce-epollEventLoop-10-1, requestId=7b8e4be9-a08a-4701-a2a0-d524917371e2 - Going to get structure for datasource with id: '6614b32d8812dc3523a96dea'.
[2024-04-09 03:17:06,248]  - Resource context exists. Returning the same.
[2024-04-09 03:17:10,032] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=reactor-http-epoll-1, requestId=705386a8-8ea4-4e30-9b72-67ea33ea3a9a - Going to create resource com.appsmith.external.models.ActionDTO
[2024-04-09 03:17:10,038] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=reactor-http-epoll-4, requestId=4f3fd23a-9f8c-4263-9304-bdc849dce3ea - Going to create resource com.appsmith.external.models.ActionDTO
[2024-04-09 03:17:10,171] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=nioEventLoopGroup-3-5, requestId=705386a8-8ea4-4e30-9b72-67ea33ea3a9a - Sanitizing the action for missing plugin type or plugin Id with action id: null 
[2024-04-09 03:17:10,264] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=nioEventLoopGroup-3-5, requestId=4f3fd23a-9f8c-4263-9304-bdc849dce3ea - Sanitizing the action for missing plugin type or plugin Id with action id: null 
[2024-04-09 03:17:10,340] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=nioEventLoopGroup-3-5, requestId=705386a8-8ea4-4e30-9b72-67ea33ea3a9a - Updating action DTO with default resources with action id: 6614b3368812dc3523a96df0 
[2024-04-09 03:17:10,354] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=nioEventLoopGroup-3-5, requestId=4f3fd23a-9f8c-4263-9304-bdc849dce3ea - Updating action DTO with default resources with action id: 6614b3368812dc3523a96df1 
[2024-04-09 03:17:10,403] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=lettuce-epollEventLoop-10-1, requestId=6778edae-e234-4911-84c9-b34652a7024d - Going to get all actions with params: {applicationId=[6614aaf6cb467e382bcada34]}, branch: null
[2024-04-09 03:17:10,450] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=nioEventLoopGroup-3-3, requestId=6778edae-e234-4911-84c9-b34652a7024d - Updating action DTO with default resources with action id: 6614b3368812dc3523a96df1 
[2024-04-09 03:17:10,453] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=nioEventLoopGroup-3-5, requestId=6778edae-e234-4911-84c9-b34652a7024d - Updating action DTO with default resources with action id: 6614b3368812dc3523a96df0 
[2024-04-09 03:17:10,721] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=reactor-http-epoll-4, requestId=f75fe8d0-f722-4de8-80d4-b52c6711818a - Going to find action based on branchName and defaultActionId with id: 6614b3368812dc3523a96df1 
[2024-04-09 03:17:10,784] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=nioEventLoopGroup-3-4, requestId=f75fe8d0-f722-4de8-80d4-b52c6711818a - [nioEventLoopGroup-3-4]Execute Action called in Page 6614aaf6cb467e382bcada37, for action id : 6614b3368812dc3523a96df1  action name : Select_public_users1
[2024-04-09 03:17:10,792]  - Resource context exists. Returning the same.
[2024-04-09 03:17:11,491] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=boundedElastic-7, requestId=f75fe8d0-f722-4de8-80d4-b52c6711818a - boundedElastic-7: Action Select_public_users1 with id 6614b3368812dc3523a96df1 execution time : 700 ms
[2024-04-09 03:17:11,648] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=reactor-http-epoll-4, requestId=fc15ba18-1a0b-44be-a641-34130f8eab14 - Going to find action based on branchName and defaultActionId with id: 6614b3368812dc3523a96df0 
[2024-04-09 03:17:11,688] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=nioEventLoopGroup-3-3, requestId=fc15ba18-1a0b-44be-a641-34130f8eab14 - [nioEventLoopGroup-3-3]Execute Action called in Page 6614aaf6cb467e382bcada37, for action id : 6614b3368812dc3523a96df0  action name : Total_record_public_users1
[2024-04-09 03:17:11,688]  - Resource context exists. Returning the same.
[2024-04-09 03:17:11,902] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=boundedElastic-7, requestId=fc15ba18-1a0b-44be-a641-34130f8eab14 - boundedElastic-7: Action Total_record_public_users1 with id 6614b3368812dc3523a96df0 execution time : 214 ms
[2024-04-09 03:17:12,168] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=lettuce-epollEventLoop-10-1, requestId=5601fd5c-46d3-4678-9172-6eddec5d7b12 - Going to create resource com.appsmith.external.models.ActionDTO
[2024-04-09 03:17:12,180] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=reactor-http-epoll-4, requestId=cda76720-c915-472f-aafd-6674796cd50d - Going to create resource com.appsmith.external.models.ActionDTO
[2024-04-09 03:17:12,230] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=nioEventLoopGroup-3-5, requestId=5601fd5c-46d3-4678-9172-6eddec5d7b12 - Sanitizing the action for missing plugin type or plugin Id with action id: null 
[2024-04-09 03:17:12,275] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=nioEventLoopGroup-3-3, requestId=cda76720-c915-472f-aafd-6674796cd50d - Sanitizing the action for missing plugin type or plugin Id with action id: null 
[2024-04-09 03:17:12,346] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=nioEventLoopGroup-3-3, requestId=5601fd5c-46d3-4678-9172-6eddec5d7b12 - Updating action DTO with default resources with action id: 6614b3388812dc3523a96df4 
[2024-04-09 03:17:12,364] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=nioEventLoopGroup-3-5, requestId=cda76720-c915-472f-aafd-6674796cd50d - Updating action DTO with default resources with action id: 6614b3388812dc3523a96df5 
[2024-04-09 03:17:12,708] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=reactor-http-epoll-4, requestId=b2e9a573-42c4-426e-ad9f-00aff811ab22 - Going to get all actions with params: {applicationId=[6614aaf6cb467e382bcada34]}, branch: null
[2024-04-09 03:17:12,753] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=nioEventLoopGroup-3-7, requestId=b2e9a573-42c4-426e-ad9f-00aff811ab22 - Updating action DTO with default resources with action id: 6614b3368812dc3523a96df1 
[2024-04-09 03:17:12,764] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=nioEventLoopGroup-3-3, requestId=b2e9a573-42c4-426e-ad9f-00aff811ab22 - Updating action DTO with default resources with action id: 6614b3388812dc3523a96df4 
[2024-04-09 03:17:12,775] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=nioEventLoopGroup-3-3, requestId=b2e9a573-42c4-426e-ad9f-00aff811ab22 - Updating action DTO with default resources with action id: 6614b3388812dc3523a96df5 
[2024-04-09 03:17:12,777] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=nioEventLoopGroup-3-5, requestId=b2e9a573-42c4-426e-ad9f-00aff811ab22 - Updating action DTO with default resources with action id: 6614b3368812dc3523a96df0 
[2024-04-09 03:17:14,302] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=reactor-http-epoll-4, requestId=a91092e8-bc00-44bc-a6f8-597b2a16cea3 - update layout received for page 6614aaf6cb467e382bcada37
[2024-04-09 03:17:14,540] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=nioEventLoopGroup-3-5, requestId=a91092e8-bc00-44bc-a6f8-597b2a16cea3 - Updating unpublished action with action id: 6614b3368812dc3523a96df0 and id: 6614b3368812dc3523a96df0 
[2024-04-09 03:17:14,541] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=nioEventLoopGroup-3-5, requestId=a91092e8-bc00-44bc-a6f8-597b2a16cea3 - Updating unpublished action without analytics with action id: 6614b3368812dc3523a96df0 
[2024-04-09 03:17:14,547] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=nioEventLoopGroup-3-5, requestId=a91092e8-bc00-44bc-a6f8-597b2a16cea3 - Updating unpublished action with action id: 6614b3368812dc3523a96df1 and id: 6614b3368812dc3523a96df1 
[2024-04-09 03:17:14,547] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=nioEventLoopGroup-3-5, requestId=a91092e8-bc00-44bc-a6f8-597b2a16cea3 - Updating unpublished action without analytics with action id: 6614b3368812dc3523a96df1 
[2024-04-09 03:17:21,523] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=reactor-http-epoll-4, requestId=5be555ba-2f04-4036-b7b8-1d26e8fba430 - update layout received for page 6614aaf6cb467e382bcada37
[2024-04-09 03:17:29,181] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=reactor-http-epoll-4, requestId=c9f3c177-2ddb-4798-8515-01493ec59039 - update layout received for page 6614aaf6cb467e382bcada37
[2024-04-09 03:17:41,079] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=reactor-http-epoll-4, requestId=93548658-6c16-4a0b-a09e-0757e6586fa6 - Going to get schema preview data for datasource with id: '6614b32d8812dc3523a96dea'.
[2024-04-09 03:17:41,102]  - Resource context exists. Returning the same.
[2024-04-09 03:18:14,937] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=reactor-http-epoll-4, requestId=0b0b4a67-f5f2-4f3f-bd59-20deb5bdea81 - Going to find action based on branchName and defaultActionId with id: 6614b3368812dc3523a96df1 
[2024-04-09 03:18:14,977] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=nioEventLoopGroup-3-7, requestId=0b0b4a67-f5f2-4f3f-bd59-20deb5bdea81 - [nioEventLoopGroup-3-7]Execute Action called in Page 6614aaf6cb467e382bcada37, for action id : 6614b3368812dc3523a96df1  action name : Select_public_users1
[2024-04-09 03:18:14,980]  - Resource context exists. Returning the same.
[2024-04-09 03:18:14,985] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=reactor-http-epoll-1, requestId=d65ed2ed-a413-4990-8210-c49218579995 - Going to find action based on branchName and defaultActionId with id: 6614b3368812dc3523a96df0 
[2024-04-09 03:18:15,016] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=nioEventLoopGroup-3-5, requestId=d65ed2ed-a413-4990-8210-c49218579995 - [nioEventLoopGroup-3-5]Execute Action called in Page 6614aaf6cb467e382bcada37, for action id : 6614b3368812dc3523a96df0  action name : Total_record_public_users1
[2024-04-09 03:18:15,017]  - Resource context exists. Returning the same.
[2024-04-09 03:18:17,717] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=boundedElastic-7, requestId=0b0b4a67-f5f2-4f3f-bd59-20deb5bdea81 - boundedElastic-7: Action Select_public_users1 with id 6614b3368812dc3523a96df1 execution time : 2739 ms
[2024-04-09 03:18:17,933] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=boundedElastic-4, requestId=d65ed2ed-a413-4990-8210-c49218579995 - boundedElastic-4: Action Total_record_public_users1 with id 6614b3368812dc3523a96df0 execution time : 2917 ms
[2024-04-09 03:18:22,367] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=nioEventLoopGroup-3-7, requestId=a0da5d17-d918-4ce6-9947-437a2ec7a995 - Published application 6614aaf6cb467e382bcada34 in 124 ms
[2024-04-09 03:18:22,652] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=lettuce-epollEventLoop-10-1, requestId=14d70c0b-0d3a-4adb-aeb2-a0d55ef29f99 - Going to fetch applicationPageDTO for applicationId: null, pageId: 6614aaf6cb467e382bcada37, branchName: null, mode: EDIT
[2024-04-09 03:18:22,669] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=nioEventLoopGroup-3-7, requestId=14d70c0b-0d3a-4adb-aeb2-a0d55ef29f99 - Retrieved possible application ids for page, picking the appropriate one now
[2024-04-09 03:18:22,686] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=nioEventLoopGroup-3-7, requestId=14d70c0b-0d3a-4adb-aeb2-a0d55ef29f99 - Fetched application data for id: 6614aaf6cb467e382bcada34
[2024-04-09 03:18:22,717] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=nioEventLoopGroup-3-7, requestId=14d70c0b-0d3a-4adb-aeb2-a0d55ef29f99 - Retrieved Page DTOs from DB ...
[2024-04-09 03:18:22,717] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=nioEventLoopGroup-3-7, requestId=14d70c0b-0d3a-4adb-aeb2-a0d55ef29f99 - Populating applicationPagesDTO ...
[2024-04-09 03:18:23,478] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=lettuce-epollEventLoop-10-1, requestId=d484cd7d-2007-4587-a4d7-b02ea6012365 - Going to fetch consolidatedAPI response for applicationId: null, defaultPageId: 6614aaf6cb467e382bcada37, branchName: null, mode: PUBLISHED
[2024-04-09 03:18:23,514] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=nioEventLoopGroup-3-5, requestId=d484cd7d-2007-4587-a4d7-b02ea6012365 - Retrieved possible application ids for page, picking the appropriate one now
[2024-04-09 03:18:23,536] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=nioEventLoopGroup-3-7, requestId=d484cd7d-2007-4587-a4d7-b02ea6012365 - Fetched application data for id: 6614aaf6cb467e382bcada34
[2024-04-09 03:18:23,583] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=nioEventLoopGroup-3-6, requestId=d484cd7d-2007-4587-a4d7-b02ea6012365 - Retrieved Page DTOs from DB ...
[2024-04-09 03:18:23,587] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=nioEventLoopGroup-3-6, requestId=d484cd7d-2007-4587-a4d7-b02ea6012365 - Populating applicationPagesDTO ...
[2024-04-09 03:18:26,461] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=reactor-http-epoll-1, requestId=a121851a-3dae-4f1d-9263-1d4a01623093 - Going to find action based on branchName and defaultActionId with id: 6614b3368812dc3523a96df1 
[2024-04-09 03:18:26,518] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=nioEventLoopGroup-3-6, requestId=a121851a-3dae-4f1d-9263-1d4a01623093 - [nioEventLoopGroup-3-6]Execute Action called in Page 6614aaf6cb467e382bcada37, for action id : 6614b3368812dc3523a96df1  action name : Select_public_users1
[2024-04-09 03:18:26,519]  - Resource context exists. Returning the same.
[2024-04-09 03:18:26,954] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=boundedElastic-4, requestId=a121851a-3dae-4f1d-9263-1d4a01623093 - boundedElastic-4: Action Select_public_users1 with id 6614b3368812dc3523a96df1 execution time : 435 ms
[2024-04-09 03:18:27,506] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=reactor-http-epoll-1, requestId=8bf51247-e5ff-4dc4-9f22-d4bb0eb3c550 - Going to find action based on branchName and defaultActionId with id: 6614b3368812dc3523a96df0 
[2024-04-09 03:18:27,540] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=nioEventLoopGroup-3-6, requestId=8bf51247-e5ff-4dc4-9f22-d4bb0eb3c550 - [nioEventLoopGroup-3-6]Execute Action called in Page 6614aaf6cb467e382bcada37, for action id : 6614b3368812dc3523a96df0  action name : Total_record_public_users1
[2024-04-09 03:18:27,541]  - Resource context exists. Returning the same.
[2024-04-09 03:18:27,969] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=boundedElastic-4, requestId=8bf51247-e5ff-4dc4-9f22-d4bb0eb3c550 - boundedElastic-4: Action Total_record_public_users1 with id 6614b3368812dc3523a96df0 execution time : 427 ms
[2024-04-09 03:18:52,400] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=reactor-http-epoll-1, requestId=884ac4e6-dd2f-4a67-9410-e0edb0c0e4c2 - update layout received for page 6614aaf6cb467e382bcada37
[2024-04-09 03:20:03,812] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=reactor-http-epoll-1, requestId=f62a0e78-f3d3-4cbc-980b-5c85e379875a - update layout received for page 6614aaf6cb467e382bcada37
[2024-04-09 03:20:05,652] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=reactor-http-epoll-1, requestId=ba1ae8bd-80ae-4d6a-8dff-924f6001c7d0 - update layout received for page 6614aaf6cb467e382bcada37
[2024-04-09 03:20:10,940] userEmail=<EMAIL>, sessionId=7b01c693-b30b-4693-961b-b7058646d3d7, thread=reactor-http-epoll-1, requestId=3e96d91d-3029-431e-b56a-61ff1f098be3 - update layout received for page 6614aaf6cb467e382bcada37
[2024-04-09 03:41:35,212]  - Exception in monitor thread while connecting to server mockdb-shard-00-02.kce5o.mongodb.net:27017
com.mongodb.MongoSocketReadTimeoutException: Timeout while receiving message
	at com.mongodb.internal.connection.AsynchronousChannelStream$BasicCompletionHandler.failed(AsynchronousChannelStream.java:263)
	at com.mongodb.internal.connection.AsynchronousChannelStream$BasicCompletionHandler.failed(AsynchronousChannelStream.java:233)
	at com.mongodb.internal.connection.tlschannel.async.AsynchronousTlsChannel.lambda$read$6(AsynchronousTlsChannel.java:123)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.nio.channels.InterruptedByTimeoutException: null
	at com.mongodb.internal.connection.tlschannel.async.AsynchronousTlsChannelGroup.lambda$startRead$2(AsynchronousTlsChannelGroup.java:309)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	... 3 common frames omitted
[2024-04-09 03:41:35,341]  - Exception in monitor thread while connecting to server mockdb-shard-00-01.kce5o.mongodb.net:27017
com.mongodb.MongoSocketReadTimeoutException: Timeout while receiving message
	at com.mongodb.internal.connection.AsynchronousChannelStream$BasicCompletionHandler.failed(AsynchronousChannelStream.java:263)
	at com.mongodb.internal.connection.AsynchronousChannelStream$BasicCompletionHandler.failed(AsynchronousChannelStream.java:233)
	at com.mongodb.internal.connection.tlschannel.async.AsynchronousTlsChannel.lambda$read$6(AsynchronousTlsChannel.java:123)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.nio.channels.InterruptedByTimeoutException: null
	at com.mongodb.internal.connection.tlschannel.async.AsynchronousTlsChannelGroup.lambda$startRead$2(AsynchronousTlsChannelGroup.java:309)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	... 3 common frames omitted
[2024-04-09 03:41:35,577]  - Exception in monitor thread while connecting to server mockdb-shard-00-00.kce5o.mongodb.net:27017
com.mongodb.MongoSocketReadTimeoutException: Timeout while receiving message
	at com.mongodb.internal.connection.AsynchronousChannelStream$BasicCompletionHandler.failed(AsynchronousChannelStream.java:263)
	at com.mongodb.internal.connection.AsynchronousChannelStream$BasicCompletionHandler.failed(AsynchronousChannelStream.java:233)
	at com.mongodb.internal.connection.tlschannel.async.AsynchronousTlsChannel.lambda$read$6(AsynchronousTlsChannel.java:123)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.nio.channels.InterruptedByTimeoutException: null
	at com.mongodb.internal.connection.tlschannel.async.AsynchronousTlsChannelGroup.lambda$startRead$2(AsynchronousTlsChannelGroup.java:309)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	... 3 common frames omitted
[2024-04-09 03:41:36,535]  - Monitor thread successfully connected to server with description ServerDescription{address=mockdb-shard-00-01.kce5o.mongodb.net:27017, type=REPLICA_SET_PRIMARY, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=13, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=911703074, setName='atlas-12isch-shard-0', canonicalAddress=mockdb-shard-00-01.kce5o.mongodb.net:27017, hosts=[mockdb-shard-00-02.kce5o.mongodb.net:27017, mockdb-shard-00-01.kce5o.mongodb.net:27017, mockdb-shard-00-00.kce5o.mongodb.net:27017], passives=[], arbiters=[], primary='mockdb-shard-00-01.kce5o.mongodb.net:27017', tagSet=TagSet{[Tag{name='availabilityZone', value='use2-az2'}, Tag{name='diskState', value='READY'}, Tag{name='nodeType', value='ELECTABLE'}, Tag{name='provider', value='AWS'}, Tag{name='region', value='US_EAST_2'}, Tag{name='workloadType', value='OPERATIONAL'}]}, electionId=7fffffff0000000000000053, setVersion=19, topologyVersion=TopologyVersion{processId=6605b3418dc1e1ac9bbaccbc, counter=6}, lastWriteDate=Tue Apr 09 03:41:30 UTC 2024, lastUpdateTimeNanos=7884424442329}
[2024-04-09 03:41:36,536]  - Discovered replica set primary mockdb-shard-00-01.kce5o.mongodb.net:27017 with max election id 7fffffff0000000000000053 and max set version 19
[2024-04-09 03:41:36,808]  - Monitor thread successfully connected to server with description ServerDescription{address=mockdb-shard-00-00.kce5o.mongodb.net:27017, type=REPLICA_SET_SECONDARY, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=13, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=908753136, setName='atlas-12isch-shard-0', canonicalAddress=mockdb-shard-00-00.kce5o.mongodb.net:27017, hosts=[mockdb-shard-00-02.kce5o.mongodb.net:27017, mockdb-shard-00-01.kce5o.mongodb.net:27017, mockdb-shard-00-00.kce5o.mongodb.net:27017], passives=[], arbiters=[], primary='mockdb-shard-00-01.kce5o.mongodb.net:27017', tagSet=TagSet{[Tag{name='availabilityZone', value='use2-az1'}, Tag{name='diskState', value='READY'}, Tag{name='nodeType', value='ELECTABLE'}, Tag{name='provider', value='AWS'}, Tag{name='region', value='US_EAST_2'}, Tag{name='workloadType', value='OPERATIONAL'}]}, electionId=null, setVersion=19, topologyVersion=TopologyVersion{processId=6605b2817f9bc0966b4768af, counter=5}, lastWriteDate=Tue Apr 09 03:41:30 UTC 2024, lastUpdateTimeNanos=7884697497069}
[2024-04-09 03:41:37,054]  - Monitor thread successfully connected to server with description ServerDescription{address=mockdb-shard-00-02.kce5o.mongodb.net:27017, type=REPLICA_SET_SECONDARY, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=13, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=936470247, setName='atlas-12isch-shard-0', canonicalAddress=mockdb-shard-00-02.kce5o.mongodb.net:27017, hosts=[mockdb-shard-00-02.kce5o.mongodb.net:27017, mockdb-shard-00-01.kce5o.mongodb.net:27017, mockdb-shard-00-00.kce5o.mongodb.net:27017], passives=[], arbiters=[], primary='mockdb-shard-00-01.kce5o.mongodb.net:27017', tagSet=TagSet{[Tag{name='availabilityZone', value='use2-az3'}, Tag{name='diskState', value='READY'}, Tag{name='nodeType', value='ELECTABLE'}, Tag{name='provider', value='AWS'}, Tag{name='region', value='US_EAST_2'}, Tag{name='workloadType', value='OPERATIONAL'}]}, electionId=null, setVersion=19, topologyVersion=TopologyVersion{processId=6605b40c8b733a65ff4f4738, counter=3}, lastWriteDate=Tue Apr 09 03:41:30 UTC 2024, lastUpdateTimeNanos=7884943372837}
[2024-04-09 03:41:51,874]  - Fetching features for default tenant
[2024-04-09 03:53:57,043]  - Exception in monitor thread while connecting to server localhost:27017
com.mongodb.MongoSocketReadTimeoutException: Timeout while receiving message
	at com.mongodb.connection.netty.NettyStream$InboundBufferHandler.exceptionCaught(NettyStream.java:427)
	at io.netty.channel.AbstractChannelHandlerContext.invokeExceptionCaught(AbstractChannelHandlerContext.java:346)
	at io.netty.channel.AbstractChannelHandlerContext.invokeExceptionCaught(AbstractChannelHandlerContext.java:325)
	at io.netty.channel.AbstractChannelHandlerContext.fireExceptionCaught(AbstractChannelHandlerContext.java:317)
	at com.mongodb.connection.netty.NettyStream$ReadTimeoutTask.run(NettyStream.java:564)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:153)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:174)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:167)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:470)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: io.netty.handler.timeout.ReadTimeoutException: null
[2024-04-09 03:53:57,044]  - Exception in monitor thread while connecting to server localhost:27017
com.mongodb.MongoSocketReadTimeoutException: Timeout while receiving message
	at com.mongodb.connection.netty.NettyStream$InboundBufferHandler.exceptionCaught(NettyStream.java:427)
	at io.netty.channel.AbstractChannelHandlerContext.invokeExceptionCaught(AbstractChannelHandlerContext.java:346)
	at io.netty.channel.AbstractChannelHandlerContext.invokeExceptionCaught(AbstractChannelHandlerContext.java:325)
	at io.netty.channel.AbstractChannelHandlerContext.fireExceptionCaught(AbstractChannelHandlerContext.java:317)
	at com.mongodb.connection.netty.NettyStream$ReadTimeoutTask.run(NettyStream.java:564)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:153)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:174)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:167)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:470)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: io.netty.handler.timeout.ReadTimeoutException: null
[2024-04-09 03:53:57,118]  - Exception in monitor thread while connecting to server mockdb-shard-00-00.kce5o.mongodb.net:27017
com.mongodb.MongoSocketReadTimeoutException: Timeout while receiving message
	at com.mongodb.internal.connection.AsynchronousChannelStream$BasicCompletionHandler.failed(AsynchronousChannelStream.java:263)
	at com.mongodb.internal.connection.AsynchronousChannelStream$BasicCompletionHandler.failed(AsynchronousChannelStream.java:233)
	at com.mongodb.internal.connection.tlschannel.async.AsynchronousTlsChannel.lambda$read$6(AsynchronousTlsChannel.java:123)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.nio.channels.InterruptedByTimeoutException: null
	at com.mongodb.internal.connection.tlschannel.async.AsynchronousTlsChannelGroup.lambda$startRead$2(AsynchronousTlsChannelGroup.java:309)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	... 3 common frames omitted
[2024-04-09 03:53:57,117]  - Exception in monitor thread while connecting to server mockdb-shard-00-02.kce5o.mongodb.net:27017
com.mongodb.MongoSocketReadTimeoutException: Timeout while receiving message
	at com.mongodb.internal.connection.AsynchronousChannelStream$BasicCompletionHandler.failed(AsynchronousChannelStream.java:263)
	at com.mongodb.internal.connection.AsynchronousChannelStream$BasicCompletionHandler.failed(AsynchronousChannelStream.java:233)
	at com.mongodb.internal.connection.tlschannel.async.AsynchronousTlsChannel.lambda$read$6(AsynchronousTlsChannel.java:123)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.nio.channels.InterruptedByTimeoutException: null
	at com.mongodb.internal.connection.tlschannel.async.AsynchronousTlsChannelGroup.lambda$startRead$2(AsynchronousTlsChannelGroup.java:309)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	... 3 common frames omitted
[2024-04-09 03:53:57,117]  - Exception in monitor thread while connecting to server mockdb-shard-00-01.kce5o.mongodb.net:27017
com.mongodb.MongoSocketReadTimeoutException: Timeout while receiving message
	at com.mongodb.internal.connection.AsynchronousChannelStream$BasicCompletionHandler.failed(AsynchronousChannelStream.java:263)
	at com.mongodb.internal.connection.AsynchronousChannelStream$BasicCompletionHandler.failed(AsynchronousChannelStream.java:233)
	at com.mongodb.internal.connection.tlschannel.async.AsynchronousTlsChannel.lambda$read$6(AsynchronousTlsChannel.java:123)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.nio.channels.InterruptedByTimeoutException: null
	at com.mongodb.internal.connection.tlschannel.async.AsynchronousTlsChannelGroup.lambda$startRead$2(AsynchronousTlsChannelGroup.java:309)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	... 3 common frames omitted
[2024-04-09 03:53:57,291]  - Monitor thread successfully connected to server with description ServerDescription{address=localhost:27017, type=REPLICA_SET_PRIMARY, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=13, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=183032522, setName='mr1', canonicalAddress=localhost:27017, hosts=[localhost:27017], passives=[], arbiters=[], primary='localhost:27017', tagSet=TagSet{[]}, electionId=7fffffff0000000000000003, setVersion=1, topologyVersion=TopologyVersion{processId=6614b1d8b960826999c97427, counter=6}, lastWriteDate=Tue Apr 09 03:53:57 UTC 2024, lastUpdateTimeNanos=8625795446924}
[2024-04-09 03:53:57,293]  - Monitor thread successfully connected to server with description ServerDescription{address=localhost:27017, type=REPLICA_SET_PRIMARY, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=13, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=183971590, setName='mr1', canonicalAddress=localhost:27017, hosts=[localhost:27017], passives=[], arbiters=[], primary='localhost:27017', tagSet=TagSet{[]}, electionId=7fffffff0000000000000003, setVersion=1, topologyVersion=TopologyVersion{processId=6614b1d8b960826999c97427, counter=6}, lastWriteDate=Tue Apr 09 03:53:57 UTC 2024, lastUpdateTimeNanos=8625796583205}
[2024-04-09 03:53:58,335]  - Monitor thread successfully connected to server with description ServerDescription{address=mockdb-shard-00-02.kce5o.mongodb.net:27017, type=REPLICA_SET_SECONDARY, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=13, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=941344344, setName='atlas-12isch-shard-0', canonicalAddress=mockdb-shard-00-02.kce5o.mongodb.net:27017, hosts=[mockdb-shard-00-02.kce5o.mongodb.net:27017, mockdb-shard-00-01.kce5o.mongodb.net:27017, mockdb-shard-00-00.kce5o.mongodb.net:27017], passives=[], arbiters=[], primary='mockdb-shard-00-01.kce5o.mongodb.net:27017', tagSet=TagSet{[Tag{name='availabilityZone', value='use2-az3'}, Tag{name='diskState', value='READY'}, Tag{name='nodeType', value='ELECTABLE'}, Tag{name='provider', value='AWS'}, Tag{name='region', value='US_EAST_2'}, Tag{name='workloadType', value='OPERATIONAL'}]}, electionId=null, setVersion=19, topologyVersion=TopologyVersion{processId=6605b40c8b733a65ff4f4738, counter=3}, lastWriteDate=Tue Apr 09 03:53:50 UTC 2024, lastUpdateTimeNanos=8626842022516}
[2024-04-09 03:53:58,340]  - Monitor thread successfully connected to server with description ServerDescription{address=mockdb-shard-00-00.kce5o.mongodb.net:27017, type=REPLICA_SET_SECONDARY, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=13, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=916483062, setName='atlas-12isch-shard-0', canonicalAddress=mockdb-shard-00-00.kce5o.mongodb.net:27017, hosts=[mockdb-shard-00-02.kce5o.mongodb.net:27017, mockdb-shard-00-01.kce5o.mongodb.net:27017, mockdb-shard-00-00.kce5o.mongodb.net:27017], passives=[], arbiters=[], primary='mockdb-shard-00-01.kce5o.mongodb.net:27017', tagSet=TagSet{[Tag{name='availabilityZone', value='use2-az1'}, Tag{name='diskState', value='READY'}, Tag{name='nodeType', value='ELECTABLE'}, Tag{name='provider', value='AWS'}, Tag{name='region', value='US_EAST_2'}, Tag{name='workloadType', value='OPERATIONAL'}]}, electionId=null, setVersion=19, topologyVersion=TopologyVersion{processId=6605b2817f9bc0966b4768af, counter=5}, lastWriteDate=Tue Apr 09 03:53:50 UTC 2024, lastUpdateTimeNanos=8626846450934}
[2024-04-09 03:53:58,774]  - Monitor thread successfully connected to server with description ServerDescription{address=mockdb-shard-00-01.kce5o.mongodb.net:27017, type=REPLICA_SET_PRIMARY, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=13, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=901961097, setName='atlas-12isch-shard-0', canonicalAddress=mockdb-shard-00-01.kce5o.mongodb.net:27017, hosts=[mockdb-shard-00-02.kce5o.mongodb.net:27017, mockdb-shard-00-01.kce5o.mongodb.net:27017, mockdb-shard-00-00.kce5o.mongodb.net:27017], passives=[], arbiters=[], primary='mockdb-shard-00-01.kce5o.mongodb.net:27017', tagSet=TagSet{[Tag{name='availabilityZone', value='use2-az2'}, Tag{name='diskState', value='READY'}, Tag{name='nodeType', value='ELECTABLE'}, Tag{name='provider', value='AWS'}, Tag{name='region', value='US_EAST_2'}, Tag{name='workloadType', value='OPERATIONAL'}]}, electionId=7fffffff0000000000000053, setVersion=19, topologyVersion=TopologyVersion{processId=6605b3418dc1e1ac9bbaccbc, counter=6}, lastWriteDate=Tue Apr 09 03:53:50 UTC 2024, lastUpdateTimeNanos=8627281159426}
[2024-04-09 03:53:58,776]  - Discovered replica set primary mockdb-shard-00-01.kce5o.mongodb.net:27017 with max election id 7fffffff0000000000000053 and max set version 19
[2024-04-09 03:57:14,719]  - Exception in monitor thread while connecting to server localhost:27017
com.mongodb.MongoSocketReadTimeoutException: Timeout while receiving message
	at com.mongodb.connection.netty.NettyStream$InboundBufferHandler.exceptionCaught(NettyStream.java:427)
	at io.netty.channel.AbstractChannelHandlerContext.invokeExceptionCaught(AbstractChannelHandlerContext.java:346)
	at io.netty.channel.AbstractChannelHandlerContext.invokeExceptionCaught(AbstractChannelHandlerContext.java:325)
	at io.netty.channel.AbstractChannelHandlerContext.fireExceptionCaught(AbstractChannelHandlerContext.java:317)
	at com.mongodb.connection.netty.NettyStream$ReadTimeoutTask.run(NettyStream.java:564)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:153)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:174)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:167)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:470)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: io.netty.handler.timeout.ReadTimeoutException: null
[2024-04-09 03:57:15,047]  - Exception in monitor thread while connecting to server localhost:27017
com.mongodb.MongoSocketReadTimeoutException: Timeout while receiving message
	at com.mongodb.connection.netty.NettyStream$InboundBufferHandler.exceptionCaught(NettyStream.java:427)
	at io.netty.channel.AbstractChannelHandlerContext.invokeExceptionCaught(AbstractChannelHandlerContext.java:346)
	at io.netty.channel.AbstractChannelHandlerContext.invokeExceptionCaught(AbstractChannelHandlerContext.java:325)
	at io.netty.channel.AbstractChannelHandlerContext.fireExceptionCaught(AbstractChannelHandlerContext.java:317)
	at com.mongodb.connection.netty.NettyStream$ReadTimeoutTask.run(NettyStream.java:564)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:153)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:174)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:167)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:470)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: io.netty.handler.timeout.ReadTimeoutException: null
[2024-04-09 03:57:15,320]  - Monitor thread successfully connected to server with description ServerDescription{address=localhost:27017, type=REPLICA_SET_PRIMARY, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=13, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=113797935, setName='mr1', canonicalAddress=localhost:27017, hosts=[localhost:27017], passives=[], arbiters=[], primary='localhost:27017', tagSet=TagSet{[]}, electionId=7fffffff0000000000000003, setVersion=1, topologyVersion=TopologyVersion{processId=6614b1d8b960826999c97427, counter=6}, lastWriteDate=Tue Apr 09 03:57:14 UTC 2024, lastUpdateTimeNanos=8823734146608}
[2024-04-09 03:57:15,188]  - Exception in monitor thread while connecting to server mockdb-shard-00-00.kce5o.mongodb.net:27017
com.mongodb.MongoSocketReadTimeoutException: Timeout while receiving message
	at com.mongodb.internal.connection.AsynchronousChannelStream$BasicCompletionHandler.failed(AsynchronousChannelStream.java:263)
	at com.mongodb.internal.connection.AsynchronousChannelStream$BasicCompletionHandler.failed(AsynchronousChannelStream.java:233)
	at com.mongodb.internal.connection.tlschannel.async.AsynchronousTlsChannel.lambda$read$6(AsynchronousTlsChannel.java:123)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.nio.channels.InterruptedByTimeoutException: null
	at com.mongodb.internal.connection.tlschannel.async.AsynchronousTlsChannelGroup.lambda$startRead$2(AsynchronousTlsChannelGroup.java:309)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	... 3 common frames omitted
[2024-04-09 03:57:15,189]  - Exception in monitor thread while connecting to server mockdb-shard-00-02.kce5o.mongodb.net:27017
com.mongodb.MongoSocketReadTimeoutException: Timeout while receiving message
	at com.mongodb.internal.connection.AsynchronousChannelStream$BasicCompletionHandler.failed(AsynchronousChannelStream.java:263)
	at com.mongodb.internal.connection.AsynchronousChannelStream$BasicCompletionHandler.failed(AsynchronousChannelStream.java:233)
	at com.mongodb.internal.connection.tlschannel.async.AsynchronousTlsChannel.lambda$read$6(AsynchronousTlsChannel.java:123)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.nio.channels.InterruptedByTimeoutException: null
	at com.mongodb.internal.connection.tlschannel.async.AsynchronousTlsChannelGroup.lambda$startRead$2(AsynchronousTlsChannelGroup.java:309)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	... 3 common frames omitted
[2024-04-09 03:57:15,188]  - Exception in monitor thread while connecting to server mockdb-shard-00-01.kce5o.mongodb.net:27017
com.mongodb.MongoSocketReadTimeoutException: Timeout while receiving message
	at com.mongodb.internal.connection.AsynchronousChannelStream$BasicCompletionHandler.failed(AsynchronousChannelStream.java:263)
	at com.mongodb.internal.connection.AsynchronousChannelStream$BasicCompletionHandler.failed(AsynchronousChannelStream.java:233)
	at com.mongodb.internal.connection.tlschannel.async.AsynchronousTlsChannel.lambda$read$6(AsynchronousTlsChannel.java:123)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.nio.channels.InterruptedByTimeoutException: null
	at com.mongodb.internal.connection.tlschannel.async.AsynchronousTlsChannelGroup.lambda$startRead$2(AsynchronousTlsChannelGroup.java:309)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	... 3 common frames omitted
[2024-04-09 03:57:15,340]  - Monitor thread successfully connected to server with description ServerDescription{address=localhost:27017, type=REPLICA_SET_PRIMARY, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=13, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=18495206, setName='mr1', canonicalAddress=localhost:27017, hosts=[localhost:27017], passives=[], arbiters=[], primary='localhost:27017', tagSet=TagSet{[]}, electionId=7fffffff0000000000000003, setVersion=1, topologyVersion=TopologyVersion{processId=6614b1d8b960826999c97427, counter=6}, lastWriteDate=Tue Apr 09 03:57:15 UTC 2024, lastUpdateTimeNanos=8823754873658}
[2024-04-09 03:57:16,315]  - Monitor thread successfully connected to server with description ServerDescription{address=mockdb-shard-00-00.kce5o.mongodb.net:27017, type=REPLICA_SET_SECONDARY, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=13, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=691276781, setName='atlas-12isch-shard-0', canonicalAddress=mockdb-shard-00-00.kce5o.mongodb.net:27017, hosts=[mockdb-shard-00-02.kce5o.mongodb.net:27017, mockdb-shard-00-01.kce5o.mongodb.net:27017, mockdb-shard-00-00.kce5o.mongodb.net:27017], passives=[], arbiters=[], primary='mockdb-shard-00-01.kce5o.mongodb.net:27017', tagSet=TagSet{[Tag{name='availabilityZone', value='use2-az1'}, Tag{name='diskState', value='READY'}, Tag{name='nodeType', value='ELECTABLE'}, Tag{name='provider', value='AWS'}, Tag{name='region', value='US_EAST_2'}, Tag{name='workloadType', value='OPERATIONAL'}]}, electionId=null, setVersion=19, topologyVersion=TopologyVersion{processId=6605b2817f9bc0966b4768af, counter=5}, lastWriteDate=Tue Apr 09 03:57:10 UTC 2024, lastUpdateTimeNanos=8824729290015}
[2024-04-09 03:57:16,552]  - Monitor thread successfully connected to server with description ServerDescription{address=mockdb-shard-00-02.kce5o.mongodb.net:27017, type=REPLICA_SET_SECONDARY, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=13, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=684612499, setName='atlas-12isch-shard-0', canonicalAddress=mockdb-shard-00-02.kce5o.mongodb.net:27017, hosts=[mockdb-shard-00-02.kce5o.mongodb.net:27017, mockdb-shard-00-01.kce5o.mongodb.net:27017, mockdb-shard-00-00.kce5o.mongodb.net:27017], passives=[], arbiters=[], primary='mockdb-shard-00-01.kce5o.mongodb.net:27017', tagSet=TagSet{[Tag{name='availabilityZone', value='use2-az3'}, Tag{name='diskState', value='READY'}, Tag{name='nodeType', value='ELECTABLE'}, Tag{name='provider', value='AWS'}, Tag{name='region', value='US_EAST_2'}, Tag{name='workloadType', value='OPERATIONAL'}]}, electionId=null, setVersion=19, topologyVersion=TopologyVersion{processId=6605b40c8b733a65ff4f4738, counter=3}, lastWriteDate=Tue Apr 09 03:57:10 UTC 2024, lastUpdateTimeNanos=8824966428740}
[2024-04-09 03:57:16,693]  - Monitor thread successfully connected to server with description ServerDescription{address=mockdb-shard-00-01.kce5o.mongodb.net:27017, type=REPLICA_SET_PRIMARY, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=13, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=677738593, setName='atlas-12isch-shard-0', canonicalAddress=mockdb-shard-00-01.kce5o.mongodb.net:27017, hosts=[mockdb-shard-00-02.kce5o.mongodb.net:27017, mockdb-shard-00-01.kce5o.mongodb.net:27017, mockdb-shard-00-00.kce5o.mongodb.net:27017], passives=[], arbiters=[], primary='mockdb-shard-00-01.kce5o.mongodb.net:27017', tagSet=TagSet{[Tag{name='availabilityZone', value='use2-az2'}, Tag{name='diskState', value='READY'}, Tag{name='nodeType', value='ELECTABLE'}, Tag{name='provider', value='AWS'}, Tag{name='region', value='US_EAST_2'}, Tag{name='workloadType', value='OPERATIONAL'}]}, electionId=7fffffff0000000000000053, setVersion=19, topologyVersion=TopologyVersion{processId=6605b3418dc1e1ac9bbaccbc, counter=6}, lastWriteDate=Tue Apr 09 03:57:10 UTC 2024, lastUpdateTimeNanos=8825107398012}
[2024-04-09 03:57:16,694]  - Discovered replica set primary mockdb-shard-00-01.kce5o.mongodb.net:27017 with max election id 7fffffff0000000000000053 and max set version 19
[2024-04-09 04:26:19,664]  - Exception in monitor thread while connecting to server localhost:27017
com.mongodb.MongoSocketReadTimeoutException: Timeout while receiving message
	at com.mongodb.connection.netty.NettyStream$InboundBufferHandler.exceptionCaught(NettyStream.java:427)
	at io.netty.channel.AbstractChannelHandlerContext.invokeExceptionCaught(AbstractChannelHandlerContext.java:346)
	at io.netty.channel.AbstractChannelHandlerContext.invokeExceptionCaught(AbstractChannelHandlerContext.java:325)
	at io.netty.channel.AbstractChannelHandlerContext.fireExceptionCaught(AbstractChannelHandlerContext.java:317)
	at com.mongodb.connection.netty.NettyStream$ReadTimeoutTask.run(NettyStream.java:564)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:153)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:174)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:167)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:470)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: io.netty.handler.timeout.ReadTimeoutException: null
[2024-04-09 04:26:18,596]  - Fetching features for default tenant
[2024-04-09 04:26:18,598]  - Exception in monitor thread while connecting to server localhost:27017
com.mongodb.MongoSocketReadTimeoutException: Timeout while receiving message
	at com.mongodb.connection.netty.NettyStream$InboundBufferHandler.exceptionCaught(NettyStream.java:427)
	at io.netty.channel.AbstractChannelHandlerContext.invokeExceptionCaught(AbstractChannelHandlerContext.java:346)
	at io.netty.channel.AbstractChannelHandlerContext.invokeExceptionCaught(AbstractChannelHandlerContext.java:325)
	at io.netty.channel.AbstractChannelHandlerContext.fireExceptionCaught(AbstractChannelHandlerContext.java:317)
	at com.mongodb.connection.netty.NettyStream$ReadTimeoutTask.run(NettyStream.java:564)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:153)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:174)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:167)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:470)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: io.netty.handler.timeout.ReadTimeoutException: null
[2024-04-09 04:26:18,613]  - Exception in monitor thread while connecting to server mockdb-shard-00-02.kce5o.mongodb.net:27017
com.mongodb.MongoSocketReadTimeoutException: Timeout while receiving message
	at com.mongodb.internal.connection.AsynchronousChannelStream$BasicCompletionHandler.failed(AsynchronousChannelStream.java:263)
	at com.mongodb.internal.connection.AsynchronousChannelStream$BasicCompletionHandler.failed(AsynchronousChannelStream.java:233)
	at com.mongodb.internal.connection.tlschannel.async.AsynchronousTlsChannel.lambda$read$6(AsynchronousTlsChannel.java:123)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.nio.channels.InterruptedByTimeoutException: null
	at com.mongodb.internal.connection.tlschannel.async.AsynchronousTlsChannelGroup.lambda$startRead$2(AsynchronousTlsChannelGroup.java:309)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	... 3 common frames omitted
[2024-04-09 04:26:18,614]  - Exception in monitor thread while connecting to server mockdb-shard-00-01.kce5o.mongodb.net:27017
com.mongodb.MongoSocketReadTimeoutException: Timeout while receiving message
	at com.mongodb.internal.connection.AsynchronousChannelStream$BasicCompletionHandler.failed(AsynchronousChannelStream.java:263)
	at com.mongodb.internal.connection.AsynchronousChannelStream$BasicCompletionHandler.failed(AsynchronousChannelStream.java:233)
	at com.mongodb.internal.connection.tlschannel.async.AsynchronousTlsChannel.lambda$read$6(AsynchronousTlsChannel.java:123)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.nio.channels.InterruptedByTimeoutException: null
	at com.mongodb.internal.connection.tlschannel.async.AsynchronousTlsChannelGroup.lambda$startRead$2(AsynchronousTlsChannelGroup.java:309)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	... 3 common frames omitted
[2024-04-09 04:26:18,623]  - Exception in monitor thread while connecting to server mockdb-shard-00-00.kce5o.mongodb.net:27017
com.mongodb.MongoSocketReadTimeoutException: Timeout while receiving message
	at com.mongodb.internal.connection.AsynchronousChannelStream$BasicCompletionHandler.failed(AsynchronousChannelStream.java:263)
	at com.mongodb.internal.connection.AsynchronousChannelStream$BasicCompletionHandler.failed(AsynchronousChannelStream.java:233)
	at com.mongodb.internal.connection.tlschannel.async.AsynchronousTlsChannel.lambda$read$6(AsynchronousTlsChannel.java:123)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.nio.channels.InterruptedByTimeoutException: null
	at com.mongodb.internal.connection.tlschannel.async.AsynchronousTlsChannelGroup.lambda$startRead$2(AsynchronousTlsChannelGroup.java:309)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	... 3 common frames omitted
[2024-04-09 04:26:18,671]  - No server chosen by com.mongodb.reactivestreams.client.internal.ClientSessionHelper$$Lambda$3301/0x00007f567ce1cc40@1d296fe7 from cluster description ClusterDescription{type=UNKNOWN, connectionMode=SINGLE, serverDescriptions=[ServerDescription{address=localhost:27017, type=UNKNOWN, state=CONNECTING, exception={com.mongodb.MongoSocketReadTimeoutException: Timeout while receiving message}, caused by {io.netty.handler.timeout.ReadTimeoutException}}]}. Waiting for 30000 ms before timing out
[2024-04-09 04:26:18,796]  - Monitor thread successfully connected to server with description ServerDescription{address=localhost:27017, type=REPLICA_SET_PRIMARY, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=13, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=199432807, setName='mr1', canonicalAddress=localhost:27017, hosts=[localhost:27017], passives=[], arbiters=[], primary='localhost:27017', tagSet=TagSet{[]}, electionId=7fffffff0000000000000003, setVersion=1, topologyVersion=TopologyVersion{processId=6614b1d8b960826999c97427, counter=6}, lastWriteDate=Tue Apr 09 04:26:18 UTC 2024, lastUpdateTimeNanos=10568767977700}
[2024-04-09 04:26:18,802]  - Monitor thread successfully connected to server with description ServerDescription{address=localhost:27017, type=REPLICA_SET_PRIMARY, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=13, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=145946606, setName='mr1', canonicalAddress=localhost:27017, hosts=[localhost:27017], passives=[], arbiters=[], primary='localhost:27017', tagSet=TagSet{[]}, electionId=7fffffff0000000000000003, setVersion=1, topologyVersion=TopologyVersion{processId=6614b1d8b960826999c97427, counter=6}, lastWriteDate=Tue Apr 09 04:26:18 UTC 2024, lastUpdateTimeNanos=10568773913963}
[2024-04-09 04:26:18,890]  - Cache entry evicted for key tenantNewFeatures:6614a751cb467e382bcada07
[2024-04-09 04:26:18,945]  - Cache miss for key tenantNewFeatures:6614a751cb467e382bcada07
[2024-04-09 04:26:20,200]  - Monitor thread successfully connected to server with description ServerDescription{address=mockdb-shard-00-00.kce5o.mongodb.net:27017, type=REPLICA_SET_SECONDARY, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=13, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=931585258, setName='atlas-12isch-shard-0', canonicalAddress=mockdb-shard-00-00.kce5o.mongodb.net:27017, hosts=[mockdb-shard-00-02.kce5o.mongodb.net:27017, mockdb-shard-00-01.kce5o.mongodb.net:27017, mockdb-shard-00-00.kce5o.mongodb.net:27017], passives=[], arbiters=[], primary='mockdb-shard-00-01.kce5o.mongodb.net:27017', tagSet=TagSet{[Tag{name='availabilityZone', value='use2-az1'}, Tag{name='diskState', value='READY'}, Tag{name='nodeType', value='ELECTABLE'}, Tag{name='provider', value='AWS'}, Tag{name='region', value='US_EAST_2'}, Tag{name='workloadType', value='OPERATIONAL'}]}, electionId=null, setVersion=19, topologyVersion=TopologyVersion{processId=6605b2817f9bc0966b4768af, counter=5}, lastWriteDate=Tue Apr 09 04:26:10 UTC 2024, lastUpdateTimeNanos=10570172670226}
[2024-04-09 04:26:20,388]  - Cache entry added for key tenantNewFeatures:6614a751cb467e382bcada07
[2024-04-09 04:26:20,655]  - Monitor thread successfully connected to server with description ServerDescription{address=mockdb-shard-00-01.kce5o.mongodb.net:27017, type=REPLICA_SET_PRIMARY, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=13, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=940773765, setName='atlas-12isch-shard-0', canonicalAddress=mockdb-shard-00-01.kce5o.mongodb.net:27017, hosts=[mockdb-shard-00-02.kce5o.mongodb.net:27017, mockdb-shard-00-01.kce5o.mongodb.net:27017, mockdb-shard-00-00.kce5o.mongodb.net:27017], passives=[], arbiters=[], primary='mockdb-shard-00-01.kce5o.mongodb.net:27017', tagSet=TagSet{[Tag{name='availabilityZone', value='use2-az2'}, Tag{name='diskState', value='READY'}, Tag{name='nodeType', value='ELECTABLE'}, Tag{name='provider', value='AWS'}, Tag{name='region', value='US_EAST_2'}, Tag{name='workloadType', value='OPERATIONAL'}]}, electionId=7fffffff0000000000000053, setVersion=19, topologyVersion=TopologyVersion{processId=6605b3418dc1e1ac9bbaccbc, counter=6}, lastWriteDate=Tue Apr 09 04:26:10 UTC 2024, lastUpdateTimeNanos=10570627299904}
[2024-04-09 04:26:20,655]  - Discovered replica set primary mockdb-shard-00-01.kce5o.mongodb.net:27017 with max election id 7fffffff0000000000000053 and max set version 19
[2024-04-09 04:26:20,914]  - Monitor thread successfully connected to server with description ServerDescription{address=mockdb-shard-00-02.kce5o.mongodb.net:27017, type=REPLICA_SET_SECONDARY, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=13, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=985758305, setName='atlas-12isch-shard-0', canonicalAddress=mockdb-shard-00-02.kce5o.mongodb.net:27017, hosts=[mockdb-shard-00-02.kce5o.mongodb.net:27017, mockdb-shard-00-01.kce5o.mongodb.net:27017, mockdb-shard-00-00.kce5o.mongodb.net:27017], passives=[], arbiters=[], primary='mockdb-shard-00-01.kce5o.mongodb.net:27017', tagSet=TagSet{[Tag{name='availabilityZone', value='use2-az3'}, Tag{name='diskState', value='READY'}, Tag{name='nodeType', value='ELECTABLE'}, Tag{name='provider', value='AWS'}, Tag{name='region', value='US_EAST_2'}, Tag{name='workloadType', value='OPERATIONAL'}]}, electionId=null, setVersion=19, topologyVersion=TopologyVersion{processId=6605b40c8b733a65ff4f4738, counter=3}, lastWriteDate=Tue Apr 09 04:26:20 UTC 2024, lastUpdateTimeNanos=10570886790298}
[2024-04-09 04:59:04,321]  - Fetching features for default tenant
[2024-04-09 04:59:04,684]  - Exception in monitor thread while connecting to server localhost:27017
com.mongodb.MongoSocketReadTimeoutException: Timeout while receiving message
	at com.mongodb.connection.netty.NettyStream$InboundBufferHandler.exceptionCaught(NettyStream.java:427)
	at io.netty.channel.AbstractChannelHandlerContext.invokeExceptionCaught(AbstractChannelHandlerContext.java:346)
	at io.netty.channel.AbstractChannelHandlerContext.invokeExceptionCaught(AbstractChannelHandlerContext.java:325)
	at io.netty.channel.AbstractChannelHandlerContext.fireExceptionCaught(AbstractChannelHandlerContext.java:317)
	at com.mongodb.connection.netty.NettyStream$ReadTimeoutTask.run(NettyStream.java:564)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:153)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:174)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:167)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:470)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: io.netty.handler.timeout.ReadTimeoutException: null
[2024-04-09 04:59:04,689]  - Exception in monitor thread while connecting to server mockdb-shard-00-00.kce5o.mongodb.net:27017
com.mongodb.MongoSocketReadTimeoutException: Timeout while receiving message
	at com.mongodb.internal.connection.AsynchronousChannelStream$BasicCompletionHandler.failed(AsynchronousChannelStream.java:263)
	at com.mongodb.internal.connection.AsynchronousChannelStream$BasicCompletionHandler.failed(AsynchronousChannelStream.java:233)
	at com.mongodb.internal.connection.tlschannel.async.AsynchronousTlsChannel.lambda$read$6(AsynchronousTlsChannel.java:123)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.nio.channels.InterruptedByTimeoutException: null
	at com.mongodb.internal.connection.tlschannel.async.AsynchronousTlsChannelGroup.lambda$startRead$2(AsynchronousTlsChannelGroup.java:309)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	... 3 common frames omitted
[2024-04-09 04:59:04,695]  - Exception in monitor thread while connecting to server mockdb-shard-00-02.kce5o.mongodb.net:27017
com.mongodb.MongoSocketReadTimeoutException: Timeout while receiving message
	at com.mongodb.internal.connection.AsynchronousChannelStream$BasicCompletionHandler.failed(AsynchronousChannelStream.java:263)
	at com.mongodb.internal.connection.AsynchronousChannelStream$BasicCompletionHandler.failed(AsynchronousChannelStream.java:233)
	at com.mongodb.internal.connection.tlschannel.async.AsynchronousTlsChannel.lambda$read$6(AsynchronousTlsChannel.java:123)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.nio.channels.InterruptedByTimeoutException: null
	at com.mongodb.internal.connection.tlschannel.async.AsynchronousTlsChannelGroup.lambda$startRead$2(AsynchronousTlsChannelGroup.java:309)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	... 3 common frames omitted
[2024-04-09 04:59:04,701]  - Exception in monitor thread while connecting to server localhost:27017
com.mongodb.MongoSocketReadTimeoutException: Timeout while receiving message
	at com.mongodb.connection.netty.NettyStream$InboundBufferHandler.exceptionCaught(NettyStream.java:427)
	at io.netty.channel.AbstractChannelHandlerContext.invokeExceptionCaught(AbstractChannelHandlerContext.java:346)
	at io.netty.channel.AbstractChannelHandlerContext.invokeExceptionCaught(AbstractChannelHandlerContext.java:325)
	at io.netty.channel.AbstractChannelHandlerContext.fireExceptionCaught(AbstractChannelHandlerContext.java:317)
	at com.mongodb.connection.netty.NettyStream$ReadTimeoutTask.run(NettyStream.java:564)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:153)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:174)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:167)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:470)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: io.netty.handler.timeout.ReadTimeoutException: null
[2024-04-09 04:59:03,350]  - Cluster description not yet available. Waiting for 30000 ms before timing out
[2024-04-09 04:59:04,709]  - Exception in monitor thread while connecting to server mockdb-shard-00-01.kce5o.mongodb.net:27017
com.mongodb.MongoSocketReadTimeoutException: Timeout while receiving message
	at com.mongodb.internal.connection.AsynchronousChannelStream$BasicCompletionHandler.failed(AsynchronousChannelStream.java:263)
	at com.mongodb.internal.connection.AsynchronousChannelStream$BasicCompletionHandler.failed(AsynchronousChannelStream.java:233)
	at com.mongodb.internal.connection.tlschannel.async.AsynchronousTlsChannel.lambda$read$6(AsynchronousTlsChannel.java:123)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.nio.channels.InterruptedByTimeoutException: null
	at com.mongodb.internal.connection.tlschannel.async.AsynchronousTlsChannelGroup.lambda$startRead$2(AsynchronousTlsChannelGroup.java:309)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	... 3 common frames omitted
[2024-04-09 04:59:03,394]  - Monitor thread successfully connected to server with description ServerDescription{address=localhost:27017, type=REPLICA_SET_PRIMARY, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=13, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=53604062, setName='mr1', canonicalAddress=localhost:27017, hosts=[localhost:27017], passives=[], arbiters=[], primary='localhost:27017', tagSet=TagSet{[]}, electionId=7fffffff0000000000000003, setVersion=1, topologyVersion=TopologyVersion{processId=6614b1d8b960826999c97427, counter=6}, lastWriteDate=Tue Apr 09 04:59:04 UTC 2024, lastUpdateTimeNanos=12534731490540}
[2024-04-09 04:59:03,423]  - Monitor thread successfully connected to server with description ServerDescription{address=localhost:27017, type=REPLICA_SET_PRIMARY, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=13, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=37136342, setName='mr1', canonicalAddress=localhost:27017, hosts=[localhost:27017], passives=[], arbiters=[], primary='localhost:27017', tagSet=TagSet{[]}, electionId=7fffffff0000000000000003, setVersion=1, topologyVersion=TopologyVersion{processId=6614b1d8b960826999c97427, counter=6}, lastWriteDate=Tue Apr 09 04:59:04 UTC 2024, lastUpdateTimeNanos=12534760519287}
[2024-04-09 04:59:05,038]  - Monitor thread successfully connected to server with description ServerDescription{address=mockdb-shard-00-02.kce5o.mongodb.net:27017, type=REPLICA_SET_SECONDARY, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=13, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=980092792, setName='atlas-12isch-shard-0', canonicalAddress=mockdb-shard-00-02.kce5o.mongodb.net:27017, hosts=[mockdb-shard-00-02.kce5o.mongodb.net:27017, mockdb-shard-00-01.kce5o.mongodb.net:27017, mockdb-shard-00-00.kce5o.mongodb.net:27017], passives=[], arbiters=[], primary='mockdb-shard-00-01.kce5o.mongodb.net:27017', tagSet=TagSet{[Tag{name='availabilityZone', value='use2-az3'}, Tag{name='diskState', value='READY'}, Tag{name='nodeType', value='ELECTABLE'}, Tag{name='provider', value='AWS'}, Tag{name='region', value='US_EAST_2'}, Tag{name='workloadType', value='OPERATIONAL'}]}, electionId=null, setVersion=19, topologyVersion=TopologyVersion{processId=6605b40c8b733a65ff4f4738, counter=3}, lastWriteDate=Tue Apr 09 04:59:00 UTC 2024, lastUpdateTimeNanos=12536374780707}
[2024-04-09 04:59:05,294]  - Monitor thread successfully connected to server with description ServerDescription{address=mockdb-shard-00-01.kce5o.mongodb.net:27017, type=REPLICA_SET_PRIMARY, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=13, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=927646189, setName='atlas-12isch-shard-0', canonicalAddress=mockdb-shard-00-01.kce5o.mongodb.net:27017, hosts=[mockdb-shard-00-02.kce5o.mongodb.net:27017, mockdb-shard-00-01.kce5o.mongodb.net:27017, mockdb-shard-00-00.kce5o.mongodb.net:27017], passives=[], arbiters=[], primary='mockdb-shard-00-01.kce5o.mongodb.net:27017', tagSet=TagSet{[Tag{name='availabilityZone', value='use2-az2'}, Tag{name='diskState', value='READY'}, Tag{name='nodeType', value='ELECTABLE'}, Tag{name='provider', value='AWS'}, Tag{name='region', value='US_EAST_2'}, Tag{name='workloadType', value='OPERATIONAL'}]}, electionId=7fffffff0000000000000053, setVersion=19, topologyVersion=TopologyVersion{processId=6605b3418dc1e1ac9bbaccbc, counter=6}, lastWriteDate=Tue Apr 09 04:59:00 UTC 2024, lastUpdateTimeNanos=12536631090096}
[2024-04-09 04:59:05,295]  - Discovered replica set primary mockdb-shard-00-01.kce5o.mongodb.net:27017 with max election id 7fffffff0000000000000053 and max set version 19
[2024-04-09 04:59:05,622]  - Monitor thread successfully connected to server with description ServerDescription{address=mockdb-shard-00-00.kce5o.mongodb.net:27017, type=REPLICA_SET_SECONDARY, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=13, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=933751565, setName='atlas-12isch-shard-0', canonicalAddress=mockdb-shard-00-00.kce5o.mongodb.net:27017, hosts=[mockdb-shard-00-02.kce5o.mongodb.net:27017, mockdb-shard-00-01.kce5o.mongodb.net:27017, mockdb-shard-00-00.kce5o.mongodb.net:27017], passives=[], arbiters=[], primary='mockdb-shard-00-01.kce5o.mongodb.net:27017', tagSet=TagSet{[Tag{name='availabilityZone', value='use2-az1'}, Tag{name='diskState', value='READY'}, Tag{name='nodeType', value='ELECTABLE'}, Tag{name='provider', value='AWS'}, Tag{name='region', value='US_EAST_2'}, Tag{name='workloadType', value='OPERATIONAL'}]}, electionId=null, setVersion=19, topologyVersion=TopologyVersion{processId=6605b2817f9bc0966b4768af, counter=5}, lastWriteDate=Tue Apr 09 04:59:00 UTC 2024, lastUpdateTimeNanos=12536958990322}
[2024-04-09 05:08:05,533]  - Exception in monitor thread while connecting to server mockdb-shard-00-00.kce5o.mongodb.net:27017
com.mongodb.MongoSocketReadTimeoutException: Timeout while receiving message
	at com.mongodb.internal.connection.AsynchronousChannelStream$BasicCompletionHandler.failed(AsynchronousChannelStream.java:263)
	at com.mongodb.internal.connection.AsynchronousChannelStream$BasicCompletionHandler.failed(AsynchronousChannelStream.java:233)
	at com.mongodb.internal.connection.tlschannel.async.AsynchronousTlsChannel.lambda$read$6(AsynchronousTlsChannel.java:123)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.nio.channels.InterruptedByTimeoutException: null
	at com.mongodb.internal.connection.tlschannel.async.AsynchronousTlsChannelGroup.lambda$startRead$2(AsynchronousTlsChannelGroup.java:309)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	... 3 common frames omitted
[2024-04-09 05:08:05,519]  - Exception in monitor thread while connecting to server localhost:27017
com.mongodb.MongoSocketReadTimeoutException: Timeout while receiving message
	at com.mongodb.connection.netty.NettyStream$InboundBufferHandler.exceptionCaught(NettyStream.java:427)
	at io.netty.channel.AbstractChannelHandlerContext.invokeExceptionCaught(AbstractChannelHandlerContext.java:346)
	at io.netty.channel.AbstractChannelHandlerContext.invokeExceptionCaught(AbstractChannelHandlerContext.java:325)
	at io.netty.channel.AbstractChannelHandlerContext.fireExceptionCaught(AbstractChannelHandlerContext.java:317)
	at com.mongodb.connection.netty.NettyStream$ReadTimeoutTask.run(NettyStream.java:564)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:153)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:174)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:167)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:470)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: io.netty.handler.timeout.ReadTimeoutException: null
[2024-04-09 05:08:05,488]  - Exception in monitor thread while connecting to server mockdb-shard-00-01.kce5o.mongodb.net:27017
com.mongodb.MongoSocketReadTimeoutException: Timeout while receiving message
	at com.mongodb.internal.connection.AsynchronousChannelStream$BasicCompletionHandler.failed(AsynchronousChannelStream.java:263)
	at com.mongodb.internal.connection.AsynchronousChannelStream$BasicCompletionHandler.failed(AsynchronousChannelStream.java:233)
	at com.mongodb.internal.connection.tlschannel.async.AsynchronousTlsChannel.lambda$read$6(AsynchronousTlsChannel.java:123)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.nio.channels.InterruptedByTimeoutException: null
	at com.mongodb.internal.connection.tlschannel.async.AsynchronousTlsChannelGroup.lambda$startRead$2(AsynchronousTlsChannelGroup.java:309)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	... 3 common frames omitted
[2024-04-09 05:08:05,488]  - Exception in monitor thread while connecting to server mockdb-shard-00-02.kce5o.mongodb.net:27017
com.mongodb.MongoSocketReadTimeoutException: Timeout while receiving message
	at com.mongodb.internal.connection.AsynchronousChannelStream$BasicCompletionHandler.failed(AsynchronousChannelStream.java:263)
	at com.mongodb.internal.connection.AsynchronousChannelStream$BasicCompletionHandler.failed(AsynchronousChannelStream.java:233)
	at com.mongodb.internal.connection.tlschannel.async.AsynchronousTlsChannel.lambda$read$6(AsynchronousTlsChannel.java:123)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.nio.channels.InterruptedByTimeoutException: null
	at com.mongodb.internal.connection.tlschannel.async.AsynchronousTlsChannelGroup.lambda$startRead$2(AsynchronousTlsChannelGroup.java:309)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	... 3 common frames omitted
[2024-04-09 05:08:05,527]  - Exception in monitor thread while connecting to server localhost:27017
com.mongodb.MongoSocketReadTimeoutException: Timeout while receiving message
	at com.mongodb.connection.netty.NettyStream$InboundBufferHandler.exceptionCaught(NettyStream.java:427)
	at io.netty.channel.AbstractChannelHandlerContext.invokeExceptionCaught(AbstractChannelHandlerContext.java:346)
	at io.netty.channel.AbstractChannelHandlerContext.invokeExceptionCaught(AbstractChannelHandlerContext.java:325)
	at io.netty.channel.AbstractChannelHandlerContext.fireExceptionCaught(AbstractChannelHandlerContext.java:317)
	at com.mongodb.connection.netty.NettyStream$ReadTimeoutTask.run(NettyStream.java:564)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:153)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:174)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:167)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:470)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: io.netty.handler.timeout.ReadTimeoutException: null
[2024-04-09 05:08:05,978]  - Monitor thread successfully connected to server with description ServerDescription{address=localhost:27017, type=REPLICA_SET_PRIMARY, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=13, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=128996544, setName='mr1', canonicalAddress=localhost:27017, hosts=[localhost:27017], passives=[], arbiters=[], primary='localhost:27017', tagSet=TagSet{[]}, electionId=7fffffff0000000000000003, setVersion=1, topologyVersion=TopologyVersion{processId=6614b1d8b960826999c97427, counter=6}, lastWriteDate=Tue Apr 09 05:08:05 UTC 2024, lastUpdateTimeNanos=13077525275997}
[2024-04-09 05:08:05,981]  - Monitor thread successfully connected to server with description ServerDescription{address=localhost:27017, type=REPLICA_SET_PRIMARY, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=13, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=36265735, setName='mr1', canonicalAddress=localhost:27017, hosts=[localhost:27017], passives=[], arbiters=[], primary='localhost:27017', tagSet=TagSet{[]}, electionId=7fffffff0000000000000003, setVersion=1, topologyVersion=TopologyVersion{processId=6614b1d8b960826999c97427, counter=6}, lastWriteDate=Tue Apr 09 05:08:05 UTC 2024, lastUpdateTimeNanos=13077528909023}
[2024-04-09 05:08:07,350]  - Monitor thread successfully connected to server with description ServerDescription{address=mockdb-shard-00-00.kce5o.mongodb.net:27017, type=REPLICA_SET_SECONDARY, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=13, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=**********, setName='atlas-12isch-shard-0', canonicalAddress=mockdb-shard-00-00.kce5o.mongodb.net:27017, hosts=[mockdb-shard-00-02.kce5o.mongodb.net:27017, mockdb-shard-00-01.kce5o.mongodb.net:27017, mockdb-shard-00-00.kce5o.mongodb.net:27017], passives=[], arbiters=[], primary='mockdb-shard-00-01.kce5o.mongodb.net:27017', tagSet=TagSet{[Tag{name='availabilityZone', value='use2-az1'}, Tag{name='diskState', value='READY'}, Tag{name='nodeType', value='ELECTABLE'}, Tag{name='provider', value='AWS'}, Tag{name='region', value='US_EAST_2'}, Tag{name='workloadType', value='OPERATIONAL'}]}, electionId=null, setVersion=19, topologyVersion=TopologyVersion{processId=6605b2817f9bc0966b4768af, counter=5}, lastWriteDate=Tue Apr 09 05:08:00 UTC 2024, lastUpdateTimeNanos=13078897116238}
[2024-04-09 05:08:07,559]  - Monitor thread successfully connected to server with description ServerDescription{address=mockdb-shard-00-02.kce5o.mongodb.net:27017, type=REPLICA_SET_SECONDARY, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=13, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=**********, setName='atlas-12isch-shard-0', canonicalAddress=mockdb-shard-00-02.kce5o.mongodb.net:27017, hosts=[mockdb-shard-00-02.kce5o.mongodb.net:27017, mockdb-shard-00-01.kce5o.mongodb.net:27017, mockdb-shard-00-00.kce5o.mongodb.net:27017], passives=[], arbiters=[], primary='mockdb-shard-00-01.kce5o.mongodb.net:27017', tagSet=TagSet{[Tag{name='availabilityZone', value='use2-az3'}, Tag{name='diskState', value='READY'}, Tag{name='nodeType', value='ELECTABLE'}, Tag{name='provider', value='AWS'}, Tag{name='region', value='US_EAST_2'}, Tag{name='workloadType', value='OPERATIONAL'}]}, electionId=null, setVersion=19, topologyVersion=TopologyVersion{processId=6605b40c8b733a65ff4f4738, counter=3}, lastWriteDate=Tue Apr 09 05:08:00 UTC 2024, lastUpdateTimeNanos=13079106185679}
[2024-04-09 05:08:07,571]  - Monitor thread successfully connected to server with description ServerDescription{address=mockdb-shard-00-01.kce5o.mongodb.net:27017, type=REPLICA_SET_PRIMARY, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=13, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=**********, setName='atlas-12isch-shard-0', canonicalAddress=mockdb-shard-00-01.kce5o.mongodb.net:27017, hosts=[mockdb-shard-00-02.kce5o.mongodb.net:27017, mockdb-shard-00-01.kce5o.mongodb.net:27017, mockdb-shard-00-00.kce5o.mongodb.net:27017], passives=[], arbiters=[], primary='mockdb-shard-00-01.kce5o.mongodb.net:27017', tagSet=TagSet{[Tag{name='availabilityZone', value='use2-az2'}, Tag{name='diskState', value='READY'}, Tag{name='nodeType', value='ELECTABLE'}, Tag{name='provider', value='AWS'}, Tag{name='region', value='US_EAST_2'}, Tag{name='workloadType', value='OPERATIONAL'}]}, electionId=7fffffff0000000000000053, setVersion=19, topologyVersion=TopologyVersion{processId=6605b3418dc1e1ac9bbaccbc, counter=6}, lastWriteDate=Tue Apr 09 05:08:00 UTC 2024, lastUpdateTimeNanos=13079118104507}
[2024-04-09 05:08:07,571]  - Discovered replica set primary mockdb-shard-00-01.kce5o.mongodb.net:27017 with max election id 7fffffff0000000000000053 and max set version 19
[2024-04-09 05:11:47,830]  - Fetching features for default tenant
[2024-04-09 05:24:26,010]  - Commencing graceful shutdown. Waiting for active requests to complete
[2024-04-09 05:24:26,018]  - Graceful shutdown complete
[2024-04-09 05:24:26,086]  - Operator called default onErrorDropped
reactor.core.Exceptions$ErrorCallbackNotImplemented: java.util.concurrent.CancellationException: Disconnected
Caused by: java.util.concurrent.CancellationException: Disconnected
	at reactor.core.publisher.FluxPublish$PublishSubscriber.disconnectAction(FluxPublish.java:327)
	at reactor.core.publisher.FluxPublish$PublishSubscriber.dispose(FluxPublish.java:318)
	at org.springframework.data.redis.connection.lettuce.LettuceReactiveSubscription$State.terminate(LettuceReactiveSubscription.java:271)
	at org.springframework.data.redis.connection.lettuce.LettuceReactiveSubscription.lambda$cancel$8(LettuceReactiveSubscription.java:149)
	at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:45)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:64)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:240)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.onComplete(MonoIgnoreThen.java:203)
	at com.appsmith.server.configurations.MDCConfig$MdcContextLifter.onComplete(MDCConfig.java:63)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.onComplete(MonoIgnoreThen.java:209)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:238)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.onComplete(MonoIgnoreThen.java:203)
	at com.appsmith.server.configurations.MDCConfig$MdcContextLifter.onComplete(MDCConfig.java:63)
	at com.appsmith.server.configurations.MDCConfig$MdcContextLifter.onComplete(MDCConfig.java:63)
	at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.onComplete(Operators.java:2205)
	at com.appsmith.server.configurations.MDCConfig$MdcContextLifter.onComplete(MDCConfig.java:63)
	at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.onComplete(MonoPeekTerminal.java:299)
	at com.appsmith.server.configurations.MDCConfig$MdcContextLifter.onComplete(MDCConfig.java:63)
	at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.onComplete(Operators.java:2205)
	at com.appsmith.server.configurations.MDCConfig$MdcContextLifter.onComplete(MDCConfig.java:63)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.secondComplete(MonoFlatMap.java:250)
	at reactor.core.publisher.MonoFlatMap$FlatMapInner.onComplete(MonoFlatMap.java:324)
	at com.appsmith.server.configurations.MDCConfig$MdcContextLifter.onComplete(MDCConfig.java:63)
	at reactor.core.publisher.MonoIgnoreElements$IgnoreElementsSubscriber.onComplete(MonoIgnoreElements.java:89)
	at com.appsmith.server.configurations.MDCConfig$MdcContextLifter.onComplete(MDCConfig.java:63)
	at io.lettuce.core.RedisPublisher$ImmediateSubscriber.onComplete(RedisPublisher.java:900)
	at io.lettuce.core.RedisPublisher$State.onAllDataRead(RedisPublisher.java:702)
	at io.lettuce.core.RedisPublisher$State$3.read(RedisPublisher.java:612)
	at io.lettuce.core.RedisPublisher$State$3.onDataAvailable(RedisPublisher.java:569)
	at io.lettuce.core.RedisPublisher$RedisSubscription.onDataAvailable(RedisPublisher.java:326)
	at io.lettuce.core.RedisPublisher$RedisSubscription.onAllDataRead(RedisPublisher.java:341)
	at io.lettuce.core.RedisPublisher$SubscriptionCommand.doOnComplete(RedisPublisher.java:782)
	at io.lettuce.core.protocol.CommandWrapper.complete(CommandWrapper.java:65)
	at io.lettuce.core.protocol.CommandWrapper.complete(CommandWrapper.java:63)
	at io.lettuce.core.protocol.CommandHandler.complete(CommandHandler.java:747)
	at io.lettuce.core.pubsub.PubSubCommandHandler.complete(PubSubCommandHandler.java:167)
	at io.lettuce.core.protocol.CommandHandler.decode(CommandHandler.java:682)
	at io.lettuce.core.pubsub.PubSubCommandHandler.decode(PubSubCommandHandler.java:112)
	at io.lettuce.core.protocol.CommandHandler.channelRead(CommandHandler.java:599)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
	at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:800)
	at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:499)
	at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:397)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)
