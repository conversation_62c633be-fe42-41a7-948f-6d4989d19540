1776:C 09 Apr 2024 02:25:55.920 # oO0OoO0OoO0Oo Redis is starting oO0OoO0OoO0Oo
1776:C 09 Apr 2024 02:25:55.920 # Redis version=5.0.7, bits=64, commit=00000000, modified=0, pid=1776, just started
1776:C 09 Apr 2024 02:25:55.920 # Configuration loaded
1776:M 09 Apr 2024 02:25:55.997 * Running mode=standalone, port=6379.
1776:M 09 Apr 2024 02:25:55.998 # Server initialized
1776:M 09 Apr 2024 02:25:55.998 # WARNING you have Transparent Huge Pages (THP) support enabled in your kernel. This will create latency and memory usage issues with Redis. To fix this issue run the command 'echo never > /sys/kernel/mm/transparent_hugepage/enabled' as root, and add it to your /etc/rc.local in order to retain the setting after a reboot. Redis must be restarted after THP is disabled.
1776:M 09 Apr 2024 02:25:55.999 * Ready to accept connections
1776:M 09 Apr 2024 02:26:33.407 * 1 changes in 15 seconds. Saving...
1776:M 09 Apr 2024 02:26:33.408 * Background saving started by pid 2108
2108:C 09 Apr 2024 02:26:33.413 * DB saved on disk
2108:C 09 Apr 2024 02:26:33.414 * RDB: 0 MB of memory used by copy-on-write
1776:M 09 Apr 2024 02:26:33.512 * Background saving terminated with success
1776:M 09 Apr 2024 02:26:56.240 * 1 changes in 15 seconds. Saving...
1776:M 09 Apr 2024 02:26:56.241 * Background saving started by pid 2135
2135:C 09 Apr 2024 02:26:56.263 * DB saved on disk
2135:C 09 Apr 2024 02:26:56.271 * RDB: 0 MB of memory used by copy-on-write
1776:M 09 Apr 2024 02:26:56.342 * Background saving terminated with success
1776:M 09 Apr 2024 02:27:12.079 * 1 changes in 15 seconds. Saving...
1776:M 09 Apr 2024 02:27:12.081 * Background saving started by pid 2156
2156:C 09 Apr 2024 02:27:12.092 * DB saved on disk
2156:C 09 Apr 2024 02:27:12.093 * RDB: 0 MB of memory used by copy-on-write
1776:M 09 Apr 2024 02:27:12.181 * Background saving terminated with success
1776:M 09 Apr 2024 02:29:22.313 * 1 changes in 15 seconds. Saving...
1776:M 09 Apr 2024 02:29:22.314 * Background saving started by pid 2264
2264:C 09 Apr 2024 02:29:22.342 * DB saved on disk
2264:C 09 Apr 2024 02:29:22.350 * RDB: 0 MB of memory used by copy-on-write
1776:M 09 Apr 2024 02:29:22.414 * Background saving terminated with success
1776:M 09 Apr 2024 02:29:38.074 * 1 changes in 15 seconds. Saving...
1776:M 09 Apr 2024 02:29:38.074 * Background saving started by pid 2284
2284:C 09 Apr 2024 02:29:38.080 * DB saved on disk
2284:C 09 Apr 2024 02:29:38.081 * RDB: 0 MB of memory used by copy-on-write
1776:M 09 Apr 2024 02:29:38.176 * Background saving terminated with success
1776:M 09 Apr 2024 02:29:54.017 * 1 changes in 15 seconds. Saving...
1776:M 09 Apr 2024 02:29:54.018 * Background saving started by pid 2296
2296:C 09 Apr 2024 02:29:54.028 * DB saved on disk
2296:C 09 Apr 2024 02:29:54.029 * RDB: 0 MB of memory used by copy-on-write
1776:M 09 Apr 2024 02:29:54.122 * Background saving terminated with success
1776:M 09 Apr 2024 02:30:10.094 * 1 changes in 15 seconds. Saving...
1776:M 09 Apr 2024 02:30:10.094 * Background saving started by pid 2308
2308:C 09 Apr 2024 02:30:10.100 * DB saved on disk
2308:C 09 Apr 2024 02:30:10.101 * RDB: 0 MB of memory used by copy-on-write
1776:M 09 Apr 2024 02:30:10.195 * Background saving terminated with success
1776:M 09 Apr 2024 02:30:26.060 * 1 changes in 15 seconds. Saving...
1776:M 09 Apr 2024 02:30:26.061 * Background saving started by pid 2321
2321:C 09 Apr 2024 02:30:26.066 * DB saved on disk
2321:C 09 Apr 2024 02:30:26.068 * RDB: 0 MB of memory used by copy-on-write
1776:M 09 Apr 2024 02:30:26.162 * Background saving terminated with success
1776:M 09 Apr 2024 02:30:42.033 * 1 changes in 15 seconds. Saving...
1776:M 09 Apr 2024 02:30:42.034 * Background saving started by pid 2337
2337:C 09 Apr 2024 02:30:42.045 * DB saved on disk
2337:C 09 Apr 2024 02:30:42.046 * RDB: 0 MB of memory used by copy-on-write
1776:M 09 Apr 2024 02:30:42.135 * Background saving terminated with success
1776:M 09 Apr 2024 02:30:58.007 * 1 changes in 15 seconds. Saving...
1776:M 09 Apr 2024 02:30:58.008 * Background saving started by pid 2349
2349:C 09 Apr 2024 02:30:58.018 * DB saved on disk
2349:C 09 Apr 2024 02:30:58.019 * RDB: 0 MB of memory used by copy-on-write
1776:M 09 Apr 2024 02:30:58.110 * Background saving terminated with success
1776:M 09 Apr 2024 02:31:14.093 * 1 changes in 15 seconds. Saving...
1776:M 09 Apr 2024 02:31:14.094 * Background saving started by pid 2361
2361:C 09 Apr 2024 02:31:14.127 * DB saved on disk
2361:C 09 Apr 2024 02:31:14.128 * RDB: 0 MB of memory used by copy-on-write
1776:M 09 Apr 2024 02:31:14.195 * Background saving terminated with success
1776:M 09 Apr 2024 02:32:03.667 * 1 changes in 15 seconds. Saving...
1776:M 09 Apr 2024 02:32:03.668 * Background saving started by pid 2395
2395:C 09 Apr 2024 02:32:03.675 * DB saved on disk
2395:C 09 Apr 2024 02:32:03.677 * RDB: 0 MB of memory used by copy-on-write
1776:M 09 Apr 2024 02:32:03.769 * Background saving terminated with success
1776:M 09 Apr 2024 02:35:58.931 * 1 changes in 15 seconds. Saving...
1776:M 09 Apr 2024 02:35:58.937 * Background saving started by pid 2581
2581:C 09 Apr 2024 02:35:58.945 * DB saved on disk
2581:C 09 Apr 2024 02:35:58.945 * RDB: 0 MB of memory used by copy-on-write
1776:M 09 Apr 2024 02:35:59.047 * Background saving terminated with success
1776:M 09 Apr 2024 02:36:15.001 * 1 changes in 15 seconds. Saving...
1776:M 09 Apr 2024 02:36:15.003 * Background saving started by pid 2595
2595:C 09 Apr 2024 02:36:15.014 * DB saved on disk
2595:C 09 Apr 2024 02:36:15.015 * RDB: 0 MB of memory used by copy-on-write
1776:M 09 Apr 2024 02:36:15.104 * Background saving terminated with success
1776:M 09 Apr 2024 02:36:56.153 * 1 changes in 15 seconds. Saving...
1776:M 09 Apr 2024 02:36:56.157 * Background saving started by pid 2628
2628:C 09 Apr 2024 02:36:56.195 * DB saved on disk
2628:C 09 Apr 2024 02:36:56.207 * RDB: 0 MB of memory used by copy-on-write
1776:M 09 Apr 2024 02:36:56.258 * Background saving terminated with success
1776:M 09 Apr 2024 02:37:12.065 * 1 changes in 15 seconds. Saving...
1776:M 09 Apr 2024 02:37:12.066 * Background saving started by pid 2640
2640:C 09 Apr 2024 02:37:12.078 * DB saved on disk
2640:C 09 Apr 2024 02:37:12.082 * RDB: 0 MB of memory used by copy-on-write
1776:M 09 Apr 2024 02:37:12.167 * Background saving terminated with success
1776:M 09 Apr 2024 02:38:19.872 * 1 changes in 15 seconds. Saving...
1776:M 09 Apr 2024 02:38:19.874 * Background saving started by pid 2687
2687:C 09 Apr 2024 02:38:19.914 * DB saved on disk
2687:C 09 Apr 2024 02:38:19.915 * RDB: 0 MB of memory used by copy-on-write
1776:M 09 Apr 2024 02:38:19.975 * Background saving terminated with success
1776:M 09 Apr 2024 02:38:35.016 * 1 changes in 15 seconds. Saving...
1776:M 09 Apr 2024 02:38:35.018 * Background saving started by pid 2700
2700:C 09 Apr 2024 02:38:35.032 * DB saved on disk
2700:C 09 Apr 2024 02:38:35.034 * RDB: 0 MB of memory used by copy-on-write
1776:M 09 Apr 2024 02:38:35.119 * Background saving terminated with success
1776:M 09 Apr 2024 02:39:20.523 * 1 changes in 15 seconds. Saving...
1776:M 09 Apr 2024 02:39:20.550 * Background saving started by pid 2734
2734:C 09 Apr 2024 02:39:20.558 * DB saved on disk
2734:C 09 Apr 2024 02:39:20.559 * RDB: 0 MB of memory used by copy-on-write
1776:M 09 Apr 2024 02:39:20.651 * Background saving terminated with success
1776:M 09 Apr 2024 02:39:39.118 * 1 changes in 15 seconds. Saving...
1776:M 09 Apr 2024 02:39:39.121 * Background saving started by pid 2753
2753:C 09 Apr 2024 02:39:39.159 * DB saved on disk
2753:C 09 Apr 2024 02:39:39.160 * RDB: 0 MB of memory used by copy-on-write
1776:M 09 Apr 2024 02:39:39.223 * Background saving terminated with success
1776:M 09 Apr 2024 02:39:55.016 * 1 changes in 15 seconds. Saving...
1776:M 09 Apr 2024 02:39:55.017 * Background saving started by pid 2766
2766:C 09 Apr 2024 02:39:55.026 * DB saved on disk
2766:C 09 Apr 2024 02:39:55.028 * RDB: 0 MB of memory used by copy-on-write
1776:M 09 Apr 2024 02:39:55.118 * Background saving terminated with success
1776:M 09 Apr 2024 02:40:13.830 * 1 changes in 15 seconds. Saving...
1776:M 09 Apr 2024 02:40:13.830 * Background saving started by pid 2778
2778:C 09 Apr 2024 02:40:13.840 * DB saved on disk
2778:C 09 Apr 2024 02:40:13.842 * RDB: 0 MB of memory used by copy-on-write
1776:M 09 Apr 2024 02:40:13.931 * Background saving terminated with success
1776:M 09 Apr 2024 02:40:29.093 * 1 changes in 15 seconds. Saving...
1776:M 09 Apr 2024 02:40:29.093 * Background saving started by pid 2790
2790:C 09 Apr 2024 02:40:29.098 * DB saved on disk
2790:C 09 Apr 2024 02:40:29.099 * RDB: 0 MB of memory used by copy-on-write
1776:M 09 Apr 2024 02:40:29.194 * Background saving terminated with success
1776:M 09 Apr 2024 02:41:01.753 * 1 changes in 15 seconds. Saving...
1776:M 09 Apr 2024 02:41:01.754 * Background saving started by pid 2825
2825:C 09 Apr 2024 02:41:01.761 * DB saved on disk
2825:C 09 Apr 2024 02:41:01.762 * RDB: 0 MB of memory used by copy-on-write
1776:M 09 Apr 2024 02:41:01.855 * Background saving terminated with success
1776:M 09 Apr 2024 02:41:18.453 * 1 changes in 15 seconds. Saving...
1776:M 09 Apr 2024 02:41:18.453 * Background saving started by pid 2842
2842:C 09 Apr 2024 02:41:18.461 * DB saved on disk
2842:C 09 Apr 2024 02:41:18.463 * RDB: 0 MB of memory used by copy-on-write
1776:M 09 Apr 2024 02:41:18.554 * Background saving terminated with success
1776:M 09 Apr 2024 02:41:58.477 * 1 changes in 15 seconds. Saving...
1776:M 09 Apr 2024 02:41:58.477 * Background saving started by pid 2865
2865:C 09 Apr 2024 02:41:58.486 * DB saved on disk
2865:C 09 Apr 2024 02:41:58.488 * RDB: 0 MB of memory used by copy-on-write
1776:M 09 Apr 2024 02:41:58.582 * Background saving terminated with success
1776:M 09 Apr 2024 02:42:14.054 * 1 changes in 15 seconds. Saving...
1776:M 09 Apr 2024 02:42:14.054 * Background saving started by pid 2879
2879:C 09 Apr 2024 02:42:14.060 * DB saved on disk
2879:C 09 Apr 2024 02:42:14.061 * RDB: 0 MB of memory used by copy-on-write
1776:M 09 Apr 2024 02:42:14.155 * Background saving terminated with success
1776:M 09 Apr 2024 02:42:30.001 * 1 changes in 15 seconds. Saving...
1776:M 09 Apr 2024 02:42:30.001 * Background saving started by pid 2896
2896:C 09 Apr 2024 02:42:30.008 * DB saved on disk
2896:C 09 Apr 2024 02:42:30.009 * RDB: 0 MB of memory used by copy-on-write
1776:M 09 Apr 2024 02:42:30.101 * Background saving terminated with success
1776:M 09 Apr 2024 02:42:46.075 * 1 changes in 15 seconds. Saving...
1776:M 09 Apr 2024 02:42:46.079 * Background saving started by pid 2908
2908:C 09 Apr 2024 02:42:46.092 * DB saved on disk
2908:C 09 Apr 2024 02:42:46.094 * RDB: 0 MB of memory used by copy-on-write
1776:M 09 Apr 2024 02:42:46.179 * Background saving terminated with success
1776:signal-handler (1712631677) Received SIGTERM scheduling shutdown...
1776:M 09 Apr 2024 03:01:17.617 # User requested shutdown...
1776:M 09 Apr 2024 03:01:17.617 * Saving the final RDB snapshot before exiting.
1776:M 09 Apr 2024 03:01:17.736 * DB saved on disk
1776:M 09 Apr 2024 03:01:17.736 # Redis is now ready to exit, bye bye...
1502:C 09 Apr 2024 03:11:20.393 # oO0OoO0OoO0Oo Redis is starting oO0OoO0OoO0Oo
1502:C 09 Apr 2024 03:11:20.393 # Redis version=5.0.7, bits=64, commit=00000000, modified=0, pid=1502, just started
1502:C 09 Apr 2024 03:11:20.393 # Configuration loaded
1502:M 09 Apr 2024 03:11:20.401 * Running mode=standalone, port=6379.
1502:M 09 Apr 2024 03:11:20.401 # Server initialized
1502:M 09 Apr 2024 03:11:20.401 # WARNING you have Transparent Huge Pages (THP) support enabled in your kernel. This will create latency and memory usage issues with Redis. To fix this issue run the command 'echo never > /sys/kernel/mm/transparent_hugepage/enabled' as root, and add it to your /etc/rc.local in order to retain the setting after a reboot. Redis must be restarted after THP is disabled.
1502:M 09 Apr 2024 03:11:20.412 * DB loaded from disk: 0.011 seconds
1502:M 09 Apr 2024 03:11:20.412 * Ready to accept connections
1502:M 09 Apr 2024 03:15:19.147 * 1 changes in 15 seconds. Saving...
1502:M 09 Apr 2024 03:15:19.149 * Background saving started by pid 1975
1975:C 09 Apr 2024 03:15:19.156 * DB saved on disk
1975:C 09 Apr 2024 03:15:19.156 * RDB: 0 MB of memory used by copy-on-write
1502:M 09 Apr 2024 03:15:19.249 * Background saving terminated with success
1502:M 09 Apr 2024 03:15:35.037 * 1 changes in 15 seconds. Saving...
1502:M 09 Apr 2024 03:15:35.037 * Background saving started by pid 1995
1995:C 09 Apr 2024 03:15:35.049 * DB saved on disk
1995:C 09 Apr 2024 03:15:35.050 * RDB: 0 MB of memory used by copy-on-write
1502:M 09 Apr 2024 03:15:35.138 * Background saving terminated with success
1502:M 09 Apr 2024 03:15:51.100 * 1 changes in 15 seconds. Saving...
1502:M 09 Apr 2024 03:15:51.102 * Background saving started by pid 2015
2015:C 09 Apr 2024 03:15:51.115 * DB saved on disk
2015:C 09 Apr 2024 03:15:51.116 * RDB: 0 MB of memory used by copy-on-write
1502:M 09 Apr 2024 03:15:51.203 * Background saving terminated with success
1502:M 09 Apr 2024 03:16:09.106 * 1 changes in 15 seconds. Saving...
1502:M 09 Apr 2024 03:16:09.106 * Background saving started by pid 2028
2028:C 09 Apr 2024 03:16:09.115 * DB saved on disk
2028:C 09 Apr 2024 03:16:09.116 * RDB: 0 MB of memory used by copy-on-write
1502:M 09 Apr 2024 03:16:09.208 * Background saving terminated with success
1502:M 09 Apr 2024 03:16:25.055 * 1 changes in 15 seconds. Saving...
1502:M 09 Apr 2024 03:16:25.057 * Background saving started by pid 2050
2050:C 09 Apr 2024 03:16:25.071 * DB saved on disk
2050:C 09 Apr 2024 03:16:25.072 * RDB: 0 MB of memory used by copy-on-write
1502:M 09 Apr 2024 03:16:25.157 * Background saving terminated with success
1502:M 09 Apr 2024 03:16:41.086 * 1 changes in 15 seconds. Saving...
1502:M 09 Apr 2024 03:16:41.086 * Background saving started by pid 2065
2065:C 09 Apr 2024 03:16:41.094 * DB saved on disk
2065:C 09 Apr 2024 03:16:41.096 * RDB: 0 MB of memory used by copy-on-write
1502:M 09 Apr 2024 03:16:41.187 * Background saving terminated with success
1502:M 09 Apr 2024 03:16:57.049 * 1 changes in 15 seconds. Saving...
1502:M 09 Apr 2024 03:16:57.050 * Background saving started by pid 2078
2078:C 09 Apr 2024 03:16:57.055 * DB saved on disk
2078:C 09 Apr 2024 03:16:57.056 * RDB: 0 MB of memory used by copy-on-write
1502:M 09 Apr 2024 03:16:57.152 * Background saving terminated with success
1502:M 09 Apr 2024 03:17:13.093 * 1 changes in 15 seconds. Saving...
1502:M 09 Apr 2024 03:17:13.094 * Background saving started by pid 2126
2126:C 09 Apr 2024 03:17:13.109 * DB saved on disk
2126:C 09 Apr 2024 03:17:13.116 * RDB: 0 MB of memory used by copy-on-write
1502:M 09 Apr 2024 03:17:13.198 * Background saving terminated with success
1502:M 09 Apr 2024 03:17:29.076 * 1 changes in 15 seconds. Saving...
1502:M 09 Apr 2024 03:17:29.077 * Background saving started by pid 2140
2140:C 09 Apr 2024 03:17:29.083 * DB saved on disk
2140:C 09 Apr 2024 03:17:29.084 * RDB: 0 MB of memory used by copy-on-write
1502:M 09 Apr 2024 03:17:29.178 * Background saving terminated with success
1502:M 09 Apr 2024 03:17:45.079 * 1 changes in 15 seconds. Saving...
1502:M 09 Apr 2024 03:17:45.081 * Background saving started by pid 2153
2153:C 09 Apr 2024 03:17:45.090 * DB saved on disk
2153:C 09 Apr 2024 03:17:45.092 * RDB: 0 MB of memory used by copy-on-write
1502:M 09 Apr 2024 03:17:45.181 * Background saving terminated with success
1502:M 09 Apr 2024 03:18:17.784 * 1 changes in 15 seconds. Saving...
1502:M 09 Apr 2024 03:18:17.785 * Background saving started by pid 2189
2189:C 09 Apr 2024 03:18:17.794 * DB saved on disk
2189:C 09 Apr 2024 03:18:17.795 * RDB: 0 MB of memory used by copy-on-write
1502:M 09 Apr 2024 03:18:17.889 * Background saving terminated with success
1502:M 09 Apr 2024 03:18:33.024 * 1 changes in 15 seconds. Saving...
1502:M 09 Apr 2024 03:18:33.025 * Background saving started by pid 2204
2204:C 09 Apr 2024 03:18:33.033 * DB saved on disk
2204:C 09 Apr 2024 03:18:33.034 * RDB: 0 MB of memory used by copy-on-write
1502:M 09 Apr 2024 03:18:33.125 * Background saving terminated with success
1502:M 09 Apr 2024 03:18:52.646 * 1 changes in 15 seconds. Saving...
1502:M 09 Apr 2024 03:18:52.646 * Background saving started by pid 2217
2217:C 09 Apr 2024 03:18:52.657 * DB saved on disk
2217:C 09 Apr 2024 03:18:52.658 * RDB: 0 MB of memory used by copy-on-write
1502:M 09 Apr 2024 03:18:52.748 * Background saving terminated with success
1502:M 09 Apr 2024 03:20:04.259 * 1 changes in 15 seconds. Saving...
1502:M 09 Apr 2024 03:20:04.260 * Background saving started by pid 2275
2275:C 09 Apr 2024 03:20:04.270 * DB saved on disk
2275:C 09 Apr 2024 03:20:04.271 * RDB: 0 MB of memory used by copy-on-write
1502:M 09 Apr 2024 03:20:04.362 * Background saving terminated with success
1502:M 09 Apr 2024 03:20:20.049 * 1 changes in 15 seconds. Saving...
1502:M 09 Apr 2024 03:20:20.051 * Background saving started by pid 2292
2292:C 09 Apr 2024 03:20:20.066 * DB saved on disk
2292:C 09 Apr 2024 03:20:20.067 * RDB: 0 MB of memory used by copy-on-write
1502:M 09 Apr 2024 03:20:20.152 * Background saving terminated with success
1502:M 09 Apr 2024 04:26:19.011 * 1 changes in 15 seconds. Saving...
1502:M 09 Apr 2024 04:26:19.028 * Background saving started by pid 3830
3830:C 09 Apr 2024 04:26:19.043 * DB saved on disk
3830:C 09 Apr 2024 04:26:19.045 * RDB: 0 MB of memory used by copy-on-write
1502:M 09 Apr 2024 04:26:19.128 * Background saving terminated with success
1502:M 09 Apr 2024 04:26:35.068 * 1 changes in 15 seconds. Saving...
1502:M 09 Apr 2024 04:26:35.154 * Background saving started by pid 3835
3835:C 09 Apr 2024 04:26:35.858 * DB saved on disk
3835:C 09 Apr 2024 04:26:35.951 * RDB: 0 MB of memory used by copy-on-write
1502:M 09 Apr 2024 04:26:36.046 * Background saving terminated with success
