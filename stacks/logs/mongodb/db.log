{"t":{"$date":"2024-04-09T03:11:20.750+00:00"},"s":"I",  "c":"NETWORK",  "id":4915701, "ctx":"-","msg":"Initialized wire specification","attr":{"spec":{"incomingExternalClient":{"minWireVersion":0,"maxWireVersion":13},"incomingInternalClient":{"minWireVersion":0,"maxWireVersion":13},"outgoing":{"minWireVersion":0,"maxWireVersion":13},"isInternalClient":true}}}
{"t":{"$date":"2024-04-09T03:11:20.751+00:00"},"s":"I",  "c":"CONTROL",  "id":23285,   "ctx":"-","msg":"Automatically disabling TLS 1.0, to force-enable TLS 1.0 specify --sslDisabledProtocols 'none'"}
{"t":{"$date":"2024-04-09T03:11:20.758+00:00"},"s":"W",  "c":"ASIO",     "id":22601,   "ctx":"main","msg":"No TransportLayer configured during NetworkInterface startup"}
{"t":{"$date":"2024-04-09T03:11:20.760+00:00"},"s":"I",  "c":"NETWORK",  "id":4648601, "ctx":"main","msg":"Implicit TCP FastOpen unavailable. If TCP FastOpen is required, set tcpFastOpenServer, tcpFastOpenClient, and tcpFastOpenQueueSize."}
{"t":{"$date":"2024-04-09T03:11:20.875+00:00"},"s":"W",  "c":"ASIO",     "id":22601,   "ctx":"main","msg":"No TransportLayer configured during NetworkInterface startup"}
{"t":{"$date":"2024-04-09T03:11:20.878+00:00"},"s":"W",  "c":"ASIO",     "id":22601,   "ctx":"main","msg":"No TransportLayer configured during NetworkInterface startup"}
{"t":{"$date":"2024-04-09T03:11:20.880+00:00"},"s":"I",  "c":"REPL",     "id":5123008, "ctx":"main","msg":"Successfully registered PrimaryOnlyService","attr":{"service":"TenantMigrationDonorService","ns":"config.tenantMigrationDonors"}}
{"t":{"$date":"2024-04-09T03:11:20.881+00:00"},"s":"I",  "c":"REPL",     "id":5123008, "ctx":"main","msg":"Successfully registered PrimaryOnlyService","attr":{"service":"TenantMigrationRecipientService","ns":"config.tenantMigrationRecipients"}}
{"t":{"$date":"2024-04-09T03:11:20.882+00:00"},"s":"I",  "c":"CONTROL",  "id":5945603, "ctx":"main","msg":"Multi threading initialized"}
{"t":{"$date":"2024-04-09T03:11:20.883+00:00"},"s":"I",  "c":"CONTROL",  "id":4615611, "ctx":"initandlisten","msg":"MongoDB starting","attr":{"pid":1503,"port":27017,"dbPath":".","architecture":"64-bit","host":"f3f425c8c882"}}
{"t":{"$date":"2024-04-09T03:11:20.885+00:00"},"s":"I",  "c":"CONTROL",  "id":23403,   "ctx":"initandlisten","msg":"Build Info","attr":{"buildInfo":{"version":"5.0.26","gitVersion":"0b4f1ea980b5380a66425a90b414106a191365f4","openSSLVersion":"OpenSSL 1.1.1f  31 Mar 2020","modules":[],"allocator":"tcmalloc","environment":{"distmod":"ubuntu2004","distarch":"x86_64","target_arch":"x86_64"}}}}
{"t":{"$date":"2024-04-09T03:11:20.886+00:00"},"s":"I",  "c":"CONTROL",  "id":51765,   "ctx":"initandlisten","msg":"Operating System","attr":{"os":{"name":"Ubuntu","version":"20.04"}}}
{"t":{"$date":"2024-04-09T03:11:20.887+00:00"},"s":"I",  "c":"CONTROL",  "id":21951,   "ctx":"initandlisten","msg":"Options set by command line","attr":{"options":{"net":{"bindIp":"localhost","port":27017},"replication":{"replSet":"mr1"},"security":{"keyFile":"/tmp/appsmith/mongodb-key"},"storage":{"dbPath":"."},"systemLog":{"destination":"file","path":"/appsmith-stacks/logs/mongodb/db.log"}}}}
{"t":{"$date":"2024-04-09T03:11:20.900+00:00"},"s":"I",  "c":"STORAGE",  "id":22270,   "ctx":"initandlisten","msg":"Storage engine to use detected by data files","attr":{"dbpath":".","storageEngine":"wiredTiger"}}
{"t":{"$date":"2024-04-09T03:11:20.914+00:00"},"s":"I",  "c":"STORAGE",  "id":22315,   "ctx":"initandlisten","msg":"Opening WiredTiger","attr":{"config":"create,cache_size=3467M,session_max=33000,eviction=(threads_min=4,threads_max=4),config_base=false,statistics=(fast),log=(enabled=true,archive=true,path=journal,compressor=snappy),builtin_extension_config=(zstd=(compression_level=6)),file_manager=(close_idle_time=600,close_scan_interval=10,close_handle_minimum=2000),statistics_log=(wait=0),verbose=[recovery_progress,checkpoint_progress,compact_progress],"}}
{"t":{"$date":"2024-04-09T03:11:21.183+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"initandlisten","msg":"WiredTiger message","attr":{"message":"[1712632281:183107][1503:0x7f42392c4c80], txn-recover: [WT_VERB_RECOVERY_PROGRESS] Recovering log 3 through 4"}}
{"t":{"$date":"2024-04-09T03:11:22.373+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"initandlisten","msg":"WiredTiger message","attr":{"message":"[1712632282:373107][1503:0x7f42392c4c80], txn-recover: [WT_VERB_RECOVERY_PROGRESS] Recovering log 4 through 4"}}
{"t":{"$date":"2024-04-09T03:11:23.765+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"initandlisten","msg":"WiredTiger message","attr":{"message":"[1712632283:765724][1503:0x7f42392c4c80], txn-recover: [WT_VERB_RECOVERY_ALL] Main recovery loop: starting at 3/861952 to 4/256"}}
{"t":{"$date":"2024-04-09T03:11:23.902+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"initandlisten","msg":"WiredTiger message","attr":{"message":"[1712632283:902728][1503:0x7f42392c4c80], txn-recover: [WT_VERB_RECOVERY_PROGRESS] Recovering log 3 through 4"}}
{"t":{"$date":"2024-04-09T03:11:24.018+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"initandlisten","msg":"WiredTiger message","attr":{"message":"[1712632284:18910][1503:0x7f42392c4c80], txn-recover: [WT_VERB_RECOVERY_PROGRESS] Recovering log 4 through 4"}}
{"t":{"$date":"2024-04-09T03:11:24.090+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"initandlisten","msg":"WiredTiger message","attr":{"message":"[1712632284:89965][1503:0x7f42392c4c80], txn-recover: [WT_VERB_RECOVERY_PROGRESS] recovery log replay has successfully finished and ran for 2913 milliseconds"}}
{"t":{"$date":"2024-04-09T03:11:24.092+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"initandlisten","msg":"WiredTiger message","attr":{"message":"[1712632284:92850][1503:0x7f42392c4c80], txn-recover: [WT_VERB_RECOVERY_ALL] Set global recovery timestamp: (1712631673, 1)"}}
{"t":{"$date":"2024-04-09T03:11:24.093+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"initandlisten","msg":"WiredTiger message","attr":{"message":"[1712632284:93676][1503:0x7f42392c4c80], txn-recover: [WT_VERB_RECOVERY_ALL] Set global oldest timestamp: (**********, 1)"}}
{"t":{"$date":"2024-04-09T03:11:24.101+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"initandlisten","msg":"WiredTiger message","attr":{"message":"[1712632284:101017][1503:0x7f42392c4c80], txn-recover: [WT_VERB_RECOVERY_PROGRESS] recovery rollback to stable has successfully finished and ran for 6 milliseconds"}}
{"t":{"$date":"2024-04-09T03:11:24.121+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"initandlisten","msg":"WiredTiger message","attr":{"message":"[1712632284:121804][1503:0x7f42392c4c80], WT_SESSION.checkpoint: [WT_VERB_CHECKPOINT_PROGRESS] saving checkpoint snapshot min: 1, snapshot max: 1 snapshot count: 0, oldest timestamp: (**********, 1) , meta checkpoint timestamp: (1712631673, 1) base write gen: 435"}}
{"t":{"$date":"2024-04-09T03:11:24.138+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"initandlisten","msg":"WiredTiger message","attr":{"message":"[1712632284:138110][1503:0x7f42392c4c80], txn-recover: [WT_VERB_RECOVERY_PROGRESS] recovery checkpoint has successfully finished and ran for 35 milliseconds"}}
{"t":{"$date":"2024-04-09T03:11:24.144+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"initandlisten","msg":"WiredTiger message","attr":{"message":"[1712632284:144803][1503:0x7f42392c4c80], txn-recover: [WT_VERB_RECOVERY_PROGRESS] recovery was completed successfully and took 2968ms, including 2913ms for the log replay, 6ms for the rollback to stable, and 35ms for the checkpoint."}}
{"t":{"$date":"2024-04-09T03:11:24.147+00:00"},"s":"I",  "c":"STORAGE",  "id":4795906, "ctx":"initandlisten","msg":"WiredTiger opened","attr":{"durationMillis":3230}}
{"t":{"$date":"2024-04-09T03:11:24.148+00:00"},"s":"I",  "c":"RECOVERY", "id":23987,   "ctx":"initandlisten","msg":"WiredTiger recoveryTimestamp","attr":{"recoveryTimestamp":{"$timestamp":{"t":1712631673,"i":1}}}}
{"t":{"$date":"2024-04-09T03:11:24.149+00:00"},"s":"I",  "c":"RECOVERY", "id":5380106, "ctx":"initandlisten","msg":"WiredTiger oldestTimestamp","attr":{"oldestTimestamp":{"$timestamp":{"t":**********,"i":1}}}}
{"t":{"$date":"2024-04-09T03:11:24.182+00:00"},"s":"I",  "c":"STORAGE",  "id":22383,   "ctx":"initandlisten","msg":"The size storer reports that the oplog contains","attr":{"numRecords":702,"dataSize":568315}}
{"t":{"$date":"2024-04-09T03:11:24.183+00:00"},"s":"I",  "c":"STORAGE",  "id":22384,   "ctx":"initandlisten","msg":"Scanning the oplog to determine where to place markers for truncation"}
{"t":{"$date":"2024-04-09T03:11:24.202+00:00"},"s":"I",  "c":"STORAGE",  "id":22382,   "ctx":"initandlisten","msg":"WiredTiger record store oplog processing finished","attr":{"durationMillis":19}}
{"t":{"$date":"2024-04-09T03:11:24.227+00:00"},"s":"I",  "c":"STORAGE",  "id":22262,   "ctx":"initandlisten","msg":"Timestamp monitor starting"}
{"t":{"$date":"2024-04-09T03:11:24.240+00:00"},"s":"W",  "c":"CONTROL",  "id":22138,   "ctx":"initandlisten","msg":"You are running this process as the root user, which is not recommended","tags":["startupWarnings"]}
{"t":{"$date":"2024-04-09T03:11:24.276+00:00"},"s":"I",  "c":"NETWORK",  "id":4915702, "ctx":"initandlisten","msg":"Updated wire specification","attr":{"oldSpec":{"incomingExternalClient":{"minWireVersion":0,"maxWireVersion":13},"incomingInternalClient":{"minWireVersion":0,"maxWireVersion":13},"outgoing":{"minWireVersion":0,"maxWireVersion":13},"isInternalClient":true},"newSpec":{"incomingExternalClient":{"minWireVersion":0,"maxWireVersion":13},"incomingInternalClient":{"minWireVersion":13,"maxWireVersion":13},"outgoing":{"minWireVersion":13,"maxWireVersion":13},"isInternalClient":true}}}
{"t":{"$date":"2024-04-09T03:11:24.287+00:00"},"s":"I",  "c":"STORAGE",  "id":5071100, "ctx":"initandlisten","msg":"Clearing temp directory"}
{"t":{"$date":"2024-04-09T03:11:24.452+00:00"},"s":"I",  "c":"CONTROL",  "id":20536,   "ctx":"initandlisten","msg":"Flow Control is enabled on this deployment"}
{"t":{"$date":"2024-04-09T03:11:24.453+00:00"},"s":"I",  "c":"STORAGE",  "id":5380103, "ctx":"initandlisten","msg":"Unpin oldest timestamp request","attr":{"service":"_wt_startup","requestedTs":{"$timestamp":{"t":**********,"i":1}}}}
{"t":{"$date":"2024-04-09T03:11:24.476+00:00"},"s":"I",  "c":"SHARDING", "id":20997,   "ctx":"initandlisten","msg":"Refreshed RWC defaults","attr":{"newDefaults":{}}}
{"t":{"$date":"2024-04-09T03:11:24.483+00:00"},"s":"I",  "c":"FTDC",     "id":20625,   "ctx":"initandlisten","msg":"Initializing full-time diagnostic data capture","attr":{"dataDirectory":"./diagnostic.data"}}
{"t":{"$date":"2024-04-09T03:11:24.500+00:00"},"s":"I",  "c":"REPL",     "id":6015317, "ctx":"initandlisten","msg":"Setting new configuration state","attr":{"newState":"ConfigStartingUp","oldState":"ConfigPreStart"}}
{"t":{"$date":"2024-04-09T03:11:24.501+00:00"},"s":"I",  "c":"REPL",     "id":4280500, "ctx":"initandlisten","msg":"Attempting to create internal replication collections"}
{"t":{"$date":"2024-04-09T03:11:24.527+00:00"},"s":"I",  "c":"REPL",     "id":4280501, "ctx":"initandlisten","msg":"Attempting to load local voted for document"}
{"t":{"$date":"2024-04-09T03:11:24.530+00:00"},"s":"I",  "c":"REPL",     "id":4280502, "ctx":"initandlisten","msg":"Searching for local Rollback ID document"}
{"t":{"$date":"2024-04-09T03:11:24.539+00:00"},"s":"I",  "c":"-",        "id":4939300, "ctx":"monitoring-keys-for-HMAC","msg":"Failed to refresh key cache","attr":{"error":"ReadConcernMajorityNotAvailableYet: Read concern majority reads are currently not possible.","nextWakeupMillis":200}}
{"t":{"$date":"2024-04-09T03:11:24.551+00:00"},"s":"I",  "c":"REPL",     "id":21529,   "ctx":"initandlisten","msg":"Initializing rollback ID","attr":{"rbid":1}}
{"t":{"$date":"2024-04-09T03:11:24.554+00:00"},"s":"I",  "c":"REPL",     "id":4280504, "ctx":"initandlisten","msg":"Cleaning up any partially applied oplog batches & reading last op from oplog"}
{"t":{"$date":"2024-04-09T03:11:24.563+00:00"},"s":"I",  "c":"REPL",     "id":21544,   "ctx":"initandlisten","msg":"Recovering from stable timestamp","attr":{"stableTimestamp":{"$timestamp":{"t":1712631673,"i":1}},"topOfOplog":{"ts":{"$timestamp":{"t":1712631673,"i":1}},"t":2},"appliedThrough":{"ts":{"$timestamp":{"t":0,"i":0}},"t":-1}}}
{"t":{"$date":"2024-04-09T03:11:24.565+00:00"},"s":"I",  "c":"REPL",     "id":21545,   "ctx":"initandlisten","msg":"Starting recovery oplog application at the stable timestamp","attr":{"stableTimestamp":{"$timestamp":{"t":1712631673,"i":1}}}}
{"t":{"$date":"2024-04-09T03:11:24.567+00:00"},"s":"I",  "c":"REPL",     "id":5466604, "ctx":"initandlisten","msg":"Start point for recovery oplog application exists in oplog. No adjustment necessary","attr":{"startPoint":{"$timestamp":{"t":1712631673,"i":1}}}}
{"t":{"$date":"2024-04-09T03:11:24.567+00:00"},"s":"I",  "c":"REPL",     "id":21549,   "ctx":"initandlisten","msg":"No oplog entries to apply for recovery. Start point is at the top of the oplog"}
{"t":{"$date":"2024-04-09T03:11:24.568+00:00"},"s":"I",  "c":"REPL",     "id":4280505, "ctx":"initandlisten","msg":"Creating any necessary TenantMigrationAccessBlockers for unfinished migrations"}
{"t":{"$date":"2024-04-09T03:11:24.583+00:00"},"s":"I",  "c":"REPL",     "id":4280506, "ctx":"initandlisten","msg":"Reconstructing prepared transactions"}
{"t":{"$date":"2024-04-09T03:11:24.593+00:00"},"s":"I",  "c":"REPL",     "id":4280507, "ctx":"initandlisten","msg":"Loaded replica set config, scheduled callback to set local config"}
{"t":{"$date":"2024-04-09T03:11:24.594+00:00"},"s":"I",  "c":"REPL",     "id":4280508, "ctx":"ReplCoord-0","msg":"Attempting to set local replica set config; validating config for startup"}
{"t":{"$date":"2024-04-09T03:11:24.605+00:00"},"s":"I",  "c":"CONTROL",  "id":20714,   "ctx":"LogicalSessionCacheRefresh","msg":"Failed to refresh session cache, will try again at the next refresh interval","attr":{"error":"NotYetInitialized: Replication has not yet been configured"}}
{"t":{"$date":"2024-04-09T03:11:24.607+00:00"},"s":"I",  "c":"REPL",     "id":40440,   "ctx":"initandlisten","msg":"Starting the TopologyVersionObserver"}
{"t":{"$date":"2024-04-09T03:11:24.609+00:00"},"s":"I",  "c":"CONTROL",  "id":20711,   "ctx":"LogicalSessionCacheReap","msg":"Failed to reap transaction table","attr":{"error":"NotYetInitialized: Replication has not yet been configured"}}
{"t":{"$date":"2024-04-09T03:11:24.610+00:00"},"s":"I",  "c":"REPL",     "id":40445,   "ctx":"TopologyVersionObserver","msg":"Started TopologyVersionObserver"}
{"t":{"$date":"2024-04-09T03:11:24.611+00:00"},"s":"I",  "c":"NETWORK",  "id":23015,   "ctx":"listener","msg":"Listening on","attr":{"address":"/tmp/mongodb-27017.sock"}}
{"t":{"$date":"2024-04-09T03:11:24.612+00:00"},"s":"I",  "c":"NETWORK",  "id":23015,   "ctx":"listener","msg":"Listening on","attr":{"address":"127.0.0.1"}}
{"t":{"$date":"2024-04-09T03:11:24.612+00:00"},"s":"I",  "c":"NETWORK",  "id":23016,   "ctx":"listener","msg":"Waiting for connections","attr":{"port":27017,"ssl":"off"}}
{"t":{"$date":"2024-04-09T03:11:24.618+00:00"},"s":"I",  "c":"NETWORK",  "id":22943,   "ctx":"listener","msg":"Connection accepted","attr":{"remote":"127.0.0.1:37768","uuid":"d9e97193-472c-4048-b952-e59e2e9fb53a","connectionId":2,"connectionCount":1}}
{"t":{"$date":"2024-04-09T03:11:24.640+00:00"},"s":"W",  "c":"COMMAND",  "id":5578800, "ctx":"conn2","msg":"Deprecated operation requested. The client driver may require an upgrade in order to ensure compatibility with future server versions. For more details see https://dochub.mongodb.org/core/legacy-opcode-compatibility","attr":{"op":"query","clientInfo":{"address":"127.0.0.1:37768"}}}
{"t":{"$date":"2024-04-09T03:11:24.740+00:00"},"s":"I",  "c":"-",        "id":4939300, "ctx":"monitoring-keys-for-HMAC","msg":"Failed to refresh key cache","attr":{"error":"ReadConcernMajorityNotAvailableYet: Read concern majority reads are currently not possible.","nextWakeupMillis":400}}
{"t":{"$date":"2024-04-09T03:11:24.761+00:00"},"s":"I",  "c":"ACCESS",   "id":20250,   "ctx":"conn2","msg":"Authentication succeeded","attr":{"mechanism":"SCRAM-SHA-256","speculative":false,"principalName":"__system","authenticationDatabase":"local","remote":"127.0.0.1:37768","extraInfo":{}}}
{"t":{"$date":"2024-04-09T03:11:24.766+00:00"},"s":"I",  "c":"REPL",     "id":4280509, "ctx":"ReplCoord-0","msg":"Local configuration validated for startup"}
{"t":{"$date":"2024-04-09T03:11:24.767+00:00"},"s":"I",  "c":"NETWORK",  "id":22944,   "ctx":"conn2","msg":"Connection ended","attr":{"remote":"127.0.0.1:37768","uuid":"d9e97193-472c-4048-b952-e59e2e9fb53a","connectionId":2,"connectionCount":0}}
{"t":{"$date":"2024-04-09T03:11:24.769+00:00"},"s":"I",  "c":"REPL",     "id":6015317, "ctx":"ReplCoord-0","msg":"Setting new configuration state","attr":{"newState":"ConfigSteady","oldState":"ConfigStartingUp"}}
{"t":{"$date":"2024-04-09T03:11:24.769+00:00"},"s":"I",  "c":"REPL",     "id":21392,   "ctx":"ReplCoord-0","msg":"New replica set config in use","attr":{"config":{"_id":"mr1","version":1,"term":2,"members":[{"_id":0,"host":"localhost:27017","arbiterOnly":false,"buildIndexes":true,"hidden":false,"priority":1,"tags":{},"secondaryDelaySecs":0,"votes":1}],"protocolVersion":1,"writeConcernMajorityJournalDefault":true,"settings":{"chainingAllowed":true,"heartbeatIntervalMillis":2000,"heartbeatTimeoutSecs":10,"electionTimeoutMillis":10000,"catchUpTimeoutMillis":-1,"catchUpTakeoverDelayMillis":30000,"getLastErrorModes":{},"getLastErrorDefaults":{"w":1,"wtimeout":0},"replicaSetId":{"$oid":"6614a714bc5de5bfe3a48011"}}}}}
{"t":{"$date":"2024-04-09T03:11:24.770+00:00"},"s":"I",  "c":"REPL",     "id":21393,   "ctx":"ReplCoord-0","msg":"Found self in config","attr":{"hostAndPort":"localhost:27017"}}
{"t":{"$date":"2024-04-09T03:11:24.771+00:00"},"s":"I",  "c":"REPL",     "id":21358,   "ctx":"ReplCoord-0","msg":"Replica set state transition","attr":{"newState":"STARTUP2","oldState":"STARTUP"}}
{"t":{"$date":"2024-04-09T03:11:24.772+00:00"},"s":"I",  "c":"REPL",     "id":21320,   "ctx":"ReplCoord-0","msg":"Updated term","attr":{"term":2}}
{"t":{"$date":"2024-04-09T03:11:24.772+00:00"},"s":"I",  "c":"REPL",     "id":21306,   "ctx":"ReplCoord-0","msg":"Starting replication storage threads"}
{"t":{"$date":"2024-04-09T03:11:24.774+00:00"},"s":"I",  "c":"REPL",     "id":4280512, "ctx":"ReplCoord-0","msg":"No initial sync required. Attempting to begin steady replication"}
{"t":{"$date":"2024-04-09T03:11:24.775+00:00"},"s":"I",  "c":"REPL",     "id":21358,   "ctx":"ReplCoord-0","msg":"Replica set state transition","attr":{"newState":"RECOVERING","oldState":"STARTUP2"}}
{"t":{"$date":"2024-04-09T03:11:24.784+00:00"},"s":"I",  "c":"REPL",     "id":21299,   "ctx":"ReplCoord-0","msg":"Starting replication fetcher thread"}
{"t":{"$date":"2024-04-09T03:11:24.785+00:00"},"s":"I",  "c":"REPL",     "id":21300,   "ctx":"ReplCoord-0","msg":"Starting replication applier thread"}
{"t":{"$date":"2024-04-09T03:11:24.785+00:00"},"s":"I",  "c":"REPL",     "id":21301,   "ctx":"ReplCoord-0","msg":"Starting replication reporter thread"}
{"t":{"$date":"2024-04-09T03:11:24.786+00:00"},"s":"I",  "c":"REPL",     "id":4280511, "ctx":"ReplCoord-0","msg":"Set local replica set config"}
{"t":{"$date":"2024-04-09T03:11:24.786+00:00"},"s":"I",  "c":"REPL",     "id":21224,   "ctx":"OplogApplier-0","msg":"Starting oplog application"}
{"t":{"$date":"2024-04-09T03:11:24.798+00:00"},"s":"I",  "c":"REPL",     "id":21358,   "ctx":"OplogApplier-0","msg":"Replica set state transition","attr":{"newState":"SECONDARY","oldState":"RECOVERING"}}
{"t":{"$date":"2024-04-09T03:11:24.801+00:00"},"s":"I",  "c":"ELECTION", "id":4615652, "ctx":"OplogApplier-0","msg":"Starting an election, since we've seen no PRIMARY in election timeout period","attr":{"electionTimeoutPeriodMillis":10000}}
{"t":{"$date":"2024-04-09T03:11:24.803+00:00"},"s":"I",  "c":"ELECTION", "id":21438,   "ctx":"OplogApplier-0","msg":"Conducting a dry run election to see if we could be elected","attr":{"currentTerm":2}}
{"t":{"$date":"2024-04-09T03:11:24.805+00:00"},"s":"I",  "c":"ELECTION", "id":21444,   "ctx":"ReplCoord-0","msg":"Dry election run succeeded, running for election","attr":{"newTerm":3}}
{"t":{"$date":"2024-04-09T03:11:24.806+00:00"},"s":"I",  "c":"ELECTION", "id":6015300, "ctx":"ReplCoord-0","msg":"Storing last vote document in local storage for my election","attr":{"lastVote":{"term":3,"candidateIndex":0}}}
{"t":{"$date":"2024-04-09T03:11:24.809+00:00"},"s":"I",  "c":"ELECTION", "id":21450,   "ctx":"ReplCoord-0","msg":"Election succeeded, assuming primary role","attr":{"term":3}}
{"t":{"$date":"2024-04-09T03:11:24.810+00:00"},"s":"I",  "c":"REPL",     "id":21358,   "ctx":"ReplCoord-0","msg":"Replica set state transition","attr":{"newState":"PRIMARY","oldState":"SECONDARY"}}
{"t":{"$date":"2024-04-09T03:11:24.810+00:00"},"s":"I",  "c":"REPL",     "id":21106,   "ctx":"ReplCoord-0","msg":"Resetting sync source to empty","attr":{"previousSyncSource":":27017"}}
{"t":{"$date":"2024-04-09T03:11:24.811+00:00"},"s":"I",  "c":"REPL",     "id":21359,   "ctx":"ReplCoord-0","msg":"Entering primary catch-up mode"}
{"t":{"$date":"2024-04-09T03:11:24.811+00:00"},"s":"I",  "c":"REPL",     "id":6015304, "ctx":"ReplCoord-0","msg":"Skipping primary catchup since we are the only node in the replica set."}
{"t":{"$date":"2024-04-09T03:11:24.812+00:00"},"s":"I",  "c":"REPL",     "id":21363,   "ctx":"ReplCoord-0","msg":"Exited primary catch-up mode"}
{"t":{"$date":"2024-04-09T03:11:24.813+00:00"},"s":"I",  "c":"REPL",     "id":21107,   "ctx":"ReplCoord-0","msg":"Stopping replication producer"}
{"t":{"$date":"2024-04-09T03:11:24.813+00:00"},"s":"I",  "c":"REPL",     "id":21239,   "ctx":"ReplBatcher","msg":"Oplog buffer has been drained","attr":{"term":3}}
{"t":{"$date":"2024-04-09T03:11:24.816+00:00"},"s":"I",  "c":"REPL",     "id":21343,   "ctx":"RstlKillOpThread","msg":"Starting to kill user operations"}
{"t":{"$date":"2024-04-09T03:11:24.824+00:00"},"s":"I",  "c":"REPL",     "id":21344,   "ctx":"RstlKillOpThread","msg":"Stopped killing user operations"}
{"t":{"$date":"2024-04-09T03:11:24.824+00:00"},"s":"I",  "c":"REPL",     "id":21340,   "ctx":"RstlKillOpThread","msg":"State transition ops metrics","attr":{"metrics":{"lastStateTransition":"stepUp","userOpsKilled":0,"userOpsRunning":0}}}
{"t":{"$date":"2024-04-09T03:11:24.825+00:00"},"s":"I",  "c":"REPL",     "id":4508103, "ctx":"OplogApplier-0","msg":"Increment the config term via reconfig"}
{"t":{"$date":"2024-04-09T03:11:24.826+00:00"},"s":"I",  "c":"REPL",     "id":6015313, "ctx":"OplogApplier-0","msg":"Replication config state is Steady, starting reconfig"}
{"t":{"$date":"2024-04-09T03:11:24.826+00:00"},"s":"I",  "c":"REPL",     "id":6015317, "ctx":"OplogApplier-0","msg":"Setting new configuration state","attr":{"newState":"ConfigReconfiguring","oldState":"ConfigSteady"}}
{"t":{"$date":"2024-04-09T03:11:24.827+00:00"},"s":"I",  "c":"REPL",     "id":21353,   "ctx":"OplogApplier-0","msg":"replSetReconfig config object parses ok","attr":{"numMembers":1}}
{"t":{"$date":"2024-04-09T03:11:24.828+00:00"},"s":"I",  "c":"REPL",     "id":51814,   "ctx":"OplogApplier-0","msg":"Persisting new config to disk"}
{"t":{"$date":"2024-04-09T03:11:24.833+00:00"},"s":"I",  "c":"REPL",     "id":6015315, "ctx":"OplogApplier-0","msg":"Persisted new config to disk"}
{"t":{"$date":"2024-04-09T03:11:24.834+00:00"},"s":"I",  "c":"REPL",     "id":6015317, "ctx":"OplogApplier-0","msg":"Setting new configuration state","attr":{"newState":"ConfigSteady","oldState":"ConfigReconfiguring"}}
{"t":{"$date":"2024-04-09T03:11:24.835+00:00"},"s":"I",  "c":"REPL",     "id":21392,   "ctx":"OplogApplier-0","msg":"New replica set config in use","attr":{"config":{"_id":"mr1","version":1,"term":3,"members":[{"_id":0,"host":"localhost:27017","arbiterOnly":false,"buildIndexes":true,"hidden":false,"priority":1,"tags":{},"secondaryDelaySecs":0,"votes":1}],"protocolVersion":1,"writeConcernMajorityJournalDefault":true,"settings":{"chainingAllowed":true,"heartbeatIntervalMillis":2000,"heartbeatTimeoutSecs":10,"electionTimeoutMillis":10000,"catchUpTimeoutMillis":-1,"catchUpTakeoverDelayMillis":30000,"getLastErrorModes":{},"getLastErrorDefaults":{"w":1,"wtimeout":0},"replicaSetId":{"$oid":"6614a714bc5de5bfe3a48011"}}}}}
{"t":{"$date":"2024-04-09T03:11:24.836+00:00"},"s":"I",  "c":"REPL",     "id":21393,   "ctx":"OplogApplier-0","msg":"Found self in config","attr":{"hostAndPort":"localhost:27017"}}
{"t":{"$date":"2024-04-09T03:11:24.837+00:00"},"s":"I",  "c":"REPL",     "id":6015310, "ctx":"OplogApplier-0","msg":"Starting to transition to primary."}
{"t":{"$date":"2024-04-09T03:11:24.842+00:00"},"s":"I",  "c":"REPL",     "id":6015309, "ctx":"OplogApplier-0","msg":"Logging transition to primary to oplog on stepup"}
{"t":{"$date":"2024-04-09T03:11:24.844+00:00"},"s":"I",  "c":"STORAGE",  "id":20657,   "ctx":"OplogApplier-0","msg":"IndexBuildsCoordinator::onStepUp - this node is stepping up to primary"}
{"t":{"$date":"2024-04-09T03:11:24.848+00:00"},"s":"I",  "c":"REPL",     "id":21331,   "ctx":"OplogApplier-0","msg":"Transition to primary complete; database writes are now permitted"}
{"t":{"$date":"2024-04-09T03:11:24.852+00:00"},"s":"I",  "c":"-",        "id":4939300, "ctx":"monitoring-keys-for-HMAC","msg":"Failed to refresh key cache","attr":{"error":"ReadConcernMajorityNotAvailableYet: Read concern majority reads are currently not possible.","nextWakeupMillis":600}}
{"t":{"$date":"2024-04-09T03:11:24.857+00:00"},"s":"I",  "c":"REPL",     "id":5123005, "ctx":"TenantMigrationRecipientService-0","msg":"Rebuilding PrimaryOnlyService due to stepUp","attr":{"service":"TenantMigrationRecipientService"}}
{"t":{"$date":"2024-04-09T03:11:24.860+00:00"},"s":"I",  "c":"REPL",     "id":5123005, "ctx":"TenantMigrationDonorService-0","msg":"Rebuilding PrimaryOnlyService due to stepUp","attr":{"service":"TenantMigrationDonorService"}}
{"t":{"$date":"2024-04-09T03:11:25.340+00:00"},"s":"I",  "c":"NETWORK",  "id":22943,   "ctx":"listener","msg":"Connection accepted","attr":{"remote":"127.0.0.1:37778","uuid":"bb4f5b51-aad6-4bd8-90e0-48570ab5ca96","connectionId":3,"connectionCount":1}}
{"t":{"$date":"2024-04-09T03:11:25.345+00:00"},"s":"I",  "c":"NETWORK",  "id":51800,   "ctx":"conn3","msg":"client metadata","attr":{"remote":"127.0.0.1:37778","client":"conn3","negotiatedCompressors":[],"doc":{"application":{"name":"mongosh 2.2.2"},"driver":{"name":"nodejs|mongosh","version":"6.5.0|2.2.2"},"platform":"Node.js v20.11.1, LE","os":{"name":"linux","architecture":"x64","version":"3.10.0-327.22.2.el7.x86_64","type":"Linux"},"env":{"container":{"runtime":"docker"}}}}}
{"t":{"$date":"2024-04-09T03:11:25.362+00:00"},"s":"I",  "c":"NETWORK",  "id":22943,   "ctx":"listener","msg":"Connection accepted","attr":{"remote":"127.0.0.1:37780","uuid":"1038195d-44c8-4abb-aec3-076faea526ed","connectionId":4,"connectionCount":2}}
{"t":{"$date":"2024-04-09T03:11:25.366+00:00"},"s":"I",  "c":"NETWORK",  "id":51800,   "ctx":"conn4","msg":"client metadata","attr":{"remote":"127.0.0.1:37780","client":"conn4","negotiatedCompressors":[],"doc":{"application":{"name":"mongosh 2.2.2"},"driver":{"name":"nodejs|mongosh","version":"6.5.0|2.2.2"},"platform":"Node.js v20.11.1, LE","os":{"name":"linux","architecture":"x64","version":"3.10.0-327.22.2.el7.x86_64","type":"Linux"},"env":{"container":{"runtime":"docker"}}}}}
{"t":{"$date":"2024-04-09T03:11:25.408+00:00"},"s":"I",  "c":"ACCESS",   "id":20250,   "ctx":"conn4","msg":"Authentication succeeded","attr":{"mechanism":"SCRAM-SHA-256","speculative":true,"principalName":"appsmith","authenticationDatabase":"appsmith","remote":"127.0.0.1:37780","extraInfo":{}}}
{"t":{"$date":"2024-04-09T03:11:25.434+00:00"},"s":"I",  "c":"NETWORK",  "id":22943,   "ctx":"listener","msg":"Connection accepted","attr":{"remote":"127.0.0.1:37784","uuid":"f0eeaf05-ad9a-4a11-aa39-e7e85f88fb42","connectionId":5,"connectionCount":3}}
{"t":{"$date":"2024-04-09T03:11:25.436+00:00"},"s":"I",  "c":"NETWORK",  "id":22943,   "ctx":"listener","msg":"Connection accepted","attr":{"remote":"127.0.0.1:37800","uuid":"5e6b1631-69c4-4c5c-8e49-823c851f18bb","connectionId":6,"connectionCount":4}}
{"t":{"$date":"2024-04-09T03:11:25.441+00:00"},"s":"I",  "c":"NETWORK",  "id":51800,   "ctx":"conn5","msg":"client metadata","attr":{"remote":"127.0.0.1:37784","client":"conn5","negotiatedCompressors":[],"doc":{"application":{"name":"mongosh 2.2.2"},"driver":{"name":"nodejs|mongosh","version":"6.5.0|2.2.2"},"platform":"Node.js v20.11.1, LE","os":{"name":"linux","architecture":"x64","version":"3.10.0-327.22.2.el7.x86_64","type":"Linux"},"env":{"container":{"runtime":"docker"}}}}}
{"t":{"$date":"2024-04-09T03:11:25.454+00:00"},"s":"I",  "c":"NETWORK",  "id":51800,   "ctx":"conn6","msg":"client metadata","attr":{"remote":"127.0.0.1:37800","client":"conn6","negotiatedCompressors":[],"doc":{"application":{"name":"mongosh 2.2.2"},"driver":{"name":"nodejs|mongosh","version":"6.5.0|2.2.2"},"platform":"Node.js v20.11.1, LE","os":{"name":"linux","architecture":"x64","version":"3.10.0-327.22.2.el7.x86_64","type":"Linux"},"env":{"container":{"runtime":"docker"}}}}}
{"t":{"$date":"2024-04-09T03:11:25.461+00:00"},"s":"I",  "c":"ACCESS",   "id":20250,   "ctx":"conn5","msg":"Authentication succeeded","attr":{"mechanism":"SCRAM-SHA-256","speculative":true,"principalName":"appsmith","authenticationDatabase":"appsmith","remote":"127.0.0.1:37784","extraInfo":{}}}
{"t":{"$date":"2024-04-09T03:11:25.464+00:00"},"s":"I",  "c":"ACCESS",   "id":20250,   "ctx":"conn6","msg":"Authentication succeeded","attr":{"mechanism":"SCRAM-SHA-256","speculative":true,"principalName":"appsmith","authenticationDatabase":"appsmith","remote":"127.0.0.1:37800","extraInfo":{}}}
{"t":{"$date":"2024-04-09T03:11:25.635+00:00"},"s":"I",  "c":"NETWORK",  "id":22944,   "ctx":"conn6","msg":"Connection ended","attr":{"remote":"127.0.0.1:37800","uuid":"5e6b1631-69c4-4c5c-8e49-823c851f18bb","connectionId":6,"connectionCount":2}}
{"t":{"$date":"2024-04-09T03:11:25.635+00:00"},"s":"I",  "c":"NETWORK",  "id":22944,   "ctx":"conn5","msg":"Connection ended","attr":{"remote":"127.0.0.1:37784","uuid":"f0eeaf05-ad9a-4a11-aa39-e7e85f88fb42","connectionId":5,"connectionCount":3}}
{"t":{"$date":"2024-04-09T03:11:25.636+00:00"},"s":"I",  "c":"NETWORK",  "id":22944,   "ctx":"conn4","msg":"Connection ended","attr":{"remote":"127.0.0.1:37780","uuid":"1038195d-44c8-4abb-aec3-076faea526ed","connectionId":4,"connectionCount":1}}
{"t":{"$date":"2024-04-09T03:11:25.636+00:00"},"s":"I",  "c":"NETWORK",  "id":22944,   "ctx":"conn3","msg":"Connection ended","attr":{"remote":"127.0.0.1:37778","uuid":"bb4f5b51-aad6-4bd8-90e0-48570ab5ca96","connectionId":3,"connectionCount":0}}
{"t":{"$date":"2024-04-09T03:11:31.823+00:00"},"s":"I",  "c":"NETWORK",  "id":22943,   "ctx":"listener","msg":"Connection accepted","attr":{"remote":"127.0.0.1:37808","uuid":"ebb074c8-ac5f-42f6-b096-34135949aebf","connectionId":7,"connectionCount":1}}
{"t":{"$date":"2024-04-09T03:11:31.828+00:00"},"s":"I",  "c":"NETWORK",  "id":22943,   "ctx":"listener","msg":"Connection accepted","attr":{"remote":"127.0.0.1:37810","uuid":"e78ee172-fd93-49b6-be75-************","connectionId":8,"connectionCount":2}}
{"t":{"$date":"2024-04-09T03:11:31.929+00:00"},"s":"I",  "c":"NETWORK",  "id":51800,   "ctx":"conn7","msg":"client metadata","attr":{"remote":"127.0.0.1:37808","client":"conn7","negotiatedCompressors":[],"doc":{"driver":{"name":"mongo-java-driver|reactive-streams|spring-boot","version":"4.8.2"},"os":{"type":"Linux","name":"Linux","architecture":"amd64","version":"5.15.49-linuxkit"},"platform":"Java/Eclipse Adoptium/17.0.9+9"}}}
{"t":{"$date":"2024-04-09T03:11:31.940+00:00"},"s":"I",  "c":"NETWORK",  "id":51800,   "ctx":"conn8","msg":"client metadata","attr":{"remote":"127.0.0.1:37810","client":"conn8","negotiatedCompressors":[],"doc":{"driver":{"name":"mongo-java-driver|reactive-streams|spring-boot","version":"4.8.2"},"os":{"type":"Linux","name":"Linux","architecture":"amd64","version":"5.15.49-linuxkit"},"platform":"Java/Eclipse Adoptium/17.0.9+9"}}}
{"t":{"$date":"2024-04-09T03:11:34.649+00:00"},"s":"I",  "c":"NETWORK",  "id":22943,   "ctx":"listener","msg":"Connection accepted","attr":{"remote":"127.0.0.1:53794","uuid":"7cb740bb-bbf8-4652-9d63-9eb32093dcfa","connectionId":9,"connectionCount":3}}
{"t":{"$date":"2024-04-09T03:11:34.658+00:00"},"s":"I",  "c":"NETWORK",  "id":22943,   "ctx":"listener","msg":"Connection accepted","attr":{"remote":"127.0.0.1:53800","uuid":"38a90adc-fbe7-4a6c-abb0-c03f6cb61b48","connectionId":10,"connectionCount":4}}
{"t":{"$date":"2024-04-09T03:11:34.668+00:00"},"s":"I",  "c":"NETWORK",  "id":51800,   "ctx":"conn10","msg":"client metadata","attr":{"remote":"127.0.0.1:53800","client":"conn10","negotiatedCompressors":[],"doc":{"driver":{"name":"mongo-java-driver|sync|spring-boot","version":"4.8.2"},"os":{"type":"Linux","name":"Linux","architecture":"amd64","version":"5.15.49-linuxkit"},"platform":"Java/Eclipse Adoptium/17.0.9+9"}}}
{"t":{"$date":"2024-04-09T03:11:34.668+00:00"},"s":"I",  "c":"NETWORK",  "id":51800,   "ctx":"conn9","msg":"client metadata","attr":{"remote":"127.0.0.1:53794","client":"conn9","negotiatedCompressors":[],"doc":{"driver":{"name":"mongo-java-driver|sync|spring-boot","version":"4.8.2"},"os":{"type":"Linux","name":"Linux","architecture":"amd64","version":"5.15.49-linuxkit"},"platform":"Java/Eclipse Adoptium/17.0.9+9"}}}
{"t":{"$date":"2024-04-09T03:11:40.480+00:00"},"s":"I",  "c":"NETWORK",  "id":22943,   "ctx":"listener","msg":"Connection accepted","attr":{"remote":"127.0.0.1:53812","uuid":"fae06e55-f29f-4af5-a03f-b3e1fcedc07c","connectionId":11,"connectionCount":5}}
{"t":{"$date":"2024-04-09T03:11:40.494+00:00"},"s":"I",  "c":"NETWORK",  "id":51800,   "ctx":"conn11","msg":"client metadata","attr":{"remote":"127.0.0.1:53812","client":"conn11","negotiatedCompressors":[],"doc":{"driver":{"name":"mongo-java-driver|sync|spring-boot","version":"4.8.2"},"os":{"type":"Linux","name":"Linux","architecture":"amd64","version":"5.15.49-linuxkit"},"platform":"Java/Eclipse Adoptium/17.0.9+9"}}}
{"t":{"$date":"2024-04-09T03:11:40.614+00:00"},"s":"I",  "c":"ACCESS",   "id":20250,   "ctx":"conn11","msg":"Authentication succeeded","attr":{"mechanism":"SCRAM-SHA-256","speculative":true,"principalName":"appsmith","authenticationDatabase":"appsmith","remote":"127.0.0.1:53812","extraInfo":{}}}
{"t":{"$date":"2024-04-09T03:11:40.857+00:00"},"s":"I",  "c":"NETWORK",  "id":22943,   "ctx":"listener","msg":"Connection accepted","attr":{"remote":"127.0.0.1:53814","uuid":"3e5ee28b-279b-4f40-b3cd-fc33a5db5412","connectionId":12,"connectionCount":6}}
{"t":{"$date":"2024-04-09T03:11:40.872+00:00"},"s":"I",  "c":"NETWORK",  "id":51800,   "ctx":"conn12","msg":"client metadata","attr":{"remote":"127.0.0.1:53814","client":"conn12","negotiatedCompressors":[],"doc":{"driver":{"name":"mongo-java-driver|sync|spring-boot","version":"4.8.2"},"os":{"type":"Linux","name":"Linux","architecture":"amd64","version":"5.15.49-linuxkit"},"platform":"Java/Eclipse Adoptium/17.0.9+9"}}}
{"t":{"$date":"2024-04-09T03:11:40.926+00:00"},"s":"I",  "c":"ACCESS",   "id":20250,   "ctx":"conn12","msg":"Authentication succeeded","attr":{"mechanism":"SCRAM-SHA-256","speculative":true,"principalName":"appsmith","authenticationDatabase":"appsmith","remote":"127.0.0.1:53814","extraInfo":{}}}
{"t":{"$date":"2024-04-09T03:11:43.355+00:00"},"s":"I",  "c":"NETWORK",  "id":22943,   "ctx":"listener","msg":"Connection accepted","attr":{"remote":"127.0.0.1:49276","uuid":"3e0de5e9-74ab-4bd5-b33f-06d6fc59ec50","connectionId":13,"connectionCount":7}}
{"t":{"$date":"2024-04-09T03:11:43.364+00:00"},"s":"I",  "c":"NETWORK",  "id":51800,   "ctx":"conn13","msg":"client metadata","attr":{"remote":"127.0.0.1:49276","client":"conn13","negotiatedCompressors":[],"doc":{"driver":{"name":"mongo-java-driver|reactive-streams|spring-boot","version":"4.8.2"},"os":{"type":"Linux","name":"Linux","architecture":"amd64","version":"5.15.49-linuxkit"},"platform":"Java/Eclipse Adoptium/17.0.9+9"}}}
{"t":{"$date":"2024-04-09T03:11:43.390+00:00"},"s":"I",  "c":"ACCESS",   "id":20250,   "ctx":"conn13","msg":"Authentication succeeded","attr":{"mechanism":"SCRAM-SHA-256","speculative":true,"principalName":"appsmith","authenticationDatabase":"appsmith","remote":"127.0.0.1:49276","extraInfo":{}}}
{"t":{"$date":"2024-04-09T03:12:13.251+00:00"},"s":"I",  "c":"NETWORK",  "id":22943,   "ctx":"listener","msg":"Connection accepted","attr":{"remote":"127.0.0.1:54858","uuid":"1a75d2c0-f9ab-40a6-bb08-b29d650d795f","connectionId":14,"connectionCount":8}}
{"t":{"$date":"2024-04-09T03:12:13.256+00:00"},"s":"I",  "c":"NETWORK",  "id":51800,   "ctx":"conn14","msg":"client metadata","attr":{"remote":"127.0.0.1:54858","client":"conn14","negotiatedCompressors":[],"doc":{"driver":{"name":"mongo-java-driver|reactive-streams|spring-boot","version":"4.8.2"},"os":{"type":"Linux","name":"Linux","architecture":"amd64","version":"5.15.49-linuxkit"},"platform":"Java/Eclipse Adoptium/17.0.9+9"}}}
{"t":{"$date":"2024-04-09T03:12:13.277+00:00"},"s":"I",  "c":"ACCESS",   "id":20250,   "ctx":"conn14","msg":"Authentication succeeded","attr":{"mechanism":"SCRAM-SHA-256","speculative":true,"principalName":"appsmith","authenticationDatabase":"appsmith","remote":"127.0.0.1:54858","extraInfo":{}}}
{"t":{"$date":"2024-04-09T03:12:16.061+00:00"},"s":"I",  "c":"NETWORK",  "id":22943,   "ctx":"listener","msg":"Connection accepted","attr":{"remote":"127.0.0.1:54860","uuid":"7f966025-d824-45d7-b3aa-6c45557c6ec8","connectionId":15,"connectionCount":9}}
{"t":{"$date":"2024-04-09T03:12:16.065+00:00"},"s":"I",  "c":"NETWORK",  "id":51800,   "ctx":"conn15","msg":"client metadata","attr":{"remote":"127.0.0.1:54860","client":"conn15","negotiatedCompressors":[],"doc":{"driver":{"name":"mongo-java-driver|reactive-streams|spring-boot","version":"4.8.2"},"os":{"type":"Linux","name":"Linux","architecture":"amd64","version":"5.15.49-linuxkit"},"platform":"Java/Eclipse Adoptium/17.0.9+9"}}}
{"t":{"$date":"2024-04-09T03:12:16.096+00:00"},"s":"I",  "c":"ACCESS",   "id":20250,   "ctx":"conn15","msg":"Authentication succeeded","attr":{"mechanism":"SCRAM-SHA-256","speculative":true,"principalName":"appsmith","authenticationDatabase":"appsmith","remote":"127.0.0.1:54860","extraInfo":{}}}
{"t":{"$date":"2024-04-09T03:12:24.352+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"Checkpointer","msg":"WiredTiger message","attr":{"message":"[1712632344:352310][1503:0x7f423029d700], WT_SESSION.checkpoint: [WT_VERB_CHECKPOINT_PROGRESS] saving checkpoint snapshot min: 30, snapshot max: 30 snapshot count: 0, oldest timestamp: (1712632036, 3) , meta checkpoint timestamp: (1712632336, 3) base write gen: 435"}}
{"t":{"$date":"2024-04-09T03:13:24.394+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"Checkpointer","msg":"WiredTiger message","attr":{"message":"[1712632404:394529][1503:0x7f423029d700], WT_SESSION.checkpoint: [WT_VERB_CHECKPOINT_PROGRESS] saving checkpoint snapshot min: 43, snapshot max: 43 snapshot count: 0, oldest timestamp: (1712632094, 1) , meta checkpoint timestamp: (1712632394, 1) base write gen: 435"}}
{"t":{"$date":"2024-04-09T03:13:43.250+00:00"},"s":"I",  "c":"NETWORK",  "id":22943,   "ctx":"listener","msg":"Connection accepted","attr":{"remote":"127.0.0.1:49458","uuid":"16f94180-e474-4f6a-9143-ae6333bd3661","connectionId":16,"connectionCount":10}}
{"t":{"$date":"2024-04-09T03:13:43.255+00:00"},"s":"I",  "c":"NETWORK",  "id":51800,   "ctx":"conn16","msg":"client metadata","attr":{"remote":"127.0.0.1:49458","client":"conn16","negotiatedCompressors":[],"doc":{"driver":{"name":"mongo-java-driver|reactive-streams|spring-boot","version":"4.8.2"},"os":{"type":"Linux","name":"Linux","architecture":"amd64","version":"5.15.49-linuxkit"},"platform":"Java/Eclipse Adoptium/17.0.9+9"}}}
{"t":{"$date":"2024-04-09T03:13:43.260+00:00"},"s":"I",  "c":"NETWORK",  "id":22943,   "ctx":"listener","msg":"Connection accepted","attr":{"remote":"127.0.0.1:49466","uuid":"60027c8e-e09b-4d07-84a9-725ae4725245","connectionId":17,"connectionCount":11}}
{"t":{"$date":"2024-04-09T03:13:43.261+00:00"},"s":"I",  "c":"NETWORK",  "id":51800,   "ctx":"conn17","msg":"client metadata","attr":{"remote":"127.0.0.1:49466","client":"conn17","negotiatedCompressors":[],"doc":{"driver":{"name":"mongo-java-driver|reactive-streams|spring-boot","version":"4.8.2"},"os":{"type":"Linux","name":"Linux","architecture":"amd64","version":"5.15.49-linuxkit"},"platform":"Java/Eclipse Adoptium/17.0.9+9"}}}
{"t":{"$date":"2024-04-09T03:13:43.309+00:00"},"s":"I",  "c":"ACCESS",   "id":20250,   "ctx":"conn16","msg":"Authentication succeeded","attr":{"mechanism":"SCRAM-SHA-256","speculative":true,"principalName":"appsmith","authenticationDatabase":"appsmith","remote":"127.0.0.1:49458","extraInfo":{}}}
{"t":{"$date":"2024-04-09T03:13:43.310+00:00"},"s":"I",  "c":"ACCESS",   "id":20250,   "ctx":"conn17","msg":"Authentication succeeded","attr":{"mechanism":"SCRAM-SHA-256","speculative":true,"principalName":"appsmith","authenticationDatabase":"appsmith","remote":"127.0.0.1:49466","extraInfo":{}}}
{"t":{"$date":"2024-04-09T03:14:24.439+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"Checkpointer","msg":"WiredTiger message","attr":{"message":"[1712632464:439971][1503:0x7f423029d700], WT_SESSION.checkpoint: [WT_VERB_CHECKPOINT_PROGRESS] saving checkpoint snapshot min: 58, snapshot max: 58 snapshot count: 0, oldest timestamp: (1712632154, 1) , meta checkpoint timestamp: (1712632454, 1) base write gen: 435"}}
{"t":{"$date":"2024-04-09T03:15:24.480+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"Checkpointer","msg":"WiredTiger message","attr":{"message":"[1712632524:480210][1503:0x7f423029d700], WT_SESSION.checkpoint: [WT_VERB_CHECKPOINT_PROGRESS] saving checkpoint snapshot min: 73, snapshot max: 73 snapshot count: 0, oldest timestamp: (1712632214, 1) , meta checkpoint timestamp: (1712632514, 1) base write gen: 435"}}
{"t":{"$date":"2024-04-09T03:16:24.595+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"Checkpointer","msg":"WiredTiger message","attr":{"message":"[1712632584:595240][1503:0x7f423029d700], WT_SESSION.checkpoint: [WT_VERB_CHECKPOINT_PROGRESS] saving checkpoint snapshot min: 108, snapshot max: 108 snapshot count: 0, oldest timestamp: (1712632283, 2) , meta checkpoint timestamp: (1712632583, 2) base write gen: 435"}}
{"t":{"$date":"2024-04-09T03:17:24.760+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"Checkpointer","msg":"WiredTiger message","attr":{"message":"[1712632644:760121][1503:0x7f423029d700], WT_SESSION.checkpoint: [WT_VERB_CHECKPOINT_PROGRESS] saving checkpoint snapshot min: 175, snapshot max: 175 snapshot count: 0, oldest timestamp: (1712632341, 2) , meta checkpoint timestamp: (1712632641, 2) base write gen: 435"}}
{"t":{"$date":"2024-04-09T03:18:24.846+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"Checkpointer","msg":"WiredTiger message","attr":{"message":"[1712632704:846754][1503:0x7f423029d700], WT_SESSION.checkpoint: [WT_VERB_CHECKPOINT_PROGRESS] saving checkpoint snapshot min: 203, snapshot max: 203 snapshot count: 0, oldest timestamp: (1712632403, 1) , meta checkpoint timestamp: (1712632703, 1) base write gen: 435"}}
{"t":{"$date":"2024-04-09T03:19:24.924+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"Checkpointer","msg":"WiredTiger message","attr":{"message":"[1712632764:924550][1503:0x7f423029d700], WT_SESSION.checkpoint: [WT_VERB_CHECKPOINT_PROGRESS] saving checkpoint snapshot min: 220, snapshot max: 220 snapshot count: 0, oldest timestamp: (1712632454, 1) , meta checkpoint timestamp: (1712632754, 1) base write gen: 435"}}
{"t":{"$date":"2024-04-09T03:20:24.980+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"Checkpointer","msg":"WiredTiger message","attr":{"message":"[1712632824:980258][1503:0x7f423029d700], WT_SESSION.checkpoint: [WT_VERB_CHECKPOINT_PROGRESS] saving checkpoint snapshot min: 244, snapshot max: 244 snapshot count: 0, oldest timestamp: (1712632511, 2) , meta checkpoint timestamp: (1712632811, 2) base write gen: 435"}}
{"t":{"$date":"2024-04-09T03:21:25.042+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"Checkpointer","msg":"WiredTiger message","attr":{"message":"[1712632885:42941][1503:0x7f423029d700], WT_SESSION.checkpoint: [WT_VERB_CHECKPOINT_PROGRESS] saving checkpoint snapshot min: 270, snapshot max: 270 snapshot count: 0, oldest timestamp: (1712632584, 10) , meta checkpoint timestamp: (1712632884, 10) base write gen: 435"}}
{"t":{"$date":"2024-04-09T03:22:25.074+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"Checkpointer","msg":"WiredTiger message","attr":{"message":"[1712632945:74316][1503:0x7f423029d700], WT_SESSION.checkpoint: [WT_VERB_CHECKPOINT_PROGRESS] saving checkpoint snapshot min: 285, snapshot max: 285 snapshot count: 0, oldest timestamp: (1712632644, 1) , meta checkpoint timestamp: (1712632944, 1) base write gen: 435"}}
{"t":{"$date":"2024-04-09T03:23:25.131+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"Checkpointer","msg":"WiredTiger message","attr":{"message":"[1712633005:131234][1503:0x7f423029d700], WT_SESSION.checkpoint: [WT_VERB_CHECKPOINT_PROGRESS] saving checkpoint snapshot min: 300, snapshot max: 300 snapshot count: 0, oldest timestamp: (1712632704, 1) , meta checkpoint timestamp: (1712633004, 1) base write gen: 435"}}
{"t":{"$date":"2024-04-09T03:24:25.195+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"Checkpointer","msg":"WiredTiger message","attr":{"message":"[1712633065:195686][1503:0x7f423029d700], WT_SESSION.checkpoint: [WT_VERB_CHECKPOINT_PROGRESS] saving checkpoint snapshot min: 315, snapshot max: 315 snapshot count: 0, oldest timestamp: (1712632764, 1) , meta checkpoint timestamp: (1712633064, 1) base write gen: 435"}}
{"t":{"$date":"2024-04-09T03:25:25.232+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"Checkpointer","msg":"WiredTiger message","attr":{"message":"[1712633125:232574][1503:0x7f423029d700], WT_SESSION.checkpoint: [WT_VERB_CHECKPOINT_PROGRESS] saving checkpoint snapshot min: 330, snapshot max: 330 snapshot count: 0, oldest timestamp: (1712632824, 1) , meta checkpoint timestamp: (1712633124, 1) base write gen: 435"}}
{"t":{"$date":"2024-04-09T03:26:25.264+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"Checkpointer","msg":"WiredTiger message","attr":{"message":"[1712633185:264956][1503:0x7f423029d700], WT_SESSION.checkpoint: [WT_VERB_CHECKPOINT_PROGRESS] saving checkpoint snapshot min: 345, snapshot max: 345 snapshot count: 0, oldest timestamp: (1712632884, 1) , meta checkpoint timestamp: (1712633184, 1) base write gen: 435"}}
{"t":{"$date":"2024-04-09T03:27:25.297+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"Checkpointer","msg":"WiredTiger message","attr":{"message":"[1712633245:297349][1503:0x7f423029d700], WT_SESSION.checkpoint: [WT_VERB_CHECKPOINT_PROGRESS] saving checkpoint snapshot min: 360, snapshot max: 360 snapshot count: 0, oldest timestamp: (1712632944, 1) , meta checkpoint timestamp: (1712633244, 1) base write gen: 435"}}
{"t":{"$date":"2024-04-09T03:28:25.336+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"Checkpointer","msg":"WiredTiger message","attr":{"message":"[1712633305:336435][1503:0x7f423029d700], WT_SESSION.checkpoint: [WT_VERB_CHECKPOINT_PROGRESS] saving checkpoint snapshot min: 375, snapshot max: 375 snapshot count: 0, oldest timestamp: (1712633004, 1) , meta checkpoint timestamp: (1712633304, 1) base write gen: 435"}}
{"t":{"$date":"2024-04-09T03:29:25.378+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"Checkpointer","msg":"WiredTiger message","attr":{"message":"[1712633365:378453][1503:0x7f423029d700], WT_SESSION.checkpoint: [WT_VERB_CHECKPOINT_PROGRESS] saving checkpoint snapshot min: 390, snapshot max: 390 snapshot count: 0, oldest timestamp: (1712633065, 1) , meta checkpoint timestamp: (1712633365, 1) base write gen: 435"}}
{"t":{"$date":"2024-04-09T03:30:25.429+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"Checkpointer","msg":"WiredTiger message","attr":{"message":"[1712633425:429701][1503:0x7f423029d700], WT_SESSION.checkpoint: [WT_VERB_CHECKPOINT_PROGRESS] saving checkpoint snapshot min: 405, snapshot max: 405 snapshot count: 0, oldest timestamp: (1712633125, 1) , meta checkpoint timestamp: (1712633425, 1) base write gen: 435"}}
{"t":{"$date":"2024-04-09T03:31:25.477+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"Checkpointer","msg":"WiredTiger message","attr":{"message":"[1712633485:477637][1503:0x7f423029d700], WT_SESSION.checkpoint: [WT_VERB_CHECKPOINT_PROGRESS] saving checkpoint snapshot min: 420, snapshot max: 420 snapshot count: 0, oldest timestamp: (1712633184, 1) , meta checkpoint timestamp: (1712633484, 1) base write gen: 435"}}
{"t":{"$date":"2024-04-09T03:32:25.525+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"Checkpointer","msg":"WiredTiger message","attr":{"message":"[1712633545:525840][1503:0x7f423029d700], WT_SESSION.checkpoint: [WT_VERB_CHECKPOINT_PROGRESS] saving checkpoint snapshot min: 435, snapshot max: 435 snapshot count: 0, oldest timestamp: (1712633245, 1) , meta checkpoint timestamp: (1712633545, 1) base write gen: 435"}}
{"t":{"$date":"2024-04-09T03:33:25.574+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"Checkpointer","msg":"WiredTiger message","attr":{"message":"[1712633605:574290][1503:0x7f423029d700], WT_SESSION.checkpoint: [WT_VERB_CHECKPOINT_PROGRESS] saving checkpoint snapshot min: 450, snapshot max: 450 snapshot count: 0, oldest timestamp: (1712633305, 1) , meta checkpoint timestamp: (1712633605, 1) base write gen: 435"}}
{"t":{"$date":"2024-04-09T03:34:25.634+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"Checkpointer","msg":"WiredTiger message","attr":{"message":"[1712633665:634027][1503:0x7f423029d700], WT_SESSION.checkpoint: [WT_VERB_CHECKPOINT_PROGRESS] saving checkpoint snapshot min: 465, snapshot max: 465 snapshot count: 0, oldest timestamp: (1712633365, 1) , meta checkpoint timestamp: (1712633665, 1) base write gen: 435"}}
{"t":{"$date":"2024-04-09T03:35:25.674+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"Checkpointer","msg":"WiredTiger message","attr":{"message":"[1712633725:674220][1503:0x7f423029d700], WT_SESSION.checkpoint: [WT_VERB_CHECKPOINT_PROGRESS] saving checkpoint snapshot min: 480, snapshot max: 480 snapshot count: 0, oldest timestamp: (1712633425, 1) , meta checkpoint timestamp: (1712633725, 1) base write gen: 435"}}
{"t":{"$date":"2024-04-09T03:36:25.732+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"Checkpointer","msg":"WiredTiger message","attr":{"message":"[1712633785:732921][1503:0x7f423029d700], WT_SESSION.checkpoint: [WT_VERB_CHECKPOINT_PROGRESS] saving checkpoint snapshot min: 495, snapshot max: 495 snapshot count: 0, oldest timestamp: (1712633484, 1) , meta checkpoint timestamp: (1712633784, 1) base write gen: 435"}}
{"t":{"$date":"2024-04-09T03:37:25.774+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"Checkpointer","msg":"WiredTiger message","attr":{"message":"[1712633845:774174][1503:0x7f423029d700], WT_SESSION.checkpoint: [WT_VERB_CHECKPOINT_PROGRESS] saving checkpoint snapshot min: 510, snapshot max: 510 snapshot count: 0, oldest timestamp: (1712633545, 1) , meta checkpoint timestamp: (1712633845, 1) base write gen: 435"}}
{"t":{"$date":"2024-04-09T03:38:25.822+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"Checkpointer","msg":"WiredTiger message","attr":{"message":"[1712633905:822261][1503:0x7f423029d700], WT_SESSION.checkpoint: [WT_VERB_CHECKPOINT_PROGRESS] saving checkpoint snapshot min: 525, snapshot max: 525 snapshot count: 0, oldest timestamp: (1712633605, 1) , meta checkpoint timestamp: (1712633905, 1) base write gen: 435"}}
{"t":{"$date":"2024-04-09T03:39:25.859+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"Checkpointer","msg":"WiredTiger message","attr":{"message":"[1712633965:859926][1503:0x7f423029d700], WT_SESSION.checkpoint: [WT_VERB_CHECKPOINT_PROGRESS] saving checkpoint snapshot min: 540, snapshot max: 540 snapshot count: 0, oldest timestamp: (1712633665, 1) , meta checkpoint timestamp: (1712633965, 1) base write gen: 435"}}
{"t":{"$date":"2024-04-09T03:40:25.927+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"Checkpointer","msg":"WiredTiger message","attr":{"message":"[1712634025:927454][1503:0x7f423029d700], WT_SESSION.checkpoint: [WT_VERB_CHECKPOINT_PROGRESS] saving checkpoint snapshot min: 555, snapshot max: 555 snapshot count: 0, oldest timestamp: (1712633725, 1) , meta checkpoint timestamp: (1712634025, 1) base write gen: 435"}}
{"t":{"$date":"2024-04-09T03:41:25.972+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"Checkpointer","msg":"WiredTiger message","attr":{"message":"[1712634085:972430][1503:0x7f423029d700], WT_SESSION.checkpoint: [WT_VERB_CHECKPOINT_PROGRESS] saving checkpoint snapshot min: 570, snapshot max: 570 snapshot count: 0, oldest timestamp: (1712633785, 1) , meta checkpoint timestamp: (1712634085, 1) base write gen: 435"}}
{"t":{"$date":"2024-04-09T03:42:26.008+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"Checkpointer","msg":"WiredTiger message","attr":{"message":"[1712634146:8725][1503:0x7f423029d700], WT_SESSION.checkpoint: [WT_VERB_CHECKPOINT_PROGRESS] saving checkpoint snapshot min: 585, snapshot max: 585 snapshot count: 0, oldest timestamp: (1712633845, 1) , meta checkpoint timestamp: (1712634145, 1) base write gen: 435"}}
{"t":{"$date":"2024-04-09T03:43:26.060+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"Checkpointer","msg":"WiredTiger message","attr":{"message":"[1712634206:60814][1503:0x7f423029d700], WT_SESSION.checkpoint: [WT_VERB_CHECKPOINT_PROGRESS] saving checkpoint snapshot min: 600, snapshot max: 600 snapshot count: 0, oldest timestamp: (1712633905, 1) , meta checkpoint timestamp: (1712634205, 1) base write gen: 435"}}
{"t":{"$date":"2024-04-09T03:44:26.100+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"Checkpointer","msg":"WiredTiger message","attr":{"message":"[1712634266:100849][1503:0x7f423029d700], WT_SESSION.checkpoint: [WT_VERB_CHECKPOINT_PROGRESS] saving checkpoint snapshot min: 615, snapshot max: 615 snapshot count: 0, oldest timestamp: (1712633965, 1) , meta checkpoint timestamp: (1712634265, 1) base write gen: 435"}}
{"t":{"$date":"2024-04-09T03:45:26.134+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"Checkpointer","msg":"WiredTiger message","attr":{"message":"[1712634326:134934][1503:0x7f423029d700], WT_SESSION.checkpoint: [WT_VERB_CHECKPOINT_PROGRESS] saving checkpoint snapshot min: 630, snapshot max: 630 snapshot count: 0, oldest timestamp: (1712634025, 1) , meta checkpoint timestamp: (1712634325, 1) base write gen: 435"}}
{"t":{"$date":"2024-04-09T03:46:26.215+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"Checkpointer","msg":"WiredTiger message","attr":{"message":"[1712634386:215890][1503:0x7f423029d700], WT_SESSION.checkpoint: [WT_VERB_CHECKPOINT_PROGRESS] saving checkpoint snapshot min: 648, snapshot max: 648 snapshot count: 0, oldest timestamp: (1712634084, 3) , meta checkpoint timestamp: (1712634384, 3) base write gen: 435"}}
{"t":{"$date":"2024-04-09T03:47:26.266+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"Checkpointer","msg":"WiredTiger message","attr":{"message":"[1712634446:266324][1503:0x7f423029d700], WT_SESSION.checkpoint: [WT_VERB_CHECKPOINT_PROGRESS] saving checkpoint snapshot min: 663, snapshot max: 663 snapshot count: 0, oldest timestamp: (1712634145, 1) , meta checkpoint timestamp: (1712634445, 1) base write gen: 435"}}
{"t":{"$date":"2024-04-09T03:48:26.310+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"Checkpointer","msg":"WiredTiger message","attr":{"message":"[1712634506:310380][1503:0x7f423029d700], WT_SESSION.checkpoint: [WT_VERB_CHECKPOINT_PROGRESS] saving checkpoint snapshot min: 678, snapshot max: 678 snapshot count: 0, oldest timestamp: (1712634205, 1) , meta checkpoint timestamp: (1712634505, 1) base write gen: 435"}}
{"t":{"$date":"2024-04-09T03:49:26.359+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"Checkpointer","msg":"WiredTiger message","attr":{"message":"[1712634566:359070][1503:0x7f423029d700], WT_SESSION.checkpoint: [WT_VERB_CHECKPOINT_PROGRESS] saving checkpoint snapshot min: 693, snapshot max: 693 snapshot count: 0, oldest timestamp: (1712634265, 1) , meta checkpoint timestamp: (1712634565, 1) base write gen: 435"}}
{"t":{"$date":"2024-04-09T03:50:26.422+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"Checkpointer","msg":"WiredTiger message","attr":{"message":"[1712634626:422925][1503:0x7f423029d700], WT_SESSION.checkpoint: [WT_VERB_CHECKPOINT_PROGRESS] saving checkpoint snapshot min: 708, snapshot max: 708 snapshot count: 0, oldest timestamp: (1712634325, 1) , meta checkpoint timestamp: (1712634625, 1) base write gen: 435"}}
{"t":{"$date":"2024-04-09T03:51:26.484+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"Checkpointer","msg":"WiredTiger message","attr":{"message":"[1712634686:484693][1503:0x7f423029d700], WT_SESSION.checkpoint: [WT_VERB_CHECKPOINT_PROGRESS] saving checkpoint snapshot min: 727, snapshot max: 727 snapshot count: 0, oldest timestamp: (1712634384, 5) , meta checkpoint timestamp: (1712634684, 5) base write gen: 435"}}
{"t":{"$date":"2024-04-09T03:52:26.530+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"Checkpointer","msg":"WiredTiger message","attr":{"message":"[1712634746:530359][1503:0x7f423029d700], WT_SESSION.checkpoint: [WT_VERB_CHECKPOINT_PROGRESS] saving checkpoint snapshot min: 742, snapshot max: 742 snapshot count: 0, oldest timestamp: (1712634445, 1) , meta checkpoint timestamp: (1712634745, 1) base write gen: 435"}}
{"t":{"$date":"2024-04-09T03:53:57.060+00:00"},"s":"I",  "c":"-",        "id":20883,   "ctx":"conn9","msg":"Interrupted operation as its client disconnected","attr":{"opId":37829}}
{"t":{"$date":"2024-04-09T03:53:57.061+00:00"},"s":"I",  "c":"-",        "id":20883,   "ctx":"conn8","msg":"Interrupted operation as its client disconnected","attr":{"opId":37828}}
{"t":{"$date":"2024-04-09T03:53:57.084+00:00"},"s":"I",  "c":"NETWORK",  "id":22944,   "ctx":"conn13","msg":"Connection ended","attr":{"remote":"127.0.0.1:49276","uuid":"3e0de5e9-74ab-4bd5-b33f-06d6fc59ec50","connectionId":13,"connectionCount":10}}
{"t":{"$date":"2024-04-09T03:53:57.084+00:00"},"s":"I",  "c":"NETWORK",  "id":22944,   "ctx":"conn16","msg":"Connection ended","attr":{"remote":"127.0.0.1:49458","uuid":"16f94180-e474-4f6a-9143-ae6333bd3661","connectionId":16,"connectionCount":9}}
{"t":{"$date":"2024-04-09T03:53:57.087+00:00"},"s":"I",  "c":"NETWORK",  "id":22944,   "ctx":"conn15","msg":"Connection ended","attr":{"remote":"127.0.0.1:54860","uuid":"7f966025-d824-45d7-b3aa-6c45557c6ec8","connectionId":15,"connectionCount":8}}
{"t":{"$date":"2024-04-09T03:53:57.091+00:00"},"s":"I",  "c":"NETWORK",  "id":22944,   "ctx":"conn14","msg":"Connection ended","attr":{"remote":"127.0.0.1:54858","uuid":"1a75d2c0-f9ab-40a6-bb08-b29d650d795f","connectionId":14,"connectionCount":7}}
{"t":{"$date":"2024-04-09T03:53:57.095+00:00"},"s":"I",  "c":"NETWORK",  "id":22944,   "ctx":"conn12","msg":"Connection ended","attr":{"remote":"127.0.0.1:53814","uuid":"3e5ee28b-279b-4f40-b3cd-fc33a5db5412","connectionId":12,"connectionCount":6}}
{"t":{"$date":"2024-04-09T03:53:57.109+00:00"},"s":"I",  "c":"NETWORK",  "id":22943,   "ctx":"listener","msg":"Connection accepted","attr":{"remote":"127.0.0.1:58816","uuid":"3a456f02-a193-4e06-8229-4011578e921a","connectionId":18,"connectionCount":7}}
{"t":{"$date":"2024-04-09T03:53:57.109+00:00"},"s":"I",  "c":"NETWORK",  "id":22944,   "ctx":"conn17","msg":"Connection ended","attr":{"remote":"127.0.0.1:49466","uuid":"60027c8e-e09b-4d07-84a9-725ae4725245","connectionId":17,"connectionCount":6}}
{"t":{"$date":"2024-04-09T03:53:57.113+00:00"},"s":"I",  "c":"NETWORK",  "id":22944,   "ctx":"conn11","msg":"Connection ended","attr":{"remote":"127.0.0.1:53812","uuid":"fae06e55-f29f-4af5-a03f-b3e1fcedc07c","connectionId":11,"connectionCount":5}}
{"t":{"$date":"2024-04-09T03:53:57.168+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"Checkpointer","msg":"WiredTiger message","attr":{"message":"[1712634837:168453][1503:0x7f423029d700], WT_SESSION.checkpoint: [WT_VERB_CHECKPOINT_PROGRESS] saving checkpoint snapshot min: 755, snapshot max: 755 snapshot count: 0, oldest timestamp: (1712634537, 1) , meta checkpoint timestamp: (1712634785, 1) base write gen: 435"}}
{"t":{"$date":"2024-04-09T03:53:57.170+00:00"},"s":"I",  "c":"NETWORK",  "id":22989,   "ctx":"conn8","msg":"Error sending response to client. Ending connection from remote","attr":{"error":{"code":9001,"codeName":"SocketException","errmsg":"Broken pipe"},"remote":"127.0.0.1:37810","connectionId":8}}
{"t":{"$date":"2024-04-09T03:53:57.174+00:00"},"s":"I",  "c":"NETWORK",  "id":22989,   "ctx":"conn9","msg":"Error sending response to client. Ending connection from remote","attr":{"error":{"code":9001,"codeName":"SocketException","errmsg":"Broken pipe"},"remote":"127.0.0.1:53794","connectionId":9}}
{"t":{"$date":"2024-04-09T03:53:57.223+00:00"},"s":"I",  "c":"NETWORK",  "id":22943,   "ctx":"listener","msg":"Connection accepted","attr":{"remote":"127.0.0.1:58818","uuid":"b0a28b8d-9288-44ae-9f87-3e7d5eb0d731","connectionId":19,"connectionCount":6}}
{"t":{"$date":"2024-04-09T03:53:57.231+00:00"},"s":"I",  "c":"NETWORK",  "id":22944,   "ctx":"conn8","msg":"Connection ended","attr":{"remote":"127.0.0.1:37810","uuid":"e78ee172-fd93-49b6-be75-************","connectionId":8,"connectionCount":5}}
{"t":{"$date":"2024-04-09T03:53:57.232+00:00"},"s":"I",  "c":"NETWORK",  "id":22944,   "ctx":"conn9","msg":"Connection ended","attr":{"remote":"127.0.0.1:53794","uuid":"7cb740bb-bbf8-4652-9d63-9eb32093dcfa","connectionId":9,"connectionCount":4}}
{"t":{"$date":"2024-04-09T03:53:57.233+00:00"},"s":"I",  "c":"NETWORK",  "id":51800,   "ctx":"conn18","msg":"client metadata","attr":{"remote":"127.0.0.1:58816","client":"conn18","negotiatedCompressors":[],"doc":{"driver":{"name":"mongo-java-driver|reactive-streams|spring-boot","version":"4.8.2"},"os":{"type":"Linux","name":"Linux","architecture":"amd64","version":"5.15.49-linuxkit"},"platform":"Java/Eclipse Adoptium/17.0.9+9"}}}
{"t":{"$date":"2024-04-09T03:53:57.255+00:00"},"s":"I",  "c":"NETWORK",  "id":51800,   "ctx":"conn19","msg":"client metadata","attr":{"remote":"127.0.0.1:58818","client":"conn19","negotiatedCompressors":[],"doc":{"driver":{"name":"mongo-java-driver|sync|spring-boot","version":"4.8.2"},"os":{"type":"Linux","name":"Linux","architecture":"amd64","version":"5.15.49-linuxkit"},"platform":"Java/Eclipse Adoptium/17.0.9+9"}}}
{"t":{"$date":"2024-04-09T03:55:00.062+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"Checkpointer","msg":"WiredTiger message","attr":{"message":"[1712634900:61999][1503:0x7f423029d700], WT_SESSION.checkpoint: [WT_VERB_CHECKPOINT_PROGRESS] saving checkpoint snapshot min: 770, snapshot max: 770 snapshot count: 0, oldest timestamp: (1712634597, 1) , meta checkpoint timestamp: (1712634897, 1) base write gen: 435"}}
{"t":{"$date":"2024-04-09T03:57:15.035+00:00"},"s":"I",  "c":"COMMAND",  "id":51803,   "ctx":"conn18","msg":"Slow query","attr":{"type":"command","ns":"admin.$cmd","command":{"hello":1,"helloOk":true,"topologyVersion":{"processId":{"$oid":"6614b1d8b960826999c97427"},"counter":6},"maxAwaitTimeMS":10000,"$db":"admin","$clusterTime":{"clusterTime":{"$timestamp":{"t":1712634837,"i":1}},"signature":{"hash":{"$binary":{"base64":"bPjJfZVOsTbsSG0XqC3oC92Wpxw=","subType":"0"}},"keyId":7355687795744047109}},"$readPreference":{"mode":"primaryPreferred"}},"numYields":0,"reslen":751,"locks":{},"remote":"127.0.0.1:58816","protocol":"op_msg","durationMillis":230}}
{"t":{"$date":"2024-04-09T03:57:15.047+00:00"},"s":"I",  "c":"-",        "id":20883,   "ctx":"conn19","msg":"Interrupted operation as its client disconnected","attr":{"opId":38784}}
{"t":{"$date":"2024-04-09T03:57:15.210+00:00"},"s":"I",  "c":"NETWORK",  "id":22989,   "ctx":"conn19","msg":"Error sending response to client. Ending connection from remote","attr":{"error":{"code":9001,"codeName":"SocketException","errmsg":"Broken pipe"},"remote":"127.0.0.1:58818","connectionId":19}}
{"t":{"$date":"2024-04-09T03:57:15.214+00:00"},"s":"I",  "c":"NETWORK",  "id":22944,   "ctx":"conn19","msg":"Connection ended","attr":{"remote":"127.0.0.1:58818","uuid":"b0a28b8d-9288-44ae-9f87-3e7d5eb0d731","connectionId":19,"connectionCount":4}}
{"t":{"$date":"2024-04-09T03:57:15.188+00:00"},"s":"I",  "c":"NETWORK",  "id":22943,   "ctx":"listener","msg":"Connection accepted","attr":{"remote":"127.0.0.1:43552","uuid":"9a6232b6-0260-436b-ab4a-9d483913e9bf","connectionId":20,"connectionCount":5}}
{"t":{"$date":"2024-04-09T03:57:15.206+00:00"},"s":"I",  "c":"-",        "id":20883,   "ctx":"conn18","msg":"Interrupted operation as its client disconnected","attr":{"opId":38788}}
{"t":{"$date":"2024-04-09T03:57:15.229+00:00"},"s":"I",  "c":"NETWORK",  "id":22989,   "ctx":"conn18","msg":"Error sending response to client. Ending connection from remote","attr":{"error":{"code":9001,"codeName":"SocketException","errmsg":"Broken pipe"},"remote":"127.0.0.1:58816","connectionId":18}}
{"t":{"$date":"2024-04-09T03:57:15.232+00:00"},"s":"I",  "c":"NETWORK",  "id":22944,   "ctx":"conn18","msg":"Connection ended","attr":{"remote":"127.0.0.1:58816","uuid":"3a456f02-a193-4e06-8229-4011578e921a","connectionId":18,"connectionCount":3}}
{"t":{"$date":"2024-04-09T03:57:15.225+00:00"},"s":"I",  "c":"NETWORK",  "id":51800,   "ctx":"conn20","msg":"client metadata","attr":{"remote":"127.0.0.1:43552","client":"conn20","negotiatedCompressors":[],"doc":{"driver":{"name":"mongo-java-driver|reactive-streams|spring-boot","version":"4.8.2"},"os":{"type":"Linux","name":"Linux","architecture":"amd64","version":"5.15.49-linuxkit"},"platform":"Java/Eclipse Adoptium/17.0.9+9"}}}
{"t":{"$date":"2024-04-09T03:57:15.289+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"Checkpointer","msg":"WiredTiger message","attr":{"message":"[1712635035:289448][1503:0x7f423029d700], WT_SESSION.checkpoint: [WT_VERB_CHECKPOINT_PROGRESS] saving checkpoint snapshot min: 775, snapshot max: 775 snapshot count: 0, oldest timestamp: (1712634734, 1) , meta checkpoint timestamp: (1712634897, 1) base write gen: 435"}}
{"t":{"$date":"2024-04-09T03:57:15.319+00:00"},"s":"I",  "c":"NETWORK",  "id":22943,   "ctx":"listener","msg":"Connection accepted","attr":{"remote":"127.0.0.1:43562","uuid":"c9af6ad8-a0b3-44ee-b9a9-723dc75d8a79","connectionId":21,"connectionCount":4}}
{"t":{"$date":"2024-04-09T03:57:15.337+00:00"},"s":"I",  "c":"NETWORK",  "id":51800,   "ctx":"conn21","msg":"client metadata","attr":{"remote":"127.0.0.1:43562","client":"conn21","negotiatedCompressors":[],"doc":{"driver":{"name":"mongo-java-driver|sync|spring-boot","version":"4.8.2"},"os":{"type":"Linux","name":"Linux","architecture":"amd64","version":"5.15.49-linuxkit"},"platform":"Java/Eclipse Adoptium/17.0.9+9"}}}
{"t":{"$date":"2024-04-09T03:58:15.727+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"Checkpointer","msg":"WiredTiger message","attr":{"message":"[1712635095:727879][1503:0x7f423029d700], WT_SESSION.checkpoint: [WT_VERB_CHECKPOINT_PROGRESS] saving checkpoint snapshot min: 794, snapshot max: 794 snapshot count: 0, oldest timestamp: (1712634794, 1) , meta checkpoint timestamp: (1712635094, 1) base write gen: 435"}}
{"t":{"$date":"2024-04-09T04:26:19.653+00:00"},"s":"I",  "c":"-",        "id":20883,   "ctx":"conn20","msg":"Interrupted operation as its client disconnected","attr":{"opId":39650}}
{"t":{"$date":"2024-04-09T04:26:18.596+00:00"},"s":"I",  "c":"-",        "id":20883,   "ctx":"conn20","msg":"Interrupted operation as its client disconnected","attr":{"opId":39706}}
{"t":{"$date":"2024-04-09T04:26:18.650+00:00"},"s":"I",  "c":"NETWORK",  "id":22989,   "ctx":"conn20","msg":"Error sending response to client. Ending connection from remote","attr":{"error":{"code":9001,"codeName":"SocketException","errmsg":"Broken pipe"},"remote":"127.0.0.1:43552","connectionId":20}}
{"t":{"$date":"2024-04-09T04:26:18.711+00:00"},"s":"I",  "c":"NETWORK",  "id":22944,   "ctx":"conn20","msg":"Connection ended","attr":{"remote":"127.0.0.1:43552","uuid":"9a6232b6-0260-436b-ab4a-9d483913e9bf","connectionId":20,"connectionCount":4}}
{"t":{"$date":"2024-04-09T04:26:18.612+00:00"},"s":"I",  "c":"-",        "id":20883,   "ctx":"conn21","msg":"Interrupted operation as its client disconnected","attr":{"opId":39697}}
{"t":{"$date":"2024-04-09T04:26:18.764+00:00"},"s":"I",  "c":"NETWORK",  "id":22989,   "ctx":"conn21","msg":"Error sending response to client. Ending connection from remote","attr":{"error":{"code":9001,"codeName":"SocketException","errmsg":"Broken pipe"},"remote":"127.0.0.1:43562","connectionId":21}}
{"t":{"$date":"2024-04-09T04:26:18.775+00:00"},"s":"I",  "c":"NETWORK",  "id":22944,   "ctx":"conn21","msg":"Connection ended","attr":{"remote":"127.0.0.1:43562","uuid":"c9af6ad8-a0b3-44ee-b9a9-723dc75d8a79","connectionId":21,"connectionCount":3}}
{"t":{"$date":"2024-04-09T04:26:18.595+00:00"},"s":"I",  "c":"NETWORK",  "id":22943,   "ctx":"listener","msg":"Connection accepted","attr":{"remote":"127.0.0.1:39596","uuid":"b162fd07-7ced-4c76-873c-719e3f0f75f5","connectionId":22,"connectionCount":5}}
{"t":{"$date":"2024-04-09T04:26:19.693+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"Checkpointer","msg":"WiredTiger message","attr":{"message":"[1712636779:693786][1503:0x7f423029d700], WT_SESSION.checkpoint: [WT_VERB_CHECKPOINT_PROGRESS] saving checkpoint snapshot min: 796, snapshot max: 796 snapshot count: 0, oldest timestamp: (1712634794, 1) , meta checkpoint timestamp: (1712635094, 1) base write gen: 435"}}
{"t":{"$date":"2024-04-09T04:26:18.784+00:00"},"s":"I",  "c":"NETWORK",  "id":22943,   "ctx":"listener","msg":"Connection accepted","attr":{"remote":"127.0.0.1:39612","uuid":"8665a5c6-4060-4ce1-bf36-1fc6cf7a9fc3","connectionId":23,"connectionCount":4}}
{"t":{"$date":"2024-04-09T04:26:18.784+00:00"},"s":"I",  "c":"NETWORK",  "id":51800,   "ctx":"conn22","msg":"client metadata","attr":{"remote":"127.0.0.1:39596","client":"conn22","negotiatedCompressors":[],"doc":{"driver":{"name":"mongo-java-driver|reactive-streams|spring-boot","version":"4.8.2"},"os":{"type":"Linux","name":"Linux","architecture":"amd64","version":"5.15.49-linuxkit"},"platform":"Java/Eclipse Adoptium/17.0.9+9"}}}
{"t":{"$date":"2024-04-09T04:26:18.795+00:00"},"s":"I",  "c":"NETWORK",  "id":51800,   "ctx":"conn23","msg":"client metadata","attr":{"remote":"127.0.0.1:39612","client":"conn23","negotiatedCompressors":[],"doc":{"driver":{"name":"mongo-java-driver|sync|spring-boot","version":"4.8.2"},"os":{"type":"Linux","name":"Linux","architecture":"amd64","version":"5.15.49-linuxkit"},"platform":"Java/Eclipse Adoptium/17.0.9+9"}}}
{"t":{"$date":"2024-04-09T04:26:18.801+00:00"},"s":"I",  "c":"NETWORK",  "id":22943,   "ctx":"listener","msg":"Connection accepted","attr":{"remote":"127.0.0.1:39616","uuid":"841b9d92-d088-4f10-955b-a4a5e9d3daf6","connectionId":24,"connectionCount":5}}
{"t":{"$date":"2024-04-09T04:26:18.806+00:00"},"s":"I",  "c":"NETWORK",  "id":51800,   "ctx":"conn24","msg":"client metadata","attr":{"remote":"127.0.0.1:39616","client":"conn24","negotiatedCompressors":[],"doc":{"driver":{"name":"mongo-java-driver|reactive-streams|spring-boot","version":"4.8.2"},"os":{"type":"Linux","name":"Linux","architecture":"amd64","version":"5.15.49-linuxkit"},"platform":"Java/Eclipse Adoptium/17.0.9+9"}}}
{"t":{"$date":"2024-04-09T04:26:18.852+00:00"},"s":"I",  "c":"ACCESS",   "id":20250,   "ctx":"conn24","msg":"Authentication succeeded","attr":{"mechanism":"SCRAM-SHA-256","speculative":true,"principalName":"appsmith","authenticationDatabase":"appsmith","remote":"127.0.0.1:39616","extraInfo":{}}}
{"t":{"$date":"2024-04-09T04:27:20.816+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"Checkpointer","msg":"WiredTiger message","attr":{"message":"[1712636840:816537][1503:0x7f423029d700], WT_SESSION.checkpoint: [WT_VERB_CHECKPOINT_PROGRESS] saving checkpoint snapshot min: 812, snapshot max: 812 snapshot count: 0, oldest timestamp: (1712636538, 1) , meta checkpoint timestamp: (1712636838, 1) base write gen: 435"}}
{"t":{"$date":"2024-04-09T04:59:04.324+00:00"},"s":"I",  "c":"-",        "id":20883,   "ctx":"conn22","msg":"Interrupted operation as its client disconnected","attr":{"opId":40626}}
{"t":{"$date":"2024-04-09T04:59:03.345+00:00"},"s":"I",  "c":"NETWORK",  "id":22989,   "ctx":"conn22","msg":"Error sending response to client. Ending connection from remote","attr":{"error":{"code":9001,"codeName":"SocketException","errmsg":"Broken pipe"},"remote":"127.0.0.1:39596","connectionId":22}}
{"t":{"$date":"2024-04-09T04:59:03.333+00:00"},"s":"I",  "c":"NETWORK",  "id":22944,   "ctx":"conn24","msg":"Connection ended","attr":{"remote":"127.0.0.1:39616","uuid":"841b9d92-d088-4f10-955b-a4a5e9d3daf6","connectionId":24,"connectionCount":4}}
{"t":{"$date":"2024-04-09T04:59:03.334+00:00"},"s":"I",  "c":"NETWORK",  "id":22943,   "ctx":"listener","msg":"Connection accepted","attr":{"remote":"127.0.0.1:37166","uuid":"3da5b184-f0ca-4862-b552-09077e3e6b80","connectionId":25,"connectionCount":5}}
{"t":{"$date":"2024-04-09T04:59:04.359+00:00"},"s":"I",  "c":"-",        "id":20883,   "ctx":"conn23","msg":"Interrupted operation as its client disconnected","attr":{"opId":40631}}
{"t":{"$date":"2024-04-09T04:59:03.349+00:00"},"s":"I",  "c":"COMMAND",  "id":51803,   "ctx":"LogicalSessionCacheRefresh","msg":"Slow query","attr":{"type":"command","ns":"config.$cmd","command":{"update":"system.sessions","ordered":false,"writeConcern":{"w":"majority","wtimeout":15000},"$db":"config"},"numYields":0,"reslen":356,"locks":{"ParallelBatchWriterMode":{"acquireCount":{"r":1}},"FeatureCompatibilityVersion":{"acquireCount":{"r":4,"w":1}},"ReplicationStateTransition":{"acquireCount":{"w":4}},"Global":{"acquireCount":{"r":4,"w":1}},"Database":{"acquireCount":{"r":2,"w":1}},"Collection":{"acquireCount":{"r":2,"w":1}},"Mutex":{"acquireCount":{"r":2}}},"flowControl":{"acquireCount":1,"timeAcquiringMicros":3},"writeConcern":{"w":"majority","wtimeout":15000,"provenance":"clientSupplied"},"waitForWriteConcernDurationMillis":449,"storage":{},"protocol":"op_msg","durationMillis":450}}
{"t":{"$date":"2024-04-09T04:59:03.350+00:00"},"s":"I",  "c":"NETWORK",  "id":22944,   "ctx":"conn22","msg":"Connection ended","attr":{"remote":"127.0.0.1:39596","uuid":"b162fd07-7ced-4c76-873c-719e3f0f75f5","connectionId":22,"connectionCount":4}}
{"t":{"$date":"2024-04-09T04:59:03.361+00:00"},"s":"I",  "c":"NETWORK",  "id":51800,   "ctx":"conn25","msg":"client metadata","attr":{"remote":"127.0.0.1:37166","client":"conn25","negotiatedCompressors":[],"doc":{"driver":{"name":"mongo-java-driver|reactive-streams|spring-boot","version":"4.8.2"},"os":{"type":"Linux","name":"Linux","architecture":"amd64","version":"5.15.49-linuxkit"},"platform":"Java/Eclipse Adoptium/17.0.9+9"}}}
{"t":{"$date":"2024-04-09T04:59:03.367+00:00"},"s":"I",  "c":"NETWORK",  "id":22989,   "ctx":"conn23","msg":"Error sending response to client. Ending connection from remote","attr":{"error":{"code":9001,"codeName":"SocketException","errmsg":"Broken pipe"},"remote":"127.0.0.1:39612","connectionId":23}}
{"t":{"$date":"2024-04-09T04:59:03.411+00:00"},"s":"I",  "c":"NETWORK",  "id":22944,   "ctx":"conn23","msg":"Connection ended","attr":{"remote":"127.0.0.1:39612","uuid":"8665a5c6-4060-4ce1-bf36-1fc6cf7a9fc3","connectionId":23,"connectionCount":4}}
{"t":{"$date":"2024-04-09T04:59:03.385+00:00"},"s":"I",  "c":"NETWORK",  "id":22943,   "ctx":"listener","msg":"Connection accepted","attr":{"remote":"127.0.0.1:37180","uuid":"ebe5f3e3-9664-4c3c-88c3-d38d85c1161b","connectionId":26,"connectionCount":5}}
{"t":{"$date":"2024-04-09T04:59:03.417+00:00"},"s":"I",  "c":"NETWORK",  "id":22943,   "ctx":"listener","msg":"Connection accepted","attr":{"remote":"127.0.0.1:37194","uuid":"dbec4fef-e650-486c-a1e5-d2eb129e8b5c","connectionId":27,"connectionCount":5}}
{"t":{"$date":"2024-04-09T04:59:03.418+00:00"},"s":"I",  "c":"NETWORK",  "id":51800,   "ctx":"conn26","msg":"client metadata","attr":{"remote":"127.0.0.1:37180","client":"conn26","negotiatedCompressors":[],"doc":{"driver":{"name":"mongo-java-driver|sync|spring-boot","version":"4.8.2"},"os":{"type":"Linux","name":"Linux","architecture":"amd64","version":"5.15.49-linuxkit"},"platform":"Java/Eclipse Adoptium/17.0.9+9"}}}
{"t":{"$date":"2024-04-09T04:59:03.420+00:00"},"s":"I",  "c":"NETWORK",  "id":51800,   "ctx":"conn27","msg":"client metadata","attr":{"remote":"127.0.0.1:37194","client":"conn27","negotiatedCompressors":[],"doc":{"driver":{"name":"mongo-java-driver|reactive-streams|spring-boot","version":"4.8.2"},"os":{"type":"Linux","name":"Linux","architecture":"amd64","version":"5.15.49-linuxkit"},"platform":"Java/Eclipse Adoptium/17.0.9+9"}}}
{"t":{"$date":"2024-04-09T04:59:03.469+00:00"},"s":"I",  "c":"ACCESS",   "id":20250,   "ctx":"conn27","msg":"Authentication succeeded","attr":{"mechanism":"SCRAM-SHA-256","speculative":true,"principalName":"appsmith","authenticationDatabase":"appsmith","remote":"127.0.0.1:37194","extraInfo":{}}}
{"t":{"$date":"2024-04-09T04:59:03.474+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"Checkpointer","msg":"WiredTiger message","attr":{"message":"[1712638743:474950][1503:0x7f423029d700], WT_SESSION.checkpoint: [WT_VERB_CHECKPOINT_PROGRESS] saving checkpoint snapshot min: 818, snapshot max: 818 snapshot count: 0, oldest timestamp: (1712638444, 1) , meta checkpoint timestamp: (1712636838, 1) base write gen: 435"}}
{"t":{"$date":"2024-04-09T05:00:06.290+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"Checkpointer","msg":"WiredTiger message","attr":{"message":"[1712638806:290036][1503:0x7f423029d700], WT_SESSION.checkpoint: [WT_VERB_CHECKPOINT_PROGRESS] saving checkpoint snapshot min: 830, snapshot max: 830 snapshot count: 0, oldest timestamp: (1712638504, 1) , meta checkpoint timestamp: (1712638794, 1) base write gen: 435"}}
{"t":{"$date":"2024-04-09T05:08:05.483+00:00"},"s":"I",  "c":"-",        "id":20883,   "ctx":"conn25","msg":"Interrupted operation as its client disconnected","attr":{"opId":41562}}
{"t":{"$date":"2024-04-09T05:08:05.818+00:00"},"s":"I",  "c":"NETWORK",  "id":22989,   "ctx":"conn25","msg":"Error sending response to client. Ending connection from remote","attr":{"error":{"code":9001,"codeName":"SocketException","errmsg":"Broken pipe"},"remote":"127.0.0.1:37166","connectionId":25}}
{"t":{"$date":"2024-04-09T05:08:05.827+00:00"},"s":"I",  "c":"NETWORK",  "id":22943,   "ctx":"listener","msg":"Connection accepted","attr":{"remote":"127.0.0.1:36138","uuid":"b909e693-bf8b-4db2-a9c6-2b7b3c859bc9","connectionId":28,"connectionCount":6}}
{"t":{"$date":"2024-04-09T05:08:05.890+00:00"},"s":"I",  "c":"NETWORK",  "id":22944,   "ctx":"conn25","msg":"Connection ended","attr":{"remote":"127.0.0.1:37166","uuid":"3da5b184-f0ca-4862-b552-09077e3e6b80","connectionId":25,"connectionCount":5}}
{"t":{"$date":"2024-04-09T05:08:05.890+00:00"},"s":"I",  "c":"COMMAND",  "id":51803,   "ctx":"LogicalSessionCacheRefresh","msg":"Slow query","attr":{"type":"command","ns":"config.$cmd","command":{"update":"system.sessions","ordered":false,"writeConcern":{"w":"majority","wtimeout":15000},"$db":"config"},"numYields":0,"reslen":356,"locks":{"ParallelBatchWriterMode":{"acquireCount":{"r":1}},"FeatureCompatibilityVersion":{"acquireCount":{"r":4,"w":1}},"ReplicationStateTransition":{"acquireCount":{"w":4}},"Global":{"acquireCount":{"r":4,"w":1}},"Database":{"acquireCount":{"r":2,"w":1}},"Collection":{"acquireCount":{"r":2,"w":1}},"Mutex":{"acquireCount":{"r":2}}},"flowControl":{"acquireCount":1,"timeAcquiringMicros":2},"writeConcern":{"w":"majority","wtimeout":15000,"provenance":"clientSupplied"},"waitForWriteConcernDurationMillis":416,"storage":{},"protocol":"op_msg","durationMillis":416}}
{"t":{"$date":"2024-04-09T05:08:05.526+00:00"},"s":"I",  "c":"-",        "id":20883,   "ctx":"conn26","msg":"Interrupted operation as its client disconnected","attr":{"opId":41479}}
{"t":{"$date":"2024-04-09T05:08:05.941+00:00"},"s":"I",  "c":"NETWORK",  "id":22944,   "ctx":"conn27","msg":"Connection ended","attr":{"remote":"127.0.0.1:37194","uuid":"dbec4fef-e650-486c-a1e5-d2eb129e8b5c","connectionId":27,"connectionCount":4}}
{"t":{"$date":"2024-04-09T05:08:05.944+00:00"},"s":"I",  "c":"NETWORK",  "id":22943,   "ctx":"listener","msg":"Connection accepted","attr":{"remote":"127.0.0.1:36140","uuid":"2f435a64-15af-42d6-8aa0-debcf4ba20c1","connectionId":29,"connectionCount":5}}
{"t":{"$date":"2024-04-09T05:08:05.949+00:00"},"s":"I",  "c":"NETWORK",  "id":51800,   "ctx":"conn28","msg":"client metadata","attr":{"remote":"127.0.0.1:36138","client":"conn28","negotiatedCompressors":[],"doc":{"driver":{"name":"mongo-java-driver|sync|spring-boot","version":"4.8.2"},"os":{"type":"Linux","name":"Linux","architecture":"amd64","version":"5.15.49-linuxkit"},"platform":"Java/Eclipse Adoptium/17.0.9+9"}}}
{"t":{"$date":"2024-04-09T05:08:05.963+00:00"},"s":"I",  "c":"-",        "id":20883,   "ctx":"conn26","msg":"Interrupted operation as its client disconnected","attr":{"opId":41573}}
{"t":{"$date":"2024-04-09T05:08:05.969+00:00"},"s":"I",  "c":"NETWORK",  "id":51800,   "ctx":"conn29","msg":"client metadata","attr":{"remote":"127.0.0.1:36140","client":"conn29","negotiatedCompressors":[],"doc":{"driver":{"name":"mongo-java-driver|reactive-streams|spring-boot","version":"4.8.2"},"os":{"type":"Linux","name":"Linux","architecture":"amd64","version":"5.15.49-linuxkit"},"platform":"Java/Eclipse Adoptium/17.0.9+9"}}}
{"t":{"$date":"2024-04-09T05:08:05.978+00:00"},"s":"I",  "c":"NETWORK",  "id":22989,   "ctx":"conn26","msg":"Error sending response to client. Ending connection from remote","attr":{"error":{"code":9001,"codeName":"SocketException","errmsg":"Broken pipe"},"remote":"127.0.0.1:37180","connectionId":26}}
{"t":{"$date":"2024-04-09T05:08:05.984+00:00"},"s":"I",  "c":"NETWORK",  "id":22944,   "ctx":"conn26","msg":"Connection ended","attr":{"remote":"127.0.0.1:37180","uuid":"ebe5f3e3-9664-4c3c-88c3-d38d85c1161b","connectionId":26,"connectionCount":4}}
{"t":{"$date":"2024-04-09T05:08:06.046+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"Checkpointer","msg":"WiredTiger message","attr":{"message":"[1712639286:46354][1503:0x7f423029d700], WT_SESSION.checkpoint: [WT_VERB_CHECKPOINT_PROGRESS] saving checkpoint snapshot min: 839, snapshot max: 839 snapshot count: 0, oldest timestamp: (1712638985, 2) , meta checkpoint timestamp: (1712638804, 1) base write gen: 435"}}
{"t":{"$date":"2024-04-09T05:09:06.095+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"Checkpointer","msg":"WiredTiger message","attr":{"message":"[1712639346:95937][1503:0x7f423029d700], WT_SESSION.checkpoint: [WT_VERB_CHECKPOINT_PROGRESS] saving checkpoint snapshot min: 852, snapshot max: 852 snapshot count: 0, oldest timestamp: (1712639045, 1) , meta checkpoint timestamp: (1712639345, 1) base write gen: 435"}}
{"t":{"$date":"2024-04-09T05:10:06.212+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"Checkpointer","msg":"WiredTiger message","attr":{"message":"[1712639406:212140][1503:0x7f423029d700], WT_SESSION.checkpoint: [WT_VERB_CHECKPOINT_PROGRESS] saving checkpoint snapshot min: 867, snapshot max: 867 snapshot count: 0, oldest timestamp: (1712639105, 1) , meta checkpoint timestamp: (1712639405, 1) base write gen: 435"}}
{"t":{"$date":"2024-04-09T05:11:06.255+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"Checkpointer","msg":"WiredTiger message","attr":{"message":"[1712639466:255945][1503:0x7f423029d700], WT_SESSION.checkpoint: [WT_VERB_CHECKPOINT_PROGRESS] saving checkpoint snapshot min: 882, snapshot max: 882 snapshot count: 0, oldest timestamp: (1712639165, 1) , meta checkpoint timestamp: (1712639465, 1) base write gen: 435"}}
{"t":{"$date":"2024-04-09T05:11:47.837+00:00"},"s":"I",  "c":"NETWORK",  "id":22943,   "ctx":"listener","msg":"Connection accepted","attr":{"remote":"127.0.0.1:41044","uuid":"396bdf0f-9e58-42a5-80dd-1cd187852d08","connectionId":30,"connectionCount":5}}
{"t":{"$date":"2024-04-09T05:11:47.840+00:00"},"s":"I",  "c":"NETWORK",  "id":51800,   "ctx":"conn30","msg":"client metadata","attr":{"remote":"127.0.0.1:41044","client":"conn30","negotiatedCompressors":[],"doc":{"driver":{"name":"mongo-java-driver|reactive-streams|spring-boot","version":"4.8.2"},"os":{"type":"Linux","name":"Linux","architecture":"amd64","version":"5.15.49-linuxkit"},"platform":"Java/Eclipse Adoptium/17.0.9+9"}}}
{"t":{"$date":"2024-04-09T05:11:47.865+00:00"},"s":"I",  "c":"ACCESS",   "id":20250,   "ctx":"conn30","msg":"Authentication succeeded","attr":{"mechanism":"SCRAM-SHA-256","speculative":true,"principalName":"appsmith","authenticationDatabase":"appsmith","remote":"127.0.0.1:41044","extraInfo":{}}}
{"t":{"$date":"2024-04-09T05:12:06.305+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"Checkpointer","msg":"WiredTiger message","attr":{"message":"[1712639526:305825][1503:0x7f423029d700], WT_SESSION.checkpoint: [WT_VERB_CHECKPOINT_PROGRESS] saving checkpoint snapshot min: 897, snapshot max: 897 snapshot count: 0, oldest timestamp: (1712639225, 1) , meta checkpoint timestamp: (1712639525, 1) base write gen: 435"}}
{"t":{"$date":"2024-04-09T05:12:10.829+00:00"},"s":"I",  "c":"NETWORK",  "id":22943,   "ctx":"listener","msg":"Connection accepted","attr":{"remote":"127.0.0.1:51424","uuid":"e995c709-e593-46b8-b063-19f4c01cc06b","connectionId":31,"connectionCount":6}}
{"t":{"$date":"2024-04-09T05:12:10.830+00:00"},"s":"I",  "c":"NETWORK",  "id":22943,   "ctx":"listener","msg":"Connection accepted","attr":{"remote":"127.0.0.1:51430","uuid":"08826406-3d68-42b6-9a22-b8d36212e1fd","connectionId":32,"connectionCount":7}}
{"t":{"$date":"2024-04-09T05:12:10.830+00:00"},"s":"I",  "c":"NETWORK",  "id":51800,   "ctx":"conn31","msg":"client metadata","attr":{"remote":"127.0.0.1:51424","client":"conn31","negotiatedCompressors":[],"doc":{"driver":{"name":"mongo-java-driver|reactive-streams|spring-boot","version":"4.8.2"},"os":{"type":"Linux","name":"Linux","architecture":"amd64","version":"5.15.49-linuxkit"},"platform":"Java/Eclipse Adoptium/17.0.9+9"}}}
{"t":{"$date":"2024-04-09T05:12:10.832+00:00"},"s":"I",  "c":"NETWORK",  "id":51800,   "ctx":"conn32","msg":"client metadata","attr":{"remote":"127.0.0.1:51430","client":"conn32","negotiatedCompressors":[],"doc":{"driver":{"name":"mongo-java-driver|reactive-streams|spring-boot","version":"4.8.2"},"os":{"type":"Linux","name":"Linux","architecture":"amd64","version":"5.15.49-linuxkit"},"platform":"Java/Eclipse Adoptium/17.0.9+9"}}}
{"t":{"$date":"2024-04-09T05:12:10.856+00:00"},"s":"I",  "c":"ACCESS",   "id":20250,   "ctx":"conn31","msg":"Authentication succeeded","attr":{"mechanism":"SCRAM-SHA-256","speculative":true,"principalName":"appsmith","authenticationDatabase":"appsmith","remote":"127.0.0.1:51424","extraInfo":{}}}
{"t":{"$date":"2024-04-09T05:12:10.856+00:00"},"s":"I",  "c":"ACCESS",   "id":20250,   "ctx":"conn32","msg":"Authentication succeeded","attr":{"mechanism":"SCRAM-SHA-256","speculative":true,"principalName":"appsmith","authenticationDatabase":"appsmith","remote":"127.0.0.1:51430","extraInfo":{}}}
{"t":{"$date":"2024-04-09T05:13:06.395+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"Checkpointer","msg":"WiredTiger message","attr":{"message":"[1712639586:395005][1503:0x7f423029d700], WT_SESSION.checkpoint: [WT_VERB_CHECKPOINT_PROGRESS] saving checkpoint snapshot min: 916, snapshot max: 916 snapshot count: 0, oldest timestamp: (1712639285, 2) , meta checkpoint timestamp: (1712639585, 2) base write gen: 435"}}
{"t":{"$date":"2024-04-09T05:14:06.442+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"Checkpointer","msg":"WiredTiger message","attr":{"message":"[1712639646:442605][1503:0x7f423029d700], WT_SESSION.checkpoint: [WT_VERB_CHECKPOINT_PROGRESS] saving checkpoint snapshot min: 931, snapshot max: 931 snapshot count: 0, oldest timestamp: (1712639345, 1) , meta checkpoint timestamp: (1712639645, 1) base write gen: 435"}}
{"t":{"$date":"2024-04-09T05:15:06.473+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"Checkpointer","msg":"WiredTiger message","attr":{"message":"[1712639706:473167][1503:0x7f423029d700], WT_SESSION.checkpoint: [WT_VERB_CHECKPOINT_PROGRESS] saving checkpoint snapshot min: 946, snapshot max: 946 snapshot count: 0, oldest timestamp: (1712639405, 1) , meta checkpoint timestamp: (1712639705, 1) base write gen: 435"}}
{"t":{"$date":"2024-04-09T05:16:06.523+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"Checkpointer","msg":"WiredTiger message","attr":{"message":"[1712639766:523393][1503:0x7f423029d700], WT_SESSION.checkpoint: [WT_VERB_CHECKPOINT_PROGRESS] saving checkpoint snapshot min: 961, snapshot max: 961 snapshot count: 0, oldest timestamp: (1712639465, 1) , meta checkpoint timestamp: (1712639765, 1) base write gen: 435"}}
{"t":{"$date":"2024-04-09T05:17:06.556+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"Checkpointer","msg":"WiredTiger message","attr":{"message":"[1712639826:556632][1503:0x7f423029d700], WT_SESSION.checkpoint: [WT_VERB_CHECKPOINT_PROGRESS] saving checkpoint snapshot min: 976, snapshot max: 976 snapshot count: 0, oldest timestamp: (1712639525, 1) , meta checkpoint timestamp: (1712639825, 1) base write gen: 435"}}
{"t":{"$date":"2024-04-09T05:18:06.601+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"Checkpointer","msg":"WiredTiger message","attr":{"message":"[1712639886:601172][1503:0x7f423029d700], WT_SESSION.checkpoint: [WT_VERB_CHECKPOINT_PROGRESS] saving checkpoint snapshot min: 991, snapshot max: 991 snapshot count: 0, oldest timestamp: (1712639585, 1) , meta checkpoint timestamp: (1712639885, 1) base write gen: 435"}}
{"t":{"$date":"2024-04-09T05:19:06.651+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"Checkpointer","msg":"WiredTiger message","attr":{"message":"[1712639946:651124][1503:0x7f423029d700], WT_SESSION.checkpoint: [WT_VERB_CHECKPOINT_PROGRESS] saving checkpoint snapshot min: 1006, snapshot max: 1006 snapshot count: 0, oldest timestamp: (1712639645, 1) , meta checkpoint timestamp: (1712639945, 1) base write gen: 435"}}
{"t":{"$date":"2024-04-09T05:20:06.697+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"Checkpointer","msg":"WiredTiger message","attr":{"message":"[1712640006:697223][1503:0x7f423029d700], WT_SESSION.checkpoint: [WT_VERB_CHECKPOINT_PROGRESS] saving checkpoint snapshot min: 1021, snapshot max: 1021 snapshot count: 0, oldest timestamp: (1712639705, 1) , meta checkpoint timestamp: (1712640005, 1) base write gen: 435"}}
{"t":{"$date":"2024-04-09T05:21:06.732+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"Checkpointer","msg":"WiredTiger message","attr":{"message":"[1712640066:732056][1503:0x7f423029d700], WT_SESSION.checkpoint: [WT_VERB_CHECKPOINT_PROGRESS] saving checkpoint snapshot min: 1036, snapshot max: 1036 snapshot count: 0, oldest timestamp: (1712639765, 1) , meta checkpoint timestamp: (1712640065, 1) base write gen: 435"}}
{"t":{"$date":"2024-04-09T05:22:06.779+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"Checkpointer","msg":"WiredTiger message","attr":{"message":"[1712640126:779044][1503:0x7f423029d700], WT_SESSION.checkpoint: [WT_VERB_CHECKPOINT_PROGRESS] saving checkpoint snapshot min: 1051, snapshot max: 1051 snapshot count: 0, oldest timestamp: (1712639825, 1) , meta checkpoint timestamp: (1712640125, 1) base write gen: 435"}}
{"t":{"$date":"2024-04-09T05:23:06.809+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"Checkpointer","msg":"WiredTiger message","attr":{"message":"[1712640186:809619][1503:0x7f423029d700], WT_SESSION.checkpoint: [WT_VERB_CHECKPOINT_PROGRESS] saving checkpoint snapshot min: 1066, snapshot max: 1066 snapshot count: 0, oldest timestamp: (1712639885, 1) , meta checkpoint timestamp: (1712640185, 1) base write gen: 435"}}
{"t":{"$date":"2024-04-09T05:24:06.854+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"Checkpointer","msg":"WiredTiger message","attr":{"message":"[1712640246:854326][1503:0x7f423029d700], WT_SESSION.checkpoint: [WT_VERB_CHECKPOINT_PROGRESS] saving checkpoint snapshot min: 1081, snapshot max: 1081 snapshot count: 0, oldest timestamp: (1712639945, 1) , meta checkpoint timestamp: (1712640245, 1) base write gen: 435"}}
{"t":{"$date":"2024-04-09T05:24:26.144+00:00"},"s":"I",  "c":"NETWORK",  "id":22943,   "ctx":"listener","msg":"Connection accepted","attr":{"remote":"127.0.0.1:39588","uuid":"45140b9a-2d56-40bb-a7fb-b69e9418f2f8","connectionId":33,"connectionCount":8}}
{"t":{"$date":"2024-04-09T05:24:26.147+00:00"},"s":"I",  "c":"NETWORK",  "id":51800,   "ctx":"conn33","msg":"client metadata","attr":{"remote":"127.0.0.1:39588","client":"conn33","negotiatedCompressors":[],"doc":{"driver":{"name":"mongo-java-driver|sync|spring-boot","version":"4.8.2"},"os":{"type":"Linux","name":"Linux","architecture":"amd64","version":"5.15.49-linuxkit"},"platform":"Java/Eclipse Adoptium/17.0.9+9"}}}
{"t":{"$date":"2024-04-09T05:24:26.164+00:00"},"s":"I",  "c":"ACCESS",   "id":20250,   "ctx":"conn33","msg":"Authentication succeeded","attr":{"mechanism":"SCRAM-SHA-256","speculative":true,"principalName":"appsmith","authenticationDatabase":"appsmith","remote":"127.0.0.1:39588","extraInfo":{}}}
{"t":{"$date":"2024-04-09T05:24:26.170+00:00"},"s":"I",  "c":"NETWORK",  "id":22944,   "ctx":"conn33","msg":"Connection ended","attr":{"remote":"127.0.0.1:39588","uuid":"45140b9a-2d56-40bb-a7fb-b69e9418f2f8","connectionId":33,"connectionCount":7}}
{"t":{"$date":"2024-04-09T05:24:26.171+00:00"},"s":"I",  "c":"-",        "id":20883,   "ctx":"conn28","msg":"Interrupted operation as its client disconnected","attr":{"opId":55931}}
{"t":{"$date":"2024-04-09T05:24:26.173+00:00"},"s":"I",  "c":"NETWORK",  "id":22944,   "ctx":"conn28","msg":"Connection ended","attr":{"remote":"127.0.0.1:36138","uuid":"b909e693-bf8b-4db2-a9c6-2b7b3c859bc9","connectionId":28,"connectionCount":6}}
{"t":{"$date":"2024-04-09T05:24:26.175+00:00"},"s":"I",  "c":"NETWORK",  "id":22944,   "ctx":"conn10","msg":"Connection ended","attr":{"remote":"127.0.0.1:53800","uuid":"38a90adc-fbe7-4a6c-abb0-c03f6cb61b48","connectionId":10,"connectionCount":5}}
{"t":{"$date":"2024-04-09T05:24:26.306+00:00"},"s":"I",  "c":"NETWORK",  "id":22944,   "ctx":"conn31","msg":"Connection ended","attr":{"remote":"127.0.0.1:51424","uuid":"e995c709-e593-46b8-b063-19f4c01cc06b","connectionId":31,"connectionCount":4}}
{"t":{"$date":"2024-04-09T05:24:26.307+00:00"},"s":"I",  "c":"NETWORK",  "id":22944,   "ctx":"conn32","msg":"Connection ended","attr":{"remote":"127.0.0.1:51430","uuid":"08826406-3d68-42b6-9a22-b8d36212e1fd","connectionId":32,"connectionCount":3}}
{"t":{"$date":"2024-04-09T05:24:26.310+00:00"},"s":"I",  "c":"NETWORK",  "id":22944,   "ctx":"conn30","msg":"Connection ended","attr":{"remote":"127.0.0.1:41044","uuid":"396bdf0f-9e58-42a5-80dd-1cd187852d08","connectionId":30,"connectionCount":2}}
{"t":{"$date":"2024-04-09T05:24:26.310+00:00"},"s":"I",  "c":"-",        "id":20883,   "ctx":"conn29","msg":"Interrupted operation as its client disconnected","attr":{"opId":55933}}
{"t":{"$date":"2024-04-09T05:24:26.323+00:00"},"s":"I",  "c":"NETWORK",  "id":22944,   "ctx":"conn29","msg":"Connection ended","attr":{"remote":"127.0.0.1:36140","uuid":"2f435a64-15af-42d6-8aa0-debcf4ba20c1","connectionId":29,"connectionCount":0}}
{"t":{"$date":"2024-04-09T05:24:26.312+00:00"},"s":"I",  "c":"NETWORK",  "id":22944,   "ctx":"conn7","msg":"Connection ended","attr":{"remote":"127.0.0.1:37808","uuid":"ebb074c8-ac5f-42f6-b096-34135949aebf","connectionId":7,"connectionCount":1}}
{"t":{"$date":"2024-04-09T05:24:34.067+00:00"},"s":"I",  "c":"CONTROL",  "id":23377,   "ctx":"SignalHandler","msg":"Received signal","attr":{"signal":15,"error":"Terminated"}}
{"t":{"$date":"2024-04-09T05:24:34.069+00:00"},"s":"I",  "c":"CONTROL",  "id":23378,   "ctx":"SignalHandler","msg":"Signal was sent by kill(2)","attr":{"pid":1,"uid":0}}
{"t":{"$date":"2024-04-09T05:24:34.070+00:00"},"s":"I",  "c":"CONTROL",  "id":23381,   "ctx":"SignalHandler","msg":"will terminate after current cmd ends"}
{"t":{"$date":"2024-04-09T05:24:34.071+00:00"},"s":"I",  "c":"REPL",     "id":4784900, "ctx":"SignalHandler","msg":"Stepping down the ReplicationCoordinator for shutdown","attr":{"waitTimeMillis":15000}}
{"t":{"$date":"2024-04-09T05:24:34.071+00:00"},"s":"I",  "c":"REPL",     "id":4794602, "ctx":"SignalHandler","msg":"Attempting to enter quiesce mode"}
{"t":{"$date":"2024-04-09T05:24:34.072+00:00"},"s":"I",  "c":"COMMAND",  "id":4784901, "ctx":"SignalHandler","msg":"Shutting down the MirrorMaestro"}
{"t":{"$date":"2024-04-09T05:24:34.073+00:00"},"s":"I",  "c":"REPL",     "id":40441,   "ctx":"SignalHandler","msg":"Stopping TopologyVersionObserver"}
{"t":{"$date":"2024-04-09T05:24:34.074+00:00"},"s":"I",  "c":"REPL",     "id":40447,   "ctx":"TopologyVersionObserver","msg":"Stopped TopologyVersionObserver"}
{"t":{"$date":"2024-04-09T05:24:34.075+00:00"},"s":"I",  "c":"SHARDING", "id":4784902, "ctx":"SignalHandler","msg":"Shutting down the WaitForMajorityService"}
{"t":{"$date":"2024-04-09T05:24:34.076+00:00"},"s":"I",  "c":"CONTROL",  "id":4784903, "ctx":"SignalHandler","msg":"Shutting down the LogicalSessionCache"}
{"t":{"$date":"2024-04-09T05:24:34.078+00:00"},"s":"I",  "c":"NETWORK",  "id":20562,   "ctx":"SignalHandler","msg":"Shutdown: going to close listening sockets"}
{"t":{"$date":"2024-04-09T05:24:34.078+00:00"},"s":"I",  "c":"NETWORK",  "id":23017,   "ctx":"listener","msg":"removing socket file","attr":{"path":"/tmp/mongodb-27017.sock"}}
{"t":{"$date":"2024-04-09T05:24:34.080+00:00"},"s":"I",  "c":"NETWORK",  "id":4784905, "ctx":"SignalHandler","msg":"Shutting down the global connection pool"}
{"t":{"$date":"2024-04-09T05:24:34.081+00:00"},"s":"I",  "c":"CONTROL",  "id":4784906, "ctx":"SignalHandler","msg":"Shutting down the FlowControlTicketholder"}
{"t":{"$date":"2024-04-09T05:24:34.081+00:00"},"s":"I",  "c":"-",        "id":20520,   "ctx":"SignalHandler","msg":"Stopping further Flow Control ticket acquisitions."}
{"t":{"$date":"2024-04-09T05:24:34.082+00:00"},"s":"I",  "c":"REPL",     "id":4784907, "ctx":"SignalHandler","msg":"Shutting down the replica set node executor"}
{"t":{"$date":"2024-04-09T05:24:34.083+00:00"},"s":"I",  "c":"ASIO",     "id":22582,   "ctx":"ReplNodeDbWorkerNetwork","msg":"Killing all outstanding egress activity."}
{"t":{"$date":"2024-04-09T05:24:34.084+00:00"},"s":"I",  "c":"CONTROL",  "id":4784908, "ctx":"SignalHandler","msg":"Shutting down the PeriodicThreadToAbortExpiredTransactions"}
{"t":{"$date":"2024-04-09T05:24:34.084+00:00"},"s":"I",  "c":"REPL",     "id":4784909, "ctx":"SignalHandler","msg":"Shutting down the ReplicationCoordinator"}
{"t":{"$date":"2024-04-09T05:24:34.085+00:00"},"s":"I",  "c":"REPL",     "id":5074000, "ctx":"SignalHandler","msg":"Shutting down the replica set aware services."}
{"t":{"$date":"2024-04-09T05:24:34.085+00:00"},"s":"I",  "c":"REPL",     "id":5123006, "ctx":"SignalHandler","msg":"Shutting down PrimaryOnlyService","attr":{"service":"TenantMigrationRecipientService","numInstances":0,"numOperationContexts":0}}
{"t":{"$date":"2024-04-09T05:24:34.086+00:00"},"s":"I",  "c":"ASIO",     "id":22582,   "ctx":"TenantMigrationRecipientServiceNetwork","msg":"Killing all outstanding egress activity."}
{"t":{"$date":"2024-04-09T05:24:34.087+00:00"},"s":"I",  "c":"REPL",     "id":5123006, "ctx":"SignalHandler","msg":"Shutting down PrimaryOnlyService","attr":{"service":"TenantMigrationDonorService","numInstances":0,"numOperationContexts":0}}
{"t":{"$date":"2024-04-09T05:24:34.089+00:00"},"s":"I",  "c":"ASIO",     "id":22582,   "ctx":"TenantMigrationDonorServiceNetwork","msg":"Killing all outstanding egress activity."}
{"t":{"$date":"2024-04-09T05:24:34.090+00:00"},"s":"I",  "c":"REPL",     "id":21328,   "ctx":"SignalHandler","msg":"Shutting down replication subsystems"}
{"t":{"$date":"2024-04-09T05:24:34.090+00:00"},"s":"I",  "c":"REPL",     "id":21302,   "ctx":"SignalHandler","msg":"Stopping replication reporter thread"}
{"t":{"$date":"2024-04-09T05:24:34.091+00:00"},"s":"I",  "c":"REPL",     "id":21303,   "ctx":"SignalHandler","msg":"Stopping replication fetcher thread"}
{"t":{"$date":"2024-04-09T05:24:34.091+00:00"},"s":"I",  "c":"REPL",     "id":21304,   "ctx":"SignalHandler","msg":"Stopping replication applier thread"}
{"t":{"$date":"2024-04-09T05:24:34.091+00:00"},"s":"I",  "c":"REPL",     "id":21107,   "ctx":"BackgroundSync","msg":"Stopping replication producer"}
