{"t":{"$date":"2024-04-09T02:25:56.062+00:00"},"s":"I",  "c":"NETWORK",  "id":4915701, "ctx":"-","msg":"Initialized wire specification","attr":{"spec":{"incomingExternalClient":{"minWireVersion":0,"maxWireVersion":13},"incomingInternalClient":{"minWireVersion":0,"maxWireVersion":13},"outgoing":{"minWireVersion":0,"maxWireVersion":13},"isInternalClient":true}}}
{"t":{"$date":"2024-04-09T02:25:56.065+00:00"},"s":"I",  "c":"CONTROL",  "id":23285,   "ctx":"-","msg":"Automatically disabling TLS 1.0, to force-enable TLS 1.0 specify --sslDisabledProtocols 'none'"}
{"t":{"$date":"2024-04-09T02:25:56.071+00:00"},"s":"W",  "c":"ASIO",     "id":22601,   "ctx":"main","msg":"No TransportLayer configured during NetworkInterface startup"}
{"t":{"$date":"2024-04-09T02:25:56.074+00:00"},"s":"I",  "c":"NETWORK",  "id":4648601, "ctx":"main","msg":"Implicit TCP FastOpen unavailable. If TCP FastOpen is required, set tcpFastOpenServer, tcpFastOpenClient, and tcpFastOpenQueueSize."}
{"t":{"$date":"2024-04-09T02:25:56.471+00:00"},"s":"W",  "c":"ASIO",     "id":22601,   "ctx":"main","msg":"No TransportLayer configured during NetworkInterface startup"}
{"t":{"$date":"2024-04-09T02:25:56.472+00:00"},"s":"W",  "c":"ASIO",     "id":22601,   "ctx":"main","msg":"No TransportLayer configured during NetworkInterface startup"}
{"t":{"$date":"2024-04-09T02:25:56.477+00:00"},"s":"I",  "c":"REPL",     "id":5123008, "ctx":"main","msg":"Successfully registered PrimaryOnlyService","attr":{"service":"TenantMigrationDonorService","ns":"config.tenantMigrationDonors"}}
{"t":{"$date":"2024-04-09T02:25:56.479+00:00"},"s":"I",  "c":"REPL",     "id":5123008, "ctx":"main","msg":"Successfully registered PrimaryOnlyService","attr":{"service":"TenantMigrationRecipientService","ns":"config.tenantMigrationRecipients"}}
{"t":{"$date":"2024-04-09T02:25:56.481+00:00"},"s":"I",  "c":"CONTROL",  "id":5945603, "ctx":"main","msg":"Multi threading initialized"}
{"t":{"$date":"2024-04-09T02:25:56.504+00:00"},"s":"I",  "c":"CONTROL",  "id":4615611, "ctx":"initandlisten","msg":"MongoDB starting","attr":{"pid":1780,"port":27017,"dbPath":".","architecture":"64-bit","host":"f3f425c8c882"}}
{"t":{"$date":"2024-04-09T02:25:56.524+00:00"},"s":"I",  "c":"CONTROL",  "id":23403,   "ctx":"initandlisten","msg":"Build Info","attr":{"buildInfo":{"version":"5.0.26","gitVersion":"0b4f1ea980b5380a66425a90b414106a191365f4","openSSLVersion":"OpenSSL 1.1.1f  31 Mar 2020","modules":[],"allocator":"tcmalloc","environment":{"distmod":"ubuntu2004","distarch":"x86_64","target_arch":"x86_64"}}}}
{"t":{"$date":"2024-04-09T02:25:56.547+00:00"},"s":"I",  "c":"CONTROL",  "id":51765,   "ctx":"initandlisten","msg":"Operating System","attr":{"os":{"name":"Ubuntu","version":"20.04"}}}
{"t":{"$date":"2024-04-09T02:25:56.551+00:00"},"s":"I",  "c":"CONTROL",  "id":21951,   "ctx":"initandlisten","msg":"Options set by command line","attr":{"options":{"net":{"bindIp":"localhost","port":27017},"replication":{"replSet":"mr1"},"security":{"keyFile":"/tmp/appsmith/mongodb-key"},"storage":{"dbPath":"."},"systemLog":{"destination":"file","path":"/appsmith-stacks/logs/mongodb/db.log"}}}}
{"t":{"$date":"2024-04-09T02:25:56.592+00:00"},"s":"I",  "c":"STORAGE",  "id":22270,   "ctx":"initandlisten","msg":"Storage engine to use detected by data files","attr":{"dbpath":".","storageEngine":"wiredTiger"}}
{"t":{"$date":"2024-04-09T02:25:56.600+00:00"},"s":"I",  "c":"STORAGE",  "id":22315,   "ctx":"initandlisten","msg":"Opening WiredTiger","attr":{"config":"create,cache_size=3467M,session_max=33000,eviction=(threads_min=4,threads_max=4),config_base=false,statistics=(fast),log=(enabled=true,archive=true,path=journal,compressor=snappy),builtin_extension_config=(zstd=(compression_level=6)),file_manager=(close_idle_time=600,close_scan_interval=10,close_handle_minimum=2000),statistics_log=(wait=0),verbose=[recovery_progress,checkpoint_progress,compact_progress],"}}
{"t":{"$date":"2024-04-09T02:25:57.741+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"initandlisten","msg":"WiredTiger message","attr":{"message":"[1712629557:741419][1780:0x7f647e5c1c80], txn-recover: [WT_VERB_RECOVERY_PROGRESS] Recovering log 2 through 3"}}
{"t":{"$date":"2024-04-09T02:26:00.273+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"initandlisten","msg":"WiredTiger message","attr":{"message":"[1712629560:272921][1780:0x7f647e5c1c80], txn-recover: [WT_VERB_RECOVERY_PROGRESS] Recovering log 3 through 3"}}
{"t":{"$date":"2024-04-09T02:26:01.969+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"initandlisten","msg":"WiredTiger message","attr":{"message":"[1712629561:969382][1780:0x7f647e5c1c80], txn-recover: [WT_VERB_RECOVERY_ALL] Main recovery loop: starting at 2/71936 to 3/256"}}
{"t":{"$date":"2024-04-09T02:26:02.186+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"initandlisten","msg":"WiredTiger message","attr":{"message":"[1712629562:186736][1780:0x7f647e5c1c80], txn-recover: [WT_VERB_RECOVERY_PROGRESS] Recovering log 2 through 3"}}
{"t":{"$date":"2024-04-09T02:26:02.324+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"initandlisten","msg":"WiredTiger message","attr":{"message":"[1712629562:324460][1780:0x7f647e5c1c80], txn-recover: [WT_VERB_RECOVERY_PROGRESS] Recovering log 3 through 3"}}
{"t":{"$date":"2024-04-09T02:26:02.393+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"initandlisten","msg":"WiredTiger message","attr":{"message":"[1712629562:393151][1780:0x7f647e5c1c80], txn-recover: [WT_VERB_RECOVERY_PROGRESS] recovery log replay has successfully finished and ran for 4658 milliseconds"}}
{"t":{"$date":"2024-04-09T02:26:02.398+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"initandlisten","msg":"WiredTiger message","attr":{"message":"[1712629562:398763][1780:0x7f647e5c1c80], txn-recover: [WT_VERB_RECOVERY_ALL] Set global recovery timestamp: (**********, 8)"}}
{"t":{"$date":"2024-04-09T02:26:02.400+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"initandlisten","msg":"WiredTiger message","attr":{"message":"[1712629562:400271][1780:0x7f647e5c1c80], txn-recover: [WT_VERB_RECOVERY_ALL] Set global oldest timestamp: (**********, 1)"}}
{"t":{"$date":"2024-04-09T02:26:02.405+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"initandlisten","msg":"WiredTiger message","attr":{"message":"[1712629562:405234][1780:0x7f647e5c1c80], txn-recover: [WT_VERB_RECOVERY_PROGRESS] recovery rollback to stable has successfully finished and ran for 2 milliseconds"}}
{"t":{"$date":"2024-04-09T02:26:02.409+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"initandlisten","msg":"WiredTiger message","attr":{"message":"[1712629562:409817][1780:0x7f647e5c1c80], WT_SESSION.checkpoint: [WT_VERB_CHECKPOINT_PROGRESS] saving checkpoint snapshot min: 1, snapshot max: 1 snapshot count: 0, oldest timestamp: (**********, 1) , meta checkpoint timestamp: (**********, 8) base write gen: 33"}}
{"t":{"$date":"2024-04-09T02:26:02.430+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"initandlisten","msg":"WiredTiger message","attr":{"message":"[1712629562:430356][1780:0x7f647e5c1c80], txn-recover: [WT_VERB_RECOVERY_PROGRESS] recovery checkpoint has successfully finished and ran for 23 milliseconds"}}
{"t":{"$date":"2024-04-09T02:26:02.434+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"initandlisten","msg":"WiredTiger message","attr":{"message":"[1712629562:434887][1780:0x7f647e5c1c80], txn-recover: [WT_VERB_RECOVERY_PROGRESS] recovery was completed successfully and took 4699ms, including 4658ms for the log replay, 2ms for the rollback to stable, and 23ms for the checkpoint."}}
{"t":{"$date":"2024-04-09T02:26:02.440+00:00"},"s":"I",  "c":"STORAGE",  "id":4795906, "ctx":"initandlisten","msg":"WiredTiger opened","attr":{"durationMillis":5835}}
{"t":{"$date":"2024-04-09T02:26:02.441+00:00"},"s":"I",  "c":"RECOVERY", "id":23987,   "ctx":"initandlisten","msg":"WiredTiger recoveryTimestamp","attr":{"recoveryTimestamp":{"$timestamp":{"t":**********,"i":8}}}}
{"t":{"$date":"2024-04-09T02:26:02.442+00:00"},"s":"I",  "c":"RECOVERY", "id":5380106, "ctx":"initandlisten","msg":"WiredTiger oldestTimestamp","attr":{"oldestTimestamp":{"$timestamp":{"t":**********,"i":1}}}}
{"t":{"$date":"2024-04-09T02:26:02.487+00:00"},"s":"I",  "c":"STORAGE",  "id":22383,   "ctx":"initandlisten","msg":"The size storer reports that the oplog contains","attr":{"numRecords":7,"dataSize":1188}}
{"t":{"$date":"2024-04-09T02:26:02.488+00:00"},"s":"I",  "c":"STORAGE",  "id":22384,   "ctx":"initandlisten","msg":"Scanning the oplog to determine where to place markers for truncation"}
{"t":{"$date":"2024-04-09T02:26:02.505+00:00"},"s":"I",  "c":"STORAGE",  "id":22382,   "ctx":"initandlisten","msg":"WiredTiger record store oplog processing finished","attr":{"durationMillis":18}}
{"t":{"$date":"2024-04-09T02:26:02.510+00:00"},"s":"I",  "c":"STORAGE",  "id":22262,   "ctx":"initandlisten","msg":"Timestamp monitor starting"}
{"t":{"$date":"2024-04-09T02:26:02.516+00:00"},"s":"W",  "c":"CONTROL",  "id":22138,   "ctx":"initandlisten","msg":"You are running this process as the root user, which is not recommended","tags":["startupWarnings"]}
{"t":{"$date":"2024-04-09T02:26:02.534+00:00"},"s":"I",  "c":"NETWORK",  "id":4915702, "ctx":"initandlisten","msg":"Updated wire specification","attr":{"oldSpec":{"incomingExternalClient":{"minWireVersion":0,"maxWireVersion":13},"incomingInternalClient":{"minWireVersion":0,"maxWireVersion":13},"outgoing":{"minWireVersion":0,"maxWireVersion":13},"isInternalClient":true},"newSpec":{"incomingExternalClient":{"minWireVersion":0,"maxWireVersion":13},"incomingInternalClient":{"minWireVersion":13,"maxWireVersion":13},"outgoing":{"minWireVersion":13,"maxWireVersion":13},"isInternalClient":true}}}
{"t":{"$date":"2024-04-09T02:26:02.541+00:00"},"s":"I",  "c":"STORAGE",  "id":5071100, "ctx":"initandlisten","msg":"Clearing temp directory"}
{"t":{"$date":"2024-04-09T02:26:02.598+00:00"},"s":"I",  "c":"CONTROL",  "id":20536,   "ctx":"initandlisten","msg":"Flow Control is enabled on this deployment"}
{"t":{"$date":"2024-04-09T02:26:02.599+00:00"},"s":"I",  "c":"STORAGE",  "id":5380103, "ctx":"initandlisten","msg":"Unpin oldest timestamp request","attr":{"service":"_wt_startup","requestedTs":{"$timestamp":{"t":**********,"i":1}}}}
{"t":{"$date":"2024-04-09T02:26:02.604+00:00"},"s":"I",  "c":"SHARDING", "id":20997,   "ctx":"initandlisten","msg":"Refreshed RWC defaults","attr":{"newDefaults":{}}}
{"t":{"$date":"2024-04-09T02:26:02.605+00:00"},"s":"I",  "c":"FTDC",     "id":20625,   "ctx":"initandlisten","msg":"Initializing full-time diagnostic data capture","attr":{"dataDirectory":"./diagnostic.data"}}
{"t":{"$date":"2024-04-09T02:26:02.618+00:00"},"s":"I",  "c":"REPL",     "id":6015317, "ctx":"initandlisten","msg":"Setting new configuration state","attr":{"newState":"ConfigStartingUp","oldState":"ConfigPreStart"}}
{"t":{"$date":"2024-04-09T02:26:02.620+00:00"},"s":"I",  "c":"REPL",     "id":4280500, "ctx":"initandlisten","msg":"Attempting to create internal replication collections"}
{"t":{"$date":"2024-04-09T02:26:02.620+00:00"},"s":"I",  "c":"-",        "id":4939300, "ctx":"monitoring-keys-for-HMAC","msg":"Failed to refresh key cache","attr":{"error":"ReadConcernMajorityNotAvailableYet: Read concern majority reads are currently not possible.","nextWakeupMillis":200}}
{"t":{"$date":"2024-04-09T02:26:02.633+00:00"},"s":"I",  "c":"REPL",     "id":4280501, "ctx":"initandlisten","msg":"Attempting to load local voted for document"}
{"t":{"$date":"2024-04-09T02:26:02.633+00:00"},"s":"I",  "c":"REPL",     "id":4280502, "ctx":"initandlisten","msg":"Searching for local Rollback ID document"}
{"t":{"$date":"2024-04-09T02:26:02.644+00:00"},"s":"I",  "c":"REPL",     "id":21529,   "ctx":"initandlisten","msg":"Initializing rollback ID","attr":{"rbid":1}}
{"t":{"$date":"2024-04-09T02:26:02.647+00:00"},"s":"I",  "c":"REPL",     "id":4280504, "ctx":"initandlisten","msg":"Cleaning up any partially applied oplog batches & reading last op from oplog"}
{"t":{"$date":"2024-04-09T02:26:02.652+00:00"},"s":"I",  "c":"REPL",     "id":21544,   "ctx":"initandlisten","msg":"Recovering from stable timestamp","attr":{"stableTimestamp":{"$timestamp":{"t":**********,"i":8}},"topOfOplog":{"ts":{"$timestamp":{"t":**********,"i":8}},"t":1},"appliedThrough":{"ts":{"$timestamp":{"t":0,"i":0}},"t":-1}}}
{"t":{"$date":"2024-04-09T02:26:02.654+00:00"},"s":"I",  "c":"REPL",     "id":21545,   "ctx":"initandlisten","msg":"Starting recovery oplog application at the stable timestamp","attr":{"stableTimestamp":{"$timestamp":{"t":**********,"i":8}}}}
{"t":{"$date":"2024-04-09T02:26:02.655+00:00"},"s":"I",  "c":"REPL",     "id":5466604, "ctx":"initandlisten","msg":"Start point for recovery oplog application exists in oplog. No adjustment necessary","attr":{"startPoint":{"$timestamp":{"t":**********,"i":8}}}}
{"t":{"$date":"2024-04-09T02:26:02.656+00:00"},"s":"I",  "c":"REPL",     "id":21549,   "ctx":"initandlisten","msg":"No oplog entries to apply for recovery. Start point is at the top of the oplog"}
{"t":{"$date":"2024-04-09T02:26:02.657+00:00"},"s":"I",  "c":"REPL",     "id":4280505, "ctx":"initandlisten","msg":"Creating any necessary TenantMigrationAccessBlockers for unfinished migrations"}
{"t":{"$date":"2024-04-09T02:26:02.665+00:00"},"s":"I",  "c":"REPL",     "id":4280506, "ctx":"initandlisten","msg":"Reconstructing prepared transactions"}
{"t":{"$date":"2024-04-09T02:26:02.673+00:00"},"s":"I",  "c":"REPL",     "id":4280507, "ctx":"initandlisten","msg":"Loaded replica set config, scheduled callback to set local config"}
{"t":{"$date":"2024-04-09T02:26:02.673+00:00"},"s":"I",  "c":"REPL",     "id":4280508, "ctx":"ReplCoord-0","msg":"Attempting to set local replica set config; validating config for startup"}
{"t":{"$date":"2024-04-09T02:26:02.677+00:00"},"s":"I",  "c":"CONTROL",  "id":20714,   "ctx":"LogicalSessionCacheRefresh","msg":"Failed to refresh session cache, will try again at the next refresh interval","attr":{"error":"NotYetInitialized: Replication has not yet been configured"}}
{"t":{"$date":"2024-04-09T02:26:02.678+00:00"},"s":"I",  "c":"REPL",     "id":40440,   "ctx":"initandlisten","msg":"Starting the TopologyVersionObserver"}
{"t":{"$date":"2024-04-09T02:26:02.678+00:00"},"s":"I",  "c":"CONTROL",  "id":20711,   "ctx":"LogicalSessionCacheReap","msg":"Failed to reap transaction table","attr":{"error":"NotYetInitialized: Replication has not yet been configured"}}
{"t":{"$date":"2024-04-09T02:26:02.681+00:00"},"s":"I",  "c":"REPL",     "id":40445,   "ctx":"TopologyVersionObserver","msg":"Started TopologyVersionObserver"}
{"t":{"$date":"2024-04-09T02:26:02.683+00:00"},"s":"I",  "c":"NETWORK",  "id":23015,   "ctx":"listener","msg":"Listening on","attr":{"address":"/tmp/mongodb-27017.sock"}}
{"t":{"$date":"2024-04-09T02:26:02.684+00:00"},"s":"I",  "c":"NETWORK",  "id":23015,   "ctx":"listener","msg":"Listening on","attr":{"address":"127.0.0.1"}}
{"t":{"$date":"2024-04-09T02:26:02.685+00:00"},"s":"I",  "c":"NETWORK",  "id":23016,   "ctx":"listener","msg":"Waiting for connections","attr":{"port":27017,"ssl":"off"}}
{"t":{"$date":"2024-04-09T02:26:02.690+00:00"},"s":"I",  "c":"NETWORK",  "id":22943,   "ctx":"listener","msg":"Connection accepted","attr":{"remote":"127.0.0.1:48526","uuid":"90a0bf11-2062-40f9-b874-5a65fab56223","connectionId":2,"connectionCount":1}}
{"t":{"$date":"2024-04-09T02:26:02.715+00:00"},"s":"W",  "c":"COMMAND",  "id":5578800, "ctx":"conn2","msg":"Deprecated operation requested. The client driver may require an upgrade in order to ensure compatibility with future server versions. For more details see https://dochub.mongodb.org/core/legacy-opcode-compatibility","attr":{"op":"query","clientInfo":{"address":"127.0.0.1:48526"}}}
{"t":{"$date":"2024-04-09T02:26:02.786+00:00"},"s":"I",  "c":"NETWORK",  "id":22943,   "ctx":"listener","msg":"Connection accepted","attr":{"remote":"127.0.0.1:48534","uuid":"c2febaea-7660-4a8f-8703-c419e09d0bce","connectionId":3,"connectionCount":2}}
{"t":{"$date":"2024-04-09T02:26:02.798+00:00"},"s":"I",  "c":"NETWORK",  "id":51800,   "ctx":"conn3","msg":"client metadata","attr":{"remote":"127.0.0.1:48534","client":"conn3","negotiatedCompressors":[],"doc":{"application":{"name":"mongosh 2.2.2"},"driver":{"name":"nodejs|mongosh","version":"6.5.0|2.2.2"},"platform":"Node.js v20.11.1, LE","os":{"name":"linux","architecture":"x64","version":"3.10.0-327.22.2.el7.x86_64","type":"Linux"},"env":{"container":{"runtime":"docker"}}}}}
{"t":{"$date":"2024-04-09T02:26:02.817+00:00"},"s":"I",  "c":"NETWORK",  "id":22943,   "ctx":"listener","msg":"Connection accepted","attr":{"remote":"127.0.0.1:48538","uuid":"b51dc5e7-0860-4d10-a840-f0704b9295ef","connectionId":4,"connectionCount":3}}
{"t":{"$date":"2024-04-09T02:26:02.823+00:00"},"s":"I",  "c":"-",        "id":4939300, "ctx":"monitoring-keys-for-HMAC","msg":"Failed to refresh key cache","attr":{"error":"ReadConcernMajorityNotAvailableYet: Read concern majority reads are currently not possible.","nextWakeupMillis":400}}
{"t":{"$date":"2024-04-09T02:26:02.828+00:00"},"s":"I",  "c":"NETWORK",  "id":51800,   "ctx":"conn4","msg":"client metadata","attr":{"remote":"127.0.0.1:48538","client":"conn4","negotiatedCompressors":[],"doc":{"application":{"name":"mongosh 2.2.2"},"driver":{"name":"nodejs|mongosh","version":"6.5.0|2.2.2"},"platform":"Node.js v20.11.1, LE","os":{"name":"linux","architecture":"x64","version":"3.10.0-327.22.2.el7.x86_64","type":"Linux"},"env":{"container":{"runtime":"docker"}}}}}
{"t":{"$date":"2024-04-09T02:26:02.869+00:00"},"s":"I",  "c":"ACCESS",   "id":20250,   "ctx":"conn4","msg":"Authentication succeeded","attr":{"mechanism":"SCRAM-SHA-256","speculative":true,"principalName":"appsmith","authenticationDatabase":"appsmith","remote":"127.0.0.1:48538","extraInfo":{}}}
{"t":{"$date":"2024-04-09T02:26:02.899+00:00"},"s":"I",  "c":"ACCESS",   "id":20250,   "ctx":"conn2","msg":"Authentication succeeded","attr":{"mechanism":"SCRAM-SHA-256","speculative":false,"principalName":"__system","authenticationDatabase":"local","remote":"127.0.0.1:48526","extraInfo":{}}}
{"t":{"$date":"2024-04-09T02:26:02.901+00:00"},"s":"I",  "c":"REPL",     "id":4280509, "ctx":"ReplCoord-0","msg":"Local configuration validated for startup"}
{"t":{"$date":"2024-04-09T02:26:02.901+00:00"},"s":"I",  "c":"NETWORK",  "id":22944,   "ctx":"conn2","msg":"Connection ended","attr":{"remote":"127.0.0.1:48526","uuid":"90a0bf11-2062-40f9-b874-5a65fab56223","connectionId":2,"connectionCount":2}}
{"t":{"$date":"2024-04-09T02:26:02.903+00:00"},"s":"I",  "c":"REPL",     "id":6015317, "ctx":"ReplCoord-0","msg":"Setting new configuration state","attr":{"newState":"ConfigSteady","oldState":"ConfigStartingUp"}}
{"t":{"$date":"2024-04-09T02:26:02.904+00:00"},"s":"I",  "c":"REPL",     "id":21392,   "ctx":"ReplCoord-0","msg":"New replica set config in use","attr":{"config":{"_id":"mr1","version":1,"term":1,"members":[{"_id":0,"host":"localhost:27017","arbiterOnly":false,"buildIndexes":true,"hidden":false,"priority":1,"tags":{},"secondaryDelaySecs":0,"votes":1}],"protocolVersion":1,"writeConcernMajorityJournalDefault":true,"settings":{"chainingAllowed":true,"heartbeatIntervalMillis":2000,"heartbeatTimeoutSecs":10,"electionTimeoutMillis":10000,"catchUpTimeoutMillis":-1,"catchUpTakeoverDelayMillis":30000,"getLastErrorModes":{},"getLastErrorDefaults":{"w":1,"wtimeout":0},"replicaSetId":{"$oid":"6614a714bc5de5bfe3a48011"}}}}}
{"t":{"$date":"2024-04-09T02:26:02.908+00:00"},"s":"I",  "c":"REPL",     "id":21393,   "ctx":"ReplCoord-0","msg":"Found self in config","attr":{"hostAndPort":"localhost:27017"}}
{"t":{"$date":"2024-04-09T02:26:02.911+00:00"},"s":"I",  "c":"REPL",     "id":21358,   "ctx":"ReplCoord-0","msg":"Replica set state transition","attr":{"newState":"STARTUP2","oldState":"STARTUP"}}
{"t":{"$date":"2024-04-09T02:26:02.912+00:00"},"s":"I",  "c":"REPL",     "id":21320,   "ctx":"ReplCoord-0","msg":"Updated term","attr":{"term":1}}
{"t":{"$date":"2024-04-09T02:26:02.913+00:00"},"s":"I",  "c":"REPL",     "id":21306,   "ctx":"ReplCoord-0","msg":"Starting replication storage threads"}
{"t":{"$date":"2024-04-09T02:26:02.914+00:00"},"s":"I",  "c":"REPL",     "id":4280512, "ctx":"ReplCoord-0","msg":"No initial sync required. Attempting to begin steady replication"}
{"t":{"$date":"2024-04-09T02:26:02.915+00:00"},"s":"I",  "c":"REPL",     "id":21358,   "ctx":"ReplCoord-0","msg":"Replica set state transition","attr":{"newState":"RECOVERING","oldState":"STARTUP2"}}
{"t":{"$date":"2024-04-09T02:26:02.917+00:00"},"s":"I",  "c":"NETWORK",  "id":22943,   "ctx":"listener","msg":"Connection accepted","attr":{"remote":"127.0.0.1:48552","uuid":"bbe01255-b777-4124-b046-cfd5dbf44c0e","connectionId":5,"connectionCount":3}}
{"t":{"$date":"2024-04-09T02:26:02.919+00:00"},"s":"I",  "c":"NETWORK",  "id":22943,   "ctx":"listener","msg":"Connection accepted","attr":{"remote":"127.0.0.1:48562","uuid":"08a0c59b-07ce-49ac-97de-905d5da0e660","connectionId":6,"connectionCount":4}}
{"t":{"$date":"2024-04-09T02:26:02.925+00:00"},"s":"I",  "c":"REPL",     "id":21299,   "ctx":"ReplCoord-0","msg":"Starting replication fetcher thread"}
{"t":{"$date":"2024-04-09T02:26:02.926+00:00"},"s":"I",  "c":"REPL",     "id":21300,   "ctx":"ReplCoord-0","msg":"Starting replication applier thread"}
{"t":{"$date":"2024-04-09T02:26:02.927+00:00"},"s":"I",  "c":"REPL",     "id":21301,   "ctx":"ReplCoord-0","msg":"Starting replication reporter thread"}
{"t":{"$date":"2024-04-09T02:26:02.928+00:00"},"s":"I",  "c":"REPL",     "id":21224,   "ctx":"OplogApplier-0","msg":"Starting oplog application"}
{"t":{"$date":"2024-04-09T02:26:02.929+00:00"},"s":"I",  "c":"REPL",     "id":4280511, "ctx":"ReplCoord-0","msg":"Set local replica set config"}
{"t":{"$date":"2024-04-09T02:26:02.930+00:00"},"s":"I",  "c":"REPL",     "id":21358,   "ctx":"OplogApplier-0","msg":"Replica set state transition","attr":{"newState":"SECONDARY","oldState":"RECOVERING"}}
{"t":{"$date":"2024-04-09T02:26:02.931+00:00"},"s":"I",  "c":"ELECTION", "id":4615652, "ctx":"OplogApplier-0","msg":"Starting an election, since we've seen no PRIMARY in election timeout period","attr":{"electionTimeoutPeriodMillis":10000}}
{"t":{"$date":"2024-04-09T02:26:02.932+00:00"},"s":"I",  "c":"ELECTION", "id":21438,   "ctx":"OplogApplier-0","msg":"Conducting a dry run election to see if we could be elected","attr":{"currentTerm":1}}
{"t":{"$date":"2024-04-09T02:26:02.934+00:00"},"s":"I",  "c":"ELECTION", "id":21444,   "ctx":"ReplCoord-0","msg":"Dry election run succeeded, running for election","attr":{"newTerm":2}}
{"t":{"$date":"2024-04-09T02:26:02.935+00:00"},"s":"I",  "c":"ELECTION", "id":6015300, "ctx":"ReplCoord-0","msg":"Storing last vote document in local storage for my election","attr":{"lastVote":{"term":2,"candidateIndex":0}}}
{"t":{"$date":"2024-04-09T02:26:02.938+00:00"},"s":"I",  "c":"ELECTION", "id":21450,   "ctx":"ReplCoord-0","msg":"Election succeeded, assuming primary role","attr":{"term":2}}
{"t":{"$date":"2024-04-09T02:26:02.939+00:00"},"s":"I",  "c":"REPL",     "id":21358,   "ctx":"ReplCoord-0","msg":"Replica set state transition","attr":{"newState":"PRIMARY","oldState":"SECONDARY"}}
{"t":{"$date":"2024-04-09T02:26:02.940+00:00"},"s":"I",  "c":"REPL",     "id":21106,   "ctx":"ReplCoord-0","msg":"Resetting sync source to empty","attr":{"previousSyncSource":":27017"}}
{"t":{"$date":"2024-04-09T02:26:02.941+00:00"},"s":"I",  "c":"REPL",     "id":21359,   "ctx":"ReplCoord-0","msg":"Entering primary catch-up mode"}
{"t":{"$date":"2024-04-09T02:26:02.942+00:00"},"s":"I",  "c":"REPL",     "id":6015304, "ctx":"ReplCoord-0","msg":"Skipping primary catchup since we are the only node in the replica set."}
{"t":{"$date":"2024-04-09T02:26:02.943+00:00"},"s":"I",  "c":"REPL",     "id":21363,   "ctx":"ReplCoord-0","msg":"Exited primary catch-up mode"}
{"t":{"$date":"2024-04-09T02:26:02.944+00:00"},"s":"I",  "c":"REPL",     "id":21107,   "ctx":"ReplCoord-0","msg":"Stopping replication producer"}
{"t":{"$date":"2024-04-09T02:26:02.945+00:00"},"s":"I",  "c":"REPL",     "id":21239,   "ctx":"ReplBatcher","msg":"Oplog buffer has been drained","attr":{"term":2}}
{"t":{"$date":"2024-04-09T02:26:02.946+00:00"},"s":"I",  "c":"NETWORK",  "id":51800,   "ctx":"conn5","msg":"client metadata","attr":{"remote":"127.0.0.1:48552","client":"conn5","negotiatedCompressors":[],"doc":{"application":{"name":"mongosh 2.2.2"},"driver":{"name":"nodejs|mongosh","version":"6.5.0|2.2.2"},"platform":"Node.js v20.11.1, LE","os":{"name":"linux","architecture":"x64","version":"3.10.0-327.22.2.el7.x86_64","type":"Linux"},"env":{"container":{"runtime":"docker"}}}}}
{"t":{"$date":"2024-04-09T02:26:02.947+00:00"},"s":"I",  "c":"REPL",     "id":21239,   "ctx":"ReplBatcher","msg":"Oplog buffer has been drained","attr":{"term":2}}
{"t":{"$date":"2024-04-09T02:26:02.947+00:00"},"s":"I",  "c":"NETWORK",  "id":51800,   "ctx":"conn6","msg":"client metadata","attr":{"remote":"127.0.0.1:48562","client":"conn6","negotiatedCompressors":[],"doc":{"application":{"name":"mongosh 2.2.2"},"driver":{"name":"nodejs|mongosh","version":"6.5.0|2.2.2"},"platform":"Node.js v20.11.1, LE","os":{"name":"linux","architecture":"x64","version":"3.10.0-327.22.2.el7.x86_64","type":"Linux"},"env":{"container":{"runtime":"docker"}}}}}
{"t":{"$date":"2024-04-09T02:26:02.955+00:00"},"s":"I",  "c":"ACCESS",   "id":20250,   "ctx":"conn5","msg":"Authentication succeeded","attr":{"mechanism":"SCRAM-SHA-256","speculative":true,"principalName":"appsmith","authenticationDatabase":"appsmith","remote":"127.0.0.1:48552","extraInfo":{}}}
{"t":{"$date":"2024-04-09T02:26:02.956+00:00"},"s":"I",  "c":"REPL",     "id":21343,   "ctx":"RstlKillOpThread","msg":"Starting to kill user operations"}
{"t":{"$date":"2024-04-09T02:26:02.958+00:00"},"s":"I",  "c":"REPL",     "id":21344,   "ctx":"RstlKillOpThread","msg":"Stopped killing user operations"}
{"t":{"$date":"2024-04-09T02:26:02.959+00:00"},"s":"I",  "c":"REPL",     "id":21340,   "ctx":"RstlKillOpThread","msg":"State transition ops metrics","attr":{"metrics":{"lastStateTransition":"stepUp","userOpsKilled":0,"userOpsRunning":0}}}
{"t":{"$date":"2024-04-09T02:26:02.960+00:00"},"s":"I",  "c":"REPL",     "id":4508103, "ctx":"OplogApplier-0","msg":"Increment the config term via reconfig"}
{"t":{"$date":"2024-04-09T02:26:02.961+00:00"},"s":"I",  "c":"REPL",     "id":6015313, "ctx":"OplogApplier-0","msg":"Replication config state is Steady, starting reconfig"}
{"t":{"$date":"2024-04-09T02:26:02.961+00:00"},"s":"I",  "c":"REPL",     "id":6015317, "ctx":"OplogApplier-0","msg":"Setting new configuration state","attr":{"newState":"ConfigReconfiguring","oldState":"ConfigSteady"}}
{"t":{"$date":"2024-04-09T02:26:02.962+00:00"},"s":"I",  "c":"REPL",     "id":21353,   "ctx":"OplogApplier-0","msg":"replSetReconfig config object parses ok","attr":{"numMembers":1}}
{"t":{"$date":"2024-04-09T02:26:02.963+00:00"},"s":"I",  "c":"REPL",     "id":51814,   "ctx":"OplogApplier-0","msg":"Persisting new config to disk"}
{"t":{"$date":"2024-04-09T02:26:02.964+00:00"},"s":"I",  "c":"ACCESS",   "id":20250,   "ctx":"conn6","msg":"Authentication succeeded","attr":{"mechanism":"SCRAM-SHA-256","speculative":true,"principalName":"appsmith","authenticationDatabase":"appsmith","remote":"127.0.0.1:48562","extraInfo":{}}}
{"t":{"$date":"2024-04-09T02:26:02.966+00:00"},"s":"I",  "c":"REPL",     "id":6015315, "ctx":"OplogApplier-0","msg":"Persisted new config to disk"}
{"t":{"$date":"2024-04-09T02:26:02.970+00:00"},"s":"I",  "c":"REPL",     "id":6015317, "ctx":"OplogApplier-0","msg":"Setting new configuration state","attr":{"newState":"ConfigSteady","oldState":"ConfigReconfiguring"}}
{"t":{"$date":"2024-04-09T02:26:02.974+00:00"},"s":"I",  "c":"REPL",     "id":21392,   "ctx":"OplogApplier-0","msg":"New replica set config in use","attr":{"config":{"_id":"mr1","version":1,"term":2,"members":[{"_id":0,"host":"localhost:27017","arbiterOnly":false,"buildIndexes":true,"hidden":false,"priority":1,"tags":{},"secondaryDelaySecs":0,"votes":1}],"protocolVersion":1,"writeConcernMajorityJournalDefault":true,"settings":{"chainingAllowed":true,"heartbeatIntervalMillis":2000,"heartbeatTimeoutSecs":10,"electionTimeoutMillis":10000,"catchUpTimeoutMillis":-1,"catchUpTakeoverDelayMillis":30000,"getLastErrorModes":{},"getLastErrorDefaults":{"w":1,"wtimeout":0},"replicaSetId":{"$oid":"6614a714bc5de5bfe3a48011"}}}}}
{"t":{"$date":"2024-04-09T02:26:02.975+00:00"},"s":"I",  "c":"REPL",     "id":21393,   "ctx":"OplogApplier-0","msg":"Found self in config","attr":{"hostAndPort":"localhost:27017"}}
{"t":{"$date":"2024-04-09T02:26:02.978+00:00"},"s":"I",  "c":"REPL",     "id":6015310, "ctx":"OplogApplier-0","msg":"Starting to transition to primary."}
{"t":{"$date":"2024-04-09T02:26:02.985+00:00"},"s":"I",  "c":"REPL",     "id":6015309, "ctx":"OplogApplier-0","msg":"Logging transition to primary to oplog on stepup"}
{"t":{"$date":"2024-04-09T02:26:02.987+00:00"},"s":"I",  "c":"STORAGE",  "id":20657,   "ctx":"OplogApplier-0","msg":"IndexBuildsCoordinator::onStepUp - this node is stepping up to primary"}
{"t":{"$date":"2024-04-09T02:26:02.995+00:00"},"s":"I",  "c":"REPL",     "id":21331,   "ctx":"OplogApplier-0","msg":"Transition to primary complete; database writes are now permitted"}
{"t":{"$date":"2024-04-09T02:26:02.997+00:00"},"s":"I",  "c":"REPL",     "id":6015306, "ctx":"OplogApplier-0","msg":"Applier already left draining state, exiting."}
{"t":{"$date":"2024-04-09T02:26:03.008+00:00"},"s":"I",  "c":"STORAGE",  "id":20320,   "ctx":"TenantMigrationRecipientService-0","msg":"createCollection","attr":{"namespace":"config.tenantMigrationRecipients","uuidDisposition":"generated","uuid":{"uuid":{"$uuid":"4efdc86f-55e6-4259-a9d3-1808bb6c02b1"}},"options":{}}}
{"t":{"$date":"2024-04-09T02:26:03.012+00:00"},"s":"I",  "c":"STORAGE",  "id":20320,   "ctx":"TenantMigrationDonorService-0","msg":"createCollection","attr":{"namespace":"config.tenantMigrationDonors","uuidDisposition":"generated","uuid":{"uuid":{"$uuid":"a0b98ff3-d7c8-408c-84fa-bc4d9478ef27"}},"options":{}}}
{"t":{"$date":"2024-04-09T02:26:03.094+00:00"},"s":"I",  "c":"INDEX",    "id":20345,   "ctx":"TenantMigrationRecipientService-0","msg":"Index build: done building","attr":{"buildUUID":null,"namespace":"config.tenantMigrationRecipients","index":"_id_","commitTimestamp":{"$timestamp":{"t":1712629563,"i":3}}}}
{"t":{"$date":"2024-04-09T02:26:03.095+00:00"},"s":"I",  "c":"INDEX",    "id":20345,   "ctx":"TenantMigrationRecipientService-0","msg":"Index build: done building","attr":{"buildUUID":null,"namespace":"config.tenantMigrationRecipients","index":"TenantMigrationRecipientTTLIndex","commitTimestamp":{"$timestamp":{"t":1712629563,"i":3}}}}
{"t":{"$date":"2024-04-09T02:26:03.102+00:00"},"s":"I",  "c":"REPL",     "id":5123005, "ctx":"TenantMigrationRecipientService-0","msg":"Rebuilding PrimaryOnlyService due to stepUp","attr":{"service":"TenantMigrationRecipientService"}}
{"t":{"$date":"2024-04-09T02:26:03.108+00:00"},"s":"I",  "c":"INDEX",    "id":20345,   "ctx":"TenantMigrationDonorService-0","msg":"Index build: done building","attr":{"buildUUID":null,"namespace":"config.tenantMigrationDonors","index":"_id_","commitTimestamp":{"$timestamp":{"t":1712629563,"i":4}}}}
{"t":{"$date":"2024-04-09T02:26:03.109+00:00"},"s":"I",  "c":"INDEX",    "id":20345,   "ctx":"TenantMigrationDonorService-0","msg":"Index build: done building","attr":{"buildUUID":null,"namespace":"config.tenantMigrationDonors","index":"TenantMigrationDonorTTLIndex","commitTimestamp":{"$timestamp":{"t":1712629563,"i":4}}}}
{"t":{"$date":"2024-04-09T02:26:03.111+00:00"},"s":"I",  "c":"STORAGE",  "id":20320,   "ctx":"TenantMigrationDonorService-1","msg":"createCollection","attr":{"namespace":"config.external_validation_keys","uuidDisposition":"generated","uuid":{"uuid":{"$uuid":"13ee9122-f72e-41a3-b53e-f7c44eef74e9"}},"options":{}}}
{"t":{"$date":"2024-04-09T02:26:03.158+00:00"},"s":"I",  "c":"INDEX",    "id":20345,   "ctx":"TenantMigrationDonorService-1","msg":"Index build: done building","attr":{"buildUUID":null,"namespace":"config.external_validation_keys","index":"_id_","commitTimestamp":{"$timestamp":{"t":1712629563,"i":6}}}}
{"t":{"$date":"2024-04-09T02:26:03.159+00:00"},"s":"I",  "c":"INDEX",    "id":20345,   "ctx":"TenantMigrationDonorService-1","msg":"Index build: done building","attr":{"buildUUID":null,"namespace":"config.external_validation_keys","index":"ExternalKeysTTLIndex","commitTimestamp":{"$timestamp":{"t":1712629563,"i":6}}}}
{"t":{"$date":"2024-04-09T02:26:03.160+00:00"},"s":"I",  "c":"REPL",     "id":5123005, "ctx":"TenantMigrationDonorService-0","msg":"Rebuilding PrimaryOnlyService due to stepUp","attr":{"service":"TenantMigrationDonorService"}}
{"t":{"$date":"2024-04-09T02:26:03.338+00:00"},"s":"I",  "c":"NETWORK",  "id":22944,   "ctx":"conn6","msg":"Connection ended","attr":{"remote":"127.0.0.1:48562","uuid":"08a0c59b-07ce-49ac-97de-905d5da0e660","connectionId":6,"connectionCount":3}}
{"t":{"$date":"2024-04-09T02:26:03.338+00:00"},"s":"I",  "c":"NETWORK",  "id":22944,   "ctx":"conn3","msg":"Connection ended","attr":{"remote":"127.0.0.1:48534","uuid":"c2febaea-7660-4a8f-8703-c419e09d0bce","connectionId":3,"connectionCount":2}}
{"t":{"$date":"2024-04-09T02:26:03.339+00:00"},"s":"I",  "c":"NETWORK",  "id":22944,   "ctx":"conn4","msg":"Connection ended","attr":{"remote":"127.0.0.1:48538","uuid":"b51dc5e7-0860-4d10-a840-f0704b9295ef","connectionId":4,"connectionCount":1}}
{"t":{"$date":"2024-04-09T02:26:03.339+00:00"},"s":"I",  "c":"NETWORK",  "id":22944,   "ctx":"conn5","msg":"Connection ended","attr":{"remote":"127.0.0.1:48552","uuid":"bbe01255-b777-4124-b046-cfd5dbf44c0e","connectionId":5,"connectionCount":0}}
{"t":{"$date":"2024-04-09T02:26:10.605+00:00"},"s":"I",  "c":"NETWORK",  "id":22943,   "ctx":"listener","msg":"Connection accepted","attr":{"remote":"127.0.0.1:33032","uuid":"8cef55fd-01ea-49f0-9227-3b9fc41f9b98","connectionId":7,"connectionCount":1}}
{"t":{"$date":"2024-04-09T02:26:10.607+00:00"},"s":"I",  "c":"NETWORK",  "id":22943,   "ctx":"listener","msg":"Connection accepted","attr":{"remote":"127.0.0.1:33034","uuid":"b0317491-5340-4ad9-b287-eb013249a4dd","connectionId":8,"connectionCount":2}}
{"t":{"$date":"2024-04-09T02:26:10.666+00:00"},"s":"I",  "c":"NETWORK",  "id":51800,   "ctx":"conn7","msg":"client metadata","attr":{"remote":"127.0.0.1:33032","client":"conn7","negotiatedCompressors":[],"doc":{"driver":{"name":"mongo-java-driver|reactive-streams|spring-boot","version":"4.8.2"},"os":{"type":"Linux","name":"Linux","architecture":"amd64","version":"5.15.49-linuxkit"},"platform":"Java/Eclipse Adoptium/17.0.9+9"}}}
{"t":{"$date":"2024-04-09T02:26:10.668+00:00"},"s":"I",  "c":"NETWORK",  "id":51800,   "ctx":"conn8","msg":"client metadata","attr":{"remote":"127.0.0.1:33034","client":"conn8","negotiatedCompressors":[],"doc":{"driver":{"name":"mongo-java-driver|reactive-streams|spring-boot","version":"4.8.2"},"os":{"type":"Linux","name":"Linux","architecture":"amd64","version":"5.15.49-linuxkit"},"platform":"Java/Eclipse Adoptium/17.0.9+9"}}}
{"t":{"$date":"2024-04-09T02:26:14.370+00:00"},"s":"I",  "c":"NETWORK",  "id":22943,   "ctx":"listener","msg":"Connection accepted","attr":{"remote":"127.0.0.1:33050","uuid":"e8ebaea7-d809-4a42-9199-fe038cc65374","connectionId":9,"connectionCount":3}}
{"t":{"$date":"2024-04-09T02:26:14.388+00:00"},"s":"I",  "c":"NETWORK",  "id":22943,   "ctx":"listener","msg":"Connection accepted","attr":{"remote":"127.0.0.1:33064","uuid":"9c61b2d7-de1a-4395-9327-31ce98642164","connectionId":10,"connectionCount":4}}
{"t":{"$date":"2024-04-09T02:26:14.395+00:00"},"s":"I",  "c":"NETWORK",  "id":51800,   "ctx":"conn9","msg":"client metadata","attr":{"remote":"127.0.0.1:33050","client":"conn9","negotiatedCompressors":[],"doc":{"driver":{"name":"mongo-java-driver|sync|spring-boot","version":"4.8.2"},"os":{"type":"Linux","name":"Linux","architecture":"amd64","version":"5.15.49-linuxkit"},"platform":"Java/Eclipse Adoptium/17.0.9+9"}}}
{"t":{"$date":"2024-04-09T02:26:14.395+00:00"},"s":"I",  "c":"NETWORK",  "id":51800,   "ctx":"conn10","msg":"client metadata","attr":{"remote":"127.0.0.1:33064","client":"conn10","negotiatedCompressors":[],"doc":{"driver":{"name":"mongo-java-driver|sync|spring-boot","version":"4.8.2"},"os":{"type":"Linux","name":"Linux","architecture":"amd64","version":"5.15.49-linuxkit"},"platform":"Java/Eclipse Adoptium/17.0.9+9"}}}
{"t":{"$date":"2024-04-09T02:26:21.285+00:00"},"s":"I",  "c":"NETWORK",  "id":22943,   "ctx":"listener","msg":"Connection accepted","attr":{"remote":"127.0.0.1:60858","uuid":"5b6b6b16-0715-4612-b326-a88d9d3a4e40","connectionId":11,"connectionCount":5}}
{"t":{"$date":"2024-04-09T02:26:21.296+00:00"},"s":"I",  "c":"NETWORK",  "id":51800,   "ctx":"conn11","msg":"client metadata","attr":{"remote":"127.0.0.1:60858","client":"conn11","negotiatedCompressors":[],"doc":{"driver":{"name":"mongo-java-driver|sync|spring-boot","version":"4.8.2"},"os":{"type":"Linux","name":"Linux","architecture":"amd64","version":"5.15.49-linuxkit"},"platform":"Java/Eclipse Adoptium/17.0.9+9"}}}
{"t":{"$date":"2024-04-09T02:26:21.438+00:00"},"s":"I",  "c":"ACCESS",   "id":20250,   "ctx":"conn11","msg":"Authentication succeeded","attr":{"mechanism":"SCRAM-SHA-256","speculative":true,"principalName":"appsmith","authenticationDatabase":"appsmith","remote":"127.0.0.1:60858","extraInfo":{}}}
{"t":{"$date":"2024-04-09T02:26:21.503+00:00"},"s":"I",  "c":"STORAGE",  "id":20320,   "ctx":"conn11","msg":"createCollection","attr":{"namespace":"appsmith.mongockLock","uuidDisposition":"generated","uuid":{"uuid":{"$uuid":"3da5c632-8711-4110-8be2-14ecad03317f"}},"options":{}}}
{"t":{"$date":"2024-04-09T02:26:21.534+00:00"},"s":"I",  "c":"INDEX",    "id":20345,   "ctx":"conn11","msg":"Index build: done building","attr":{"buildUUID":null,"namespace":"appsmith.mongockLock","index":"_id_","commitTimestamp":{"$timestamp":{"t":1712629581,"i":2}}}}
{"t":{"$date":"2024-04-09T02:26:21.535+00:00"},"s":"I",  "c":"INDEX",    "id":20345,   "ctx":"conn11","msg":"Index build: done building","attr":{"buildUUID":null,"namespace":"appsmith.mongockLock","index":"key_1","commitTimestamp":{"$timestamp":{"t":1712629581,"i":2}}}}
{"t":{"$date":"2024-04-09T02:26:21.605+00:00"},"s":"I",  "c":"STORAGE",  "id":20320,   "ctx":"conn11","msg":"createCollection","attr":{"namespace":"appsmith.mongockChangeLog","uuidDisposition":"generated","uuid":{"uuid":{"$uuid":"b114ae48-af12-4bb7-b1de-5ec9de6586d1"}},"options":{}}}
{"t":{"$date":"2024-04-09T02:26:21.623+00:00"},"s":"I",  "c":"INDEX",    "id":20345,   "ctx":"conn11","msg":"Index build: done building","attr":{"buildUUID":null,"namespace":"appsmith.mongockChangeLog","index":"_id_","commitTimestamp":{"$timestamp":{"t":1712629581,"i":4}}}}
{"t":{"$date":"2024-04-09T02:26:21.623+00:00"},"s":"I",  "c":"INDEX",    "id":20345,   "ctx":"conn11","msg":"Index build: done building","attr":{"buildUUID":null,"namespace":"appsmith.mongockChangeLog","index":"executionId_1_author_1_changeId_1","commitTimestamp":{"$timestamp":{"t":1712629581,"i":4}}}}
{"t":{"$date":"2024-04-09T02:26:21.875+00:00"},"s":"I",  "c":"STORAGE",  "id":20320,   "ctx":"conn11","msg":"createCollection","attr":{"namespace":"appsmith.config","uuidDisposition":"generated","uuid":{"uuid":{"$uuid":"3d310cfe-c234-49ee-bdd4-8c079cf74be4"}},"options":{}}}
{"t":{"$date":"2024-04-09T02:26:21.898+00:00"},"s":"I",  "c":"INDEX",    "id":20345,   "ctx":"conn11","msg":"Index build: done building","attr":{"buildUUID":null,"namespace":"appsmith.config","index":"_id_","commitTimestamp":{"$timestamp":{"t":1712629581,"i":7}}}}
{"t":{"$date":"2024-04-09T02:26:21.947+00:00"},"s":"I",  "c":"STORAGE",  "id":20320,   "ctx":"conn11","msg":"createCollection","attr":{"namespace":"appsmith.plugin","uuidDisposition":"generated","uuid":{"uuid":{"$uuid":"049d5237-455e-4e70-ac62-8358d4b8a31e"}},"options":{}}}
{"t":{"$date":"2024-04-09T02:26:21.976+00:00"},"s":"I",  "c":"INDEX",    "id":20345,   "ctx":"conn11","msg":"Index build: done building","attr":{"buildUUID":null,"namespace":"appsmith.plugin","index":"_id_","commitTimestamp":{"$timestamp":{"t":1712629581,"i":10}}}}
{"t":{"$date":"2024-04-09T02:26:22.034+00:00"},"s":"I",  "c":"STORAGE",  "id":20320,   "ctx":"conn11","msg":"createCollection","attr":{"namespace":"appsmith.application","uuidDisposition":"generated","uuid":{"uuid":{"$uuid":"ff48fded-c9f9-4807-a12d-a235fe9711f4"}},"options":{}}}
{"t":{"$date":"2024-04-09T02:26:22.071+00:00"},"s":"I",  "c":"INDEX",    "id":20345,   "ctx":"conn11","msg":"Index build: done building","attr":{"buildUUID":null,"namespace":"appsmith.application","index":"_id_","commitTimestamp":{"$timestamp":{"t":1712629582,"i":3}}}}
{"t":{"$date":"2024-04-09T02:26:22.072+00:00"},"s":"I",  "c":"INDEX",    "id":20345,   "ctx":"conn11","msg":"Index build: done building","attr":{"buildUUID":null,"namespace":"appsmith.application","index":"createdAt","commitTimestamp":{"$timestamp":{"t":1712629582,"i":3}}}}
{"t":{"$date":"2024-04-09T02:26:22.081+00:00"},"s":"I",  "c":"STORAGE",  "id":20320,   "ctx":"conn11","msg":"createCollection","attr":{"namespace":"appsmith.collection","uuidDisposition":"generated","uuid":{"uuid":{"$uuid":"63ea5a87-8b64-4217-9de0-7133189dfcfc"}},"options":{}}}
{"t":{"$date":"2024-04-09T02:26:22.111+00:00"},"s":"I",  "c":"INDEX",    "id":20345,   "ctx":"conn11","msg":"Index build: done building","attr":{"buildUUID":null,"namespace":"appsmith.collection","index":"_id_","commitTimestamp":{"$timestamp":{"t":1712629582,"i":5}}}}
{"t":{"$date":"2024-04-09T02:26:22.112+00:00"},"s":"I",  "c":"INDEX",    "id":20345,   "ctx":"conn11","msg":"Index build: done building","attr":{"buildUUID":null,"namespace":"appsmith.collection","index":"createdAt","commitTimestamp":{"$timestamp":{"t":1712629582,"i":5}}}}
{"t":{"$date":"2024-04-09T02:26:22.123+00:00"},"s":"I",  "c":"INDEX",    "id":20438,   "ctx":"conn11","msg":"Index build: registering","attr":{"buildUUID":{"uuid":{"$uuid":"78ff68f6-28a5-4909-a823-ef5777efad4d"}},"namespace":"appsmith.config","collectionUUID":{"uuid":{"$uuid":"3d310cfe-c234-49ee-bdd4-8c079cf74be4"}},"indexes":1,"firstIndex":{"name":"createdAt"}}}
{"t":{"$date":"2024-04-09T02:26:22.161+00:00"},"s":"I",  "c":"INDEX",    "id":20384,   "ctx":"IndexBuildsCoordinatorMongod-0","msg":"Index build: starting","attr":{"buildUUID":{"uuid":{"$uuid":"78ff68f6-28a5-4909-a823-ef5777efad4d"}},"collectionUUID":{"uuid":{"$uuid":"3d310cfe-c234-49ee-bdd4-8c079cf74be4"}},"namespace":"appsmith.config","properties":{"v":2,"key":{"createdAt":1},"name":"createdAt"},"method":"Hybrid","maxTemporaryMemoryUsageMB":200}}
{"t":{"$date":"2024-04-09T02:26:22.162+00:00"},"s":"I",  "c":"INDEX",    "id":20346,   "ctx":"IndexBuildsCoordinatorMongod-0","msg":"Index build: initialized","attr":{"buildUUID":{"uuid":{"$uuid":"78ff68f6-28a5-4909-a823-ef5777efad4d"}},"collectionUUID":{"uuid":{"$uuid":"3d310cfe-c234-49ee-bdd4-8c079cf74be4"}},"namespace":"appsmith.config","initializationTimestamp":{"$timestamp":{"t":1712629582,"i":7}}}}
{"t":{"$date":"2024-04-09T02:26:22.164+00:00"},"s":"I",  "c":"INDEX",    "id":20440,   "ctx":"conn11","msg":"Index build: waiting for index build to complete","attr":{"buildUUID":{"uuid":{"$uuid":"78ff68f6-28a5-4909-a823-ef5777efad4d"}},"deadline":{"$date":{"$numberLong":"9223372036854775807"}}}}
{"t":{"$date":"2024-04-09T02:26:22.164+00:00"},"s":"I",  "c":"STORAGE",  "id":4847600, "ctx":"IndexBuildsCoordinatorMongod-0","msg":"Index build: waiting for last optime before interceptors to be majority committed","attr":{"buildUUID":{"uuid":{"$uuid":"78ff68f6-28a5-4909-a823-ef5777efad4d"}},"collectionUUID":{"uuid":{"$uuid":"3d310cfe-c234-49ee-bdd4-8c079cf74be4"}},"deadline":{"$date":"2024-04-09T02:26:32.163Z"},"timeoutMillis":10000,"lastOpTime":{"ts":{"$timestamp":{"t":1712629582,"i":7}},"t":2}}}
{"t":{"$date":"2024-04-09T02:26:22.217+00:00"},"s":"I",  "c":"INDEX",    "id":20391,   "ctx":"IndexBuildsCoordinatorMongod-0","msg":"Index build: collection scan done","attr":{"buildUUID":{"uuid":{"$uuid":"78ff68f6-28a5-4909-a823-ef5777efad4d"}},"collectionUUID":{"uuid":{"$uuid":"3d310cfe-c234-49ee-bdd4-8c079cf74be4"}},"namespace":"appsmith.config","totalRecords":1,"readSource":"kMajorityCommitted","durationMillis":0}}
{"t":{"$date":"2024-04-09T02:26:22.226+00:00"},"s":"I",  "c":"INDEX",    "id":20685,   "ctx":"IndexBuildsCoordinatorMongod-0","msg":"Index build: inserted keys from external sorter into index","attr":{"namespace":"appsmith.config","index":"createdAt","keysInserted":1,"durationMillis":0}}
{"t":{"$date":"2024-04-09T02:26:22.237+00:00"},"s":"I",  "c":"CONNPOOL", "id":22576,   "ctx":"ReplNetwork","msg":"Connecting","attr":{"hostAndPort":"localhost:27017"}}
{"t":{"$date":"2024-04-09T02:26:22.240+00:00"},"s":"I",  "c":"NETWORK",  "id":22943,   "ctx":"listener","msg":"Connection accepted","attr":{"remote":"127.0.0.1:60860","uuid":"f19a1896-efcb-457e-9bdc-af9020770d80","connectionId":12,"connectionCount":6}}
{"t":{"$date":"2024-04-09T02:26:22.241+00:00"},"s":"I",  "c":"NETWORK",  "id":51800,   "ctx":"conn12","msg":"client metadata","attr":{"remote":"127.0.0.1:60860","client":"conn12","negotiatedCompressors":["snappy","zstd","zlib"],"doc":{"driver":{"name":"NetworkInterfaceTL","version":"5.0.26"},"os":{"type":"Linux","name":"Ubuntu","architecture":"x86_64","version":"20.04"}}}}
{"t":{"$date":"2024-04-09T02:26:22.245+00:00"},"s":"I",  "c":"ACCESS",   "id":20250,   "ctx":"conn12","msg":"Authentication succeeded","attr":{"mechanism":"SCRAM-SHA-256","speculative":true,"principalName":"__system","authenticationDatabase":"local","remote":"127.0.0.1:60860","extraInfo":{}}}
{"t":{"$date":"2024-04-09T02:26:22.252+00:00"},"s":"I",  "c":"STORAGE",  "id":3856201, "ctx":"conn12","msg":"Index build: commit quorum satisfied","attr":{"indexBuildEntry":{"_id":{"$uuid":"78ff68f6-28a5-4909-a823-ef5777efad4d"},"collectionUUID":{"$uuid":"3d310cfe-c234-49ee-bdd4-8c079cf74be4"},"commitQuorum":"votingMembers","indexNames":["createdAt"],"commitReadyMembers":["localhost:27017"]}}}
{"t":{"$date":"2024-04-09T02:26:22.254+00:00"},"s":"I",  "c":"CONNPOOL", "id":22566,   "ctx":"ReplNetwork","msg":"Ending connection due to bad connection status","attr":{"hostAndPort":"localhost:27017","error":"CallbackCanceled: Callback was canceled","numOpenConns":0}}
{"t":{"$date":"2024-04-09T02:26:22.255+00:00"},"s":"I",  "c":"STORAGE",  "id":3856203, "ctx":"IndexBuildsCoordinatorMongod-0","msg":"Index build: waiting for next action before completing final phase","attr":{"buildUUID":{"uuid":{"$uuid":"78ff68f6-28a5-4909-a823-ef5777efad4d"}}}}
{"t":{"$date":"2024-04-09T02:26:22.259+00:00"},"s":"I",  "c":"STORAGE",  "id":3856204, "ctx":"IndexBuildsCoordinatorMongod-0","msg":"Index build: received signal","attr":{"buildUUID":{"uuid":{"$uuid":"78ff68f6-28a5-4909-a823-ef5777efad4d"}},"action":"Commit quorum Satisfied"}}
{"t":{"$date":"2024-04-09T02:26:22.260+00:00"},"s":"I",  "c":"NETWORK",  "id":22944,   "ctx":"conn12","msg":"Connection ended","attr":{"remote":"127.0.0.1:60860","uuid":"f19a1896-efcb-457e-9bdc-af9020770d80","connectionId":12,"connectionCount":5}}
{"t":{"$date":"2024-04-09T02:26:22.261+00:00"},"s":"I",  "c":"NETWORK",  "id":22943,   "ctx":"listener","msg":"Connection accepted","attr":{"remote":"127.0.0.1:60876","uuid":"b30fe467-43f0-4301-b36d-1232ebd2071a","connectionId":14,"connectionCount":6}}
{"t":{"$date":"2024-04-09T02:26:22.262+00:00"},"s":"I",  "c":"INDEX",    "id":20345,   "ctx":"IndexBuildsCoordinatorMongod-0","msg":"Index build: done building","attr":{"buildUUID":{"uuid":{"$uuid":"78ff68f6-28a5-4909-a823-ef5777efad4d"}},"namespace":"appsmith.config","index":"createdAt","commitTimestamp":{"$timestamp":{"t":1712629582,"i":9}}}}
{"t":{"$date":"2024-04-09T02:26:22.264+00:00"},"s":"I",  "c":"NETWORK",  "id":51800,   "ctx":"conn14","msg":"client metadata","attr":{"remote":"127.0.0.1:60876","client":"conn14","negotiatedCompressors":["snappy","zstd","zlib"],"doc":{"driver":{"name":"NetworkInterfaceTL","version":"5.0.26"},"os":{"type":"Linux","name":"Ubuntu","architecture":"x86_64","version":"20.04"}}}}
{"t":{"$date":"2024-04-09T02:26:22.267+00:00"},"s":"I",  "c":"ACCESS",   "id":20250,   "ctx":"conn14","msg":"Authentication succeeded","attr":{"mechanism":"SCRAM-SHA-256","speculative":true,"principalName":"__system","authenticationDatabase":"local","remote":"127.0.0.1:60876","extraInfo":{}}}
{"t":{"$date":"2024-04-09T02:26:22.272+00:00"},"s":"I",  "c":"STORAGE",  "id":20663,   "ctx":"IndexBuildsCoordinatorMongod-0","msg":"Index build: completed successfully","attr":{"buildUUID":{"uuid":{"$uuid":"78ff68f6-28a5-4909-a823-ef5777efad4d"}},"collectionUUID":{"uuid":{"$uuid":"3d310cfe-c234-49ee-bdd4-8c079cf74be4"}},"namespace":"appsmith.config","indexesBuilt":["createdAt"],"numIndexesBefore":1,"numIndexesAfter":2}}
{"t":{"$date":"2024-04-09T02:26:22.274+00:00"},"s":"I",  "c":"INDEX",    "id":20447,   "ctx":"conn11","msg":"Index build: completed","attr":{"buildUUID":{"uuid":{"$uuid":"78ff68f6-28a5-4909-a823-ef5777efad4d"}}}}
{"t":{"$date":"2024-04-09T02:26:22.277+00:00"},"s":"I",  "c":"COMMAND",  "id":51803,   "ctx":"conn11","msg":"Slow query","attr":{"type":"command","ns":"appsmith.config","command":{"createIndexes":"config","indexes":[{"key":{"createdAt":1},"name":"createdAt"}],"$db":"appsmith","$clusterTime":{"clusterTime":{"$timestamp":{"t":1712629582,"i":5}},"signature":{"hash":{"$binary":{"base64":"eo9g3rxCD7m9WowZaq6fXN4WB68=","subType":"0"}},"keyId":7355687795744047109}},"lsid":{"id":{"$uuid":"6083b346-52d2-4c7c-8d49-0fad1e364fcc"}},"$readPreference":{"mode":"primaryPreferred"}},"numYields":0,"reslen":271,"locks":{"ParallelBatchWriterMode":{"acquireCount":{"r":3}},"FeatureCompatibilityVersion":{"acquireCount":{"r":4,"w":2}},"ReplicationStateTransition":{"acquireCount":{"w":6}},"Global":{"acquireCount":{"r":4,"w":2}},"Database":{"acquireCount":{"r":2,"w":1}},"Collection":{"acquireCount":{"r":2,"W":1}},"Mutex":{"acquireCount":{"r":3}}},"flowControl":{"acquireCount":2,"timeAcquiringMicros":3},"readConcern":{"level":"local","provenance":"implicitDefault"},"writeConcern":{"w":"majority","wtimeout":0,"provenance":"implicitDefault"},"waitForWriteConcernDurationMillis":2,"storage":{},"remote":"127.0.0.1:60858","protocol":"op_msg","durationMillis":154}}
{"t":{"$date":"2024-04-09T02:26:22.283+00:00"},"s":"I",  "c":"INDEX",    "id":20438,   "ctx":"conn11","msg":"Index build: registering","attr":{"buildUUID":{"uuid":{"$uuid":"676d66b8-7387-4f38-bbe2-5fadb5d20e9a"}},"namespace":"appsmith.config","collectionUUID":{"uuid":{"$uuid":"3d310cfe-c234-49ee-bdd4-8c079cf74be4"}},"indexes":1,"firstIndex":{"name":"name"}}}
{"t":{"$date":"2024-04-09T02:26:22.330+00:00"},"s":"I",  "c":"INDEX",    "id":20384,   "ctx":"IndexBuildsCoordinatorMongod-0","msg":"Index build: starting","attr":{"buildUUID":{"uuid":{"$uuid":"676d66b8-7387-4f38-bbe2-5fadb5d20e9a"}},"collectionUUID":{"uuid":{"$uuid":"3d310cfe-c234-49ee-bdd4-8c079cf74be4"}},"namespace":"appsmith.config","properties":{"v":2,"unique":true,"key":{"name":1},"name":"name"},"method":"Hybrid","maxTemporaryMemoryUsageMB":200}}
{"t":{"$date":"2024-04-09T02:26:22.331+00:00"},"s":"I",  "c":"INDEX",    "id":20346,   "ctx":"IndexBuildsCoordinatorMongod-0","msg":"Index build: initialized","attr":{"buildUUID":{"uuid":{"$uuid":"676d66b8-7387-4f38-bbe2-5fadb5d20e9a"}},"collectionUUID":{"uuid":{"$uuid":"3d310cfe-c234-49ee-bdd4-8c079cf74be4"}},"namespace":"appsmith.config","initializationTimestamp":{"$timestamp":{"t":1712629582,"i":12}}}}
{"t":{"$date":"2024-04-09T02:26:22.331+00:00"},"s":"I",  "c":"STORAGE",  "id":4847600, "ctx":"IndexBuildsCoordinatorMongod-0","msg":"Index build: waiting for last optime before interceptors to be majority committed","attr":{"buildUUID":{"uuid":{"$uuid":"676d66b8-7387-4f38-bbe2-5fadb5d20e9a"}},"collectionUUID":{"uuid":{"$uuid":"3d310cfe-c234-49ee-bdd4-8c079cf74be4"}},"deadline":{"$date":"2024-04-09T02:26:32.331Z"},"timeoutMillis":10000,"lastOpTime":{"ts":{"$timestamp":{"t":1712629582,"i":12}},"t":2}}}
{"t":{"$date":"2024-04-09T02:26:22.331+00:00"},"s":"I",  "c":"INDEX",    "id":20440,   "ctx":"conn11","msg":"Index build: waiting for index build to complete","attr":{"buildUUID":{"uuid":{"$uuid":"676d66b8-7387-4f38-bbe2-5fadb5d20e9a"}},"deadline":{"$date":{"$numberLong":"9223372036854775807"}}}}
{"t":{"$date":"2024-04-09T02:26:22.379+00:00"},"s":"I",  "c":"INDEX",    "id":20391,   "ctx":"IndexBuildsCoordinatorMongod-0","msg":"Index build: collection scan done","attr":{"buildUUID":{"uuid":{"$uuid":"676d66b8-7387-4f38-bbe2-5fadb5d20e9a"}},"collectionUUID":{"uuid":{"$uuid":"3d310cfe-c234-49ee-bdd4-8c079cf74be4"}},"namespace":"appsmith.config","totalRecords":1,"readSource":"kMajorityCommitted","durationMillis":0}}
{"t":{"$date":"2024-04-09T02:26:22.384+00:00"},"s":"I",  "c":"INDEX",    "id":20685,   "ctx":"IndexBuildsCoordinatorMongod-0","msg":"Index build: inserted keys from external sorter into index","attr":{"namespace":"appsmith.config","index":"name","keysInserted":1,"durationMillis":0}}
{"t":{"$date":"2024-04-09T02:26:22.392+00:00"},"s":"I",  "c":"STORAGE",  "id":3856201, "ctx":"conn14","msg":"Index build: commit quorum satisfied","attr":{"indexBuildEntry":{"_id":{"$uuid":"676d66b8-7387-4f38-bbe2-5fadb5d20e9a"},"collectionUUID":{"$uuid":"3d310cfe-c234-49ee-bdd4-8c079cf74be4"},"commitQuorum":"votingMembers","indexNames":["name"],"commitReadyMembers":["localhost:27017"]}}}
{"t":{"$date":"2024-04-09T02:26:22.394+00:00"},"s":"I",  "c":"CONNPOOL", "id":22566,   "ctx":"ReplNetwork","msg":"Ending connection due to bad connection status","attr":{"hostAndPort":"localhost:27017","error":"CallbackCanceled: Callback was canceled","numOpenConns":0}}
{"t":{"$date":"2024-04-09T02:26:22.394+00:00"},"s":"I",  "c":"STORAGE",  "id":3856203, "ctx":"IndexBuildsCoordinatorMongod-0","msg":"Index build: waiting for next action before completing final phase","attr":{"buildUUID":{"uuid":{"$uuid":"676d66b8-7387-4f38-bbe2-5fadb5d20e9a"}}}}
{"t":{"$date":"2024-04-09T02:26:22.395+00:00"},"s":"I",  "c":"STORAGE",  "id":3856204, "ctx":"IndexBuildsCoordinatorMongod-0","msg":"Index build: received signal","attr":{"buildUUID":{"uuid":{"$uuid":"676d66b8-7387-4f38-bbe2-5fadb5d20e9a"}},"action":"Commit quorum Satisfied"}}
{"t":{"$date":"2024-04-09T02:26:22.396+00:00"},"s":"I",  "c":"INDEX",    "id":20345,   "ctx":"IndexBuildsCoordinatorMongod-0","msg":"Index build: done building","attr":{"buildUUID":{"uuid":{"$uuid":"676d66b8-7387-4f38-bbe2-5fadb5d20e9a"}},"namespace":"appsmith.config","index":"name","commitTimestamp":{"$timestamp":{"t":1712629582,"i":14}}}}
{"t":{"$date":"2024-04-09T02:26:22.396+00:00"},"s":"I",  "c":"NETWORK",  "id":22943,   "ctx":"listener","msg":"Connection accepted","attr":{"remote":"127.0.0.1:60878","uuid":"5ecfd149-edaa-4216-b065-fb073e82a170","connectionId":17,"connectionCount":7}}
{"t":{"$date":"2024-04-09T02:26:22.398+00:00"},"s":"I",  "c":"NETWORK",  "id":51800,   "ctx":"conn17","msg":"client metadata","attr":{"remote":"127.0.0.1:60878","client":"conn17","negotiatedCompressors":["snappy","zstd","zlib"],"doc":{"driver":{"name":"NetworkInterfaceTL","version":"5.0.26"},"os":{"type":"Linux","name":"Ubuntu","architecture":"x86_64","version":"20.04"}}}}
{"t":{"$date":"2024-04-09T02:26:22.398+00:00"},"s":"I",  "c":"NETWORK",  "id":22944,   "ctx":"conn14","msg":"Connection ended","attr":{"remote":"127.0.0.1:60876","uuid":"b30fe467-43f0-4301-b36d-1232ebd2071a","connectionId":14,"connectionCount":6}}
{"t":{"$date":"2024-04-09T02:26:22.401+00:00"},"s":"I",  "c":"ACCESS",   "id":20250,   "ctx":"conn17","msg":"Authentication succeeded","attr":{"mechanism":"SCRAM-SHA-256","speculative":true,"principalName":"__system","authenticationDatabase":"local","remote":"127.0.0.1:60878","extraInfo":{}}}
{"t":{"$date":"2024-04-09T02:26:22.402+00:00"},"s":"I",  "c":"STORAGE",  "id":20663,   "ctx":"IndexBuildsCoordinatorMongod-0","msg":"Index build: completed successfully","attr":{"buildUUID":{"uuid":{"$uuid":"676d66b8-7387-4f38-bbe2-5fadb5d20e9a"}},"collectionUUID":{"uuid":{"$uuid":"3d310cfe-c234-49ee-bdd4-8c079cf74be4"}},"namespace":"appsmith.config","indexesBuilt":["name"],"numIndexesBefore":2,"numIndexesAfter":3}}
{"t":{"$date":"2024-04-09T02:26:22.403+00:00"},"s":"I",  "c":"INDEX",    "id":20447,   "ctx":"conn11","msg":"Index build: completed","attr":{"buildUUID":{"uuid":{"$uuid":"676d66b8-7387-4f38-bbe2-5fadb5d20e9a"}}}}
{"t":{"$date":"2024-04-09T02:26:22.406+00:00"},"s":"I",  "c":"COMMAND",  "id":51803,   "ctx":"conn11","msg":"Slow query","attr":{"type":"command","ns":"appsmith.config","command":{"createIndexes":"config","indexes":[{"key":{"name":1},"name":"name","unique":true}],"$db":"appsmith","$clusterTime":{"clusterTime":{"$timestamp":{"t":1712629582,"i":10}},"signature":{"hash":{"$binary":{"base64":"eo9g3rxCD7m9WowZaq6fXN4WB68=","subType":"0"}},"keyId":7355687795744047109}},"lsid":{"id":{"$uuid":"6083b346-52d2-4c7c-8d49-0fad1e364fcc"}},"$readPreference":{"mode":"primaryPreferred"}},"numYields":0,"reslen":271,"locks":{"ParallelBatchWriterMode":{"acquireCount":{"r":3}},"FeatureCompatibilityVersion":{"acquireCount":{"r":4,"w":2}},"ReplicationStateTransition":{"acquireCount":{"w":6}},"Global":{"acquireCount":{"r":4,"w":2}},"Database":{"acquireCount":{"r":2,"w":1}},"Collection":{"acquireCount":{"r":2,"W":1}},"Mutex":{"acquireCount":{"r":3}}},"flowControl":{"acquireCount":2,"timeAcquiringMicros":5},"readConcern":{"level":"local","provenance":"implicitDefault"},"writeConcern":{"w":"majority","wtimeout":0,"provenance":"implicitDefault"},"waitForWriteConcernDurationMillis":2,"storage":{},"remote":"127.0.0.1:60858","protocol":"op_msg","durationMillis":122}}
{"t":{"$date":"2024-04-09T02:26:22.414+00:00"},"s":"I",  "c":"STORAGE",  "id":20320,   "ctx":"conn11","msg":"createCollection","attr":{"namespace":"appsmith.datasource","uuidDisposition":"generated","uuid":{"uuid":{"$uuid":"d2744a10-b6c0-4f57-b427-a25d4aacb1cc"}},"options":{}}}
{"t":{"$date":"2024-04-09T02:26:22.440+00:00"},"s":"I",  "c":"INDEX",    "id":20345,   "ctx":"conn11","msg":"Index build: done building","attr":{"buildUUID":null,"namespace":"appsmith.datasource","index":"_id_","commitTimestamp":{"$timestamp":{"t":1712629582,"i":17}}}}
{"t":{"$date":"2024-04-09T02:26:22.441+00:00"},"s":"I",  "c":"INDEX",    "id":20345,   "ctx":"conn11","msg":"Index build: done building","attr":{"buildUUID":null,"namespace":"appsmith.datasource","index":"createdAt","commitTimestamp":{"$timestamp":{"t":1712629582,"i":17}}}}
{"t":{"$date":"2024-04-09T02:26:22.450+00:00"},"s":"I",  "c":"STORAGE",  "id":20320,   "ctx":"conn11","msg":"createCollection","attr":{"namespace":"appsmith.passwordResetToken","uuidDisposition":"generated","uuid":{"uuid":{"$uuid":"dde13d3b-a6e9-4b93-a805-b85c54fd2fb1"}},"options":{}}}
{"t":{"$date":"2024-04-09T02:26:22.484+00:00"},"s":"I",  "c":"INDEX",    "id":20345,   "ctx":"conn11","msg":"Index build: done building","attr":{"buildUUID":null,"namespace":"appsmith.passwordResetToken","index":"_id_","commitTimestamp":{"$timestamp":{"t":1712629582,"i":19}}}}
{"t":{"$date":"2024-04-09T02:26:22.484+00:00"},"s":"I",  "c":"INDEX",    "id":20345,   "ctx":"conn11","msg":"Index build: done building","attr":{"buildUUID":null,"namespace":"appsmith.passwordResetToken","index":"createdAt","commitTimestamp":{"$timestamp":{"t":1712629582,"i":19}}}}
{"t":{"$date":"2024-04-09T02:26:22.490+00:00"},"s":"I",  "c":"INDEX",    "id":20438,   "ctx":"conn11","msg":"Index build: registering","attr":{"buildUUID":{"uuid":{"$uuid":"f70052e7-4568-4b0e-ac8a-c27477441933"}},"namespace":"appsmith.passwordResetToken","collectionUUID":{"uuid":{"$uuid":"dde13d3b-a6e9-4b93-a805-b85c54fd2fb1"}},"indexes":1,"firstIndex":{"name":"email"}}}
{"t":{"$date":"2024-04-09T02:26:22.499+00:00"},"s":"I",  "c":"INDEX",    "id":20345,   "ctx":"conn11","msg":"Index build: done building","attr":{"buildUUID":null,"namespace":"appsmith.passwordResetToken","index":"email","commitTimestamp":{"$timestamp":{"t":1712629582,"i":20}}}}
{"t":{"$date":"2024-04-09T02:26:22.499+00:00"},"s":"I",  "c":"INDEX",    "id":20440,   "ctx":"conn11","msg":"Index build: waiting for index build to complete","attr":{"buildUUID":{"uuid":{"$uuid":"f70052e7-4568-4b0e-ac8a-c27477441933"}},"deadline":{"$date":{"$numberLong":"9223372036854775807"}}}}
{"t":{"$date":"2024-04-09T02:26:22.500+00:00"},"s":"I",  "c":"INDEX",    "id":20447,   "ctx":"conn11","msg":"Index build: completed","attr":{"buildUUID":{"uuid":{"$uuid":"f70052e7-4568-4b0e-ac8a-c27477441933"}}}}
{"t":{"$date":"2024-04-09T02:26:22.508+00:00"},"s":"I",  "c":"INDEX",    "id":20438,   "ctx":"conn11","msg":"Index build: registering","attr":{"buildUUID":{"uuid":{"$uuid":"4321b241-10c4-4326-9429-43e35c9b9caa"}},"namespace":"appsmith.plugin","collectionUUID":{"uuid":{"$uuid":"049d5237-455e-4e70-ac62-8358d4b8a31e"}},"indexes":1,"firstIndex":{"name":"createdAt"}}}
{"t":{"$date":"2024-04-09T02:26:22.523+00:00"},"s":"I",  "c":"INDEX",    "id":20384,   "ctx":"IndexBuildsCoordinatorMongod-0","msg":"Index build: starting","attr":{"buildUUID":{"uuid":{"$uuid":"4321b241-10c4-4326-9429-43e35c9b9caa"}},"collectionUUID":{"uuid":{"$uuid":"049d5237-455e-4e70-ac62-8358d4b8a31e"}},"namespace":"appsmith.plugin","properties":{"v":2,"key":{"createdAt":1},"name":"createdAt"},"method":"Hybrid","maxTemporaryMemoryUsageMB":200}}
{"t":{"$date":"2024-04-09T02:26:22.524+00:00"},"s":"I",  "c":"INDEX",    "id":20346,   "ctx":"IndexBuildsCoordinatorMongod-0","msg":"Index build: initialized","attr":{"buildUUID":{"uuid":{"$uuid":"4321b241-10c4-4326-9429-43e35c9b9caa"}},"collectionUUID":{"uuid":{"$uuid":"049d5237-455e-4e70-ac62-8358d4b8a31e"}},"namespace":"appsmith.plugin","initializationTimestamp":{"$timestamp":{"t":1712629582,"i":22}}}}
{"t":{"$date":"2024-04-09T02:26:22.525+00:00"},"s":"I",  "c":"STORAGE",  "id":4847600, "ctx":"IndexBuildsCoordinatorMongod-0","msg":"Index build: waiting for last optime before interceptors to be majority committed","attr":{"buildUUID":{"uuid":{"$uuid":"4321b241-10c4-4326-9429-43e35c9b9caa"}},"collectionUUID":{"uuid":{"$uuid":"049d5237-455e-4e70-ac62-8358d4b8a31e"}},"deadline":{"$date":"2024-04-09T02:26:32.525Z"},"timeoutMillis":10000,"lastOpTime":{"ts":{"$timestamp":{"t":1712629582,"i":22}},"t":2}}}
{"t":{"$date":"2024-04-09T02:26:22.525+00:00"},"s":"I",  "c":"INDEX",    "id":20440,   "ctx":"conn11","msg":"Index build: waiting for index build to complete","attr":{"buildUUID":{"uuid":{"$uuid":"4321b241-10c4-4326-9429-43e35c9b9caa"}},"deadline":{"$date":{"$numberLong":"9223372036854775807"}}}}
{"t":{"$date":"2024-04-09T02:26:22.604+00:00"},"s":"I",  "c":"INDEX",    "id":20391,   "ctx":"IndexBuildsCoordinatorMongod-0","msg":"Index build: collection scan done","attr":{"buildUUID":{"uuid":{"$uuid":"4321b241-10c4-4326-9429-43e35c9b9caa"}},"collectionUUID":{"uuid":{"$uuid":"049d5237-455e-4e70-ac62-8358d4b8a31e"}},"namespace":"appsmith.plugin","totalRecords":3,"readSource":"kMajorityCommitted","durationMillis":0}}
{"t":{"$date":"2024-04-09T02:26:22.611+00:00"},"s":"I",  "c":"INDEX",    "id":20685,   "ctx":"IndexBuildsCoordinatorMongod-0","msg":"Index build: inserted keys from external sorter into index","attr":{"namespace":"appsmith.plugin","index":"createdAt","keysInserted":3,"durationMillis":0}}
{"t":{"$date":"2024-04-09T02:26:22.619+00:00"},"s":"I",  "c":"STORAGE",  "id":3856201, "ctx":"conn17","msg":"Index build: commit quorum satisfied","attr":{"indexBuildEntry":{"_id":{"$uuid":"4321b241-10c4-4326-9429-43e35c9b9caa"},"collectionUUID":{"$uuid":"049d5237-455e-4e70-ac62-8358d4b8a31e"},"commitQuorum":"votingMembers","indexNames":["createdAt"],"commitReadyMembers":["localhost:27017"]}}}
{"t":{"$date":"2024-04-09T02:26:22.620+00:00"},"s":"I",  "c":"CONNPOOL", "id":22566,   "ctx":"ReplNetwork","msg":"Ending connection due to bad connection status","attr":{"hostAndPort":"localhost:27017","error":"CallbackCanceled: Callback was canceled","numOpenConns":0}}
{"t":{"$date":"2024-04-09T02:26:22.620+00:00"},"s":"I",  "c":"STORAGE",  "id":3856203, "ctx":"IndexBuildsCoordinatorMongod-0","msg":"Index build: waiting for next action before completing final phase","attr":{"buildUUID":{"uuid":{"$uuid":"4321b241-10c4-4326-9429-43e35c9b9caa"}}}}
{"t":{"$date":"2024-04-09T02:26:22.621+00:00"},"s":"I",  "c":"STORAGE",  "id":3856204, "ctx":"IndexBuildsCoordinatorMongod-0","msg":"Index build: received signal","attr":{"buildUUID":{"uuid":{"$uuid":"4321b241-10c4-4326-9429-43e35c9b9caa"}},"action":"Commit quorum Satisfied"}}
{"t":{"$date":"2024-04-09T02:26:22.622+00:00"},"s":"I",  "c":"NETWORK",  "id":22943,   "ctx":"listener","msg":"Connection accepted","attr":{"remote":"127.0.0.1:60888","uuid":"5ffc6525-43e9-4950-89db-a60b12f3d05f","connectionId":19,"connectionCount":7}}
{"t":{"$date":"2024-04-09T02:26:22.623+00:00"},"s":"I",  "c":"INDEX",    "id":20345,   "ctx":"IndexBuildsCoordinatorMongod-0","msg":"Index build: done building","attr":{"buildUUID":{"uuid":{"$uuid":"4321b241-10c4-4326-9429-43e35c9b9caa"}},"namespace":"appsmith.plugin","index":"createdAt","commitTimestamp":{"$timestamp":{"t":1712629582,"i":24}}}}
{"t":{"$date":"2024-04-09T02:26:22.623+00:00"},"s":"I",  "c":"NETWORK",  "id":22944,   "ctx":"conn17","msg":"Connection ended","attr":{"remote":"127.0.0.1:60878","uuid":"5ecfd149-edaa-4216-b065-fb073e82a170","connectionId":17,"connectionCount":6}}
{"t":{"$date":"2024-04-09T02:26:22.623+00:00"},"s":"I",  "c":"NETWORK",  "id":51800,   "ctx":"conn19","msg":"client metadata","attr":{"remote":"127.0.0.1:60888","client":"conn19","negotiatedCompressors":["snappy","zstd","zlib"],"doc":{"driver":{"name":"NetworkInterfaceTL","version":"5.0.26"},"os":{"type":"Linux","name":"Ubuntu","architecture":"x86_64","version":"20.04"}}}}
{"t":{"$date":"2024-04-09T02:26:22.627+00:00"},"s":"I",  "c":"ACCESS",   "id":20250,   "ctx":"conn19","msg":"Authentication succeeded","attr":{"mechanism":"SCRAM-SHA-256","speculative":true,"principalName":"__system","authenticationDatabase":"local","remote":"127.0.0.1:60888","extraInfo":{}}}
{"t":{"$date":"2024-04-09T02:26:22.628+00:00"},"s":"I",  "c":"STORAGE",  "id":20663,   "ctx":"IndexBuildsCoordinatorMongod-0","msg":"Index build: completed successfully","attr":{"buildUUID":{"uuid":{"$uuid":"4321b241-10c4-4326-9429-43e35c9b9caa"}},"collectionUUID":{"uuid":{"$uuid":"049d5237-455e-4e70-ac62-8358d4b8a31e"}},"namespace":"appsmith.plugin","indexesBuilt":["createdAt"],"numIndexesBefore":1,"numIndexesAfter":2}}
{"t":{"$date":"2024-04-09T02:26:22.629+00:00"},"s":"I",  "c":"INDEX",    "id":20447,   "ctx":"conn11","msg":"Index build: completed","attr":{"buildUUID":{"uuid":{"$uuid":"4321b241-10c4-4326-9429-43e35c9b9caa"}}}}
{"t":{"$date":"2024-04-09T02:26:22.631+00:00"},"s":"I",  "c":"COMMAND",  "id":51803,   "ctx":"conn11","msg":"Slow query","attr":{"type":"command","ns":"appsmith.plugin","command":{"createIndexes":"plugin","indexes":[{"key":{"createdAt":1},"name":"createdAt"}],"$db":"appsmith","$clusterTime":{"clusterTime":{"$timestamp":{"t":1712629582,"i":20}},"signature":{"hash":{"$binary":{"base64":"eo9g3rxCD7m9WowZaq6fXN4WB68=","subType":"0"}},"keyId":7355687795744047109}},"lsid":{"id":{"$uuid":"6083b346-52d2-4c7c-8d49-0fad1e364fcc"}},"$readPreference":{"mode":"primaryPreferred"}},"numYields":0,"reslen":271,"locks":{"ParallelBatchWriterMode":{"acquireCount":{"r":3}},"FeatureCompatibilityVersion":{"acquireCount":{"r":4,"w":2}},"ReplicationStateTransition":{"acquireCount":{"w":6}},"Global":{"acquireCount":{"r":4,"w":2}},"Database":{"acquireCount":{"r":2,"w":1}},"Collection":{"acquireCount":{"r":2,"W":1}},"Mutex":{"acquireCount":{"r":3}}},"flowControl":{"acquireCount":2,"timeAcquiringMicros":3},"readConcern":{"level":"local","provenance":"implicitDefault"},"writeConcern":{"w":"majority","wtimeout":0,"provenance":"implicitDefault"},"storage":{},"remote":"127.0.0.1:60858","protocol":"op_msg","durationMillis":122}}
{"t":{"$date":"2024-04-09T02:26:22.635+00:00"},"s":"I",  "c":"INDEX",    "id":20438,   "ctx":"conn11","msg":"Index build: registering","attr":{"buildUUID":{"uuid":{"$uuid":"510dd3a5-cdd1-4b67-9cdc-b14c8f6296ba"}},"namespace":"appsmith.plugin","collectionUUID":{"uuid":{"$uuid":"049d5237-455e-4e70-ac62-8358d4b8a31e"}},"indexes":1,"firstIndex":{"name":"type"}}}
{"t":{"$date":"2024-04-09T02:26:22.654+00:00"},"s":"I",  "c":"INDEX",    "id":20384,   "ctx":"IndexBuildsCoordinatorMongod-0","msg":"Index build: starting","attr":{"buildUUID":{"uuid":{"$uuid":"510dd3a5-cdd1-4b67-9cdc-b14c8f6296ba"}},"collectionUUID":{"uuid":{"$uuid":"049d5237-455e-4e70-ac62-8358d4b8a31e"}},"namespace":"appsmith.plugin","properties":{"v":2,"key":{"type":1},"name":"type"},"method":"Hybrid","maxTemporaryMemoryUsageMB":200}}
{"t":{"$date":"2024-04-09T02:26:22.655+00:00"},"s":"I",  "c":"INDEX",    "id":20346,   "ctx":"IndexBuildsCoordinatorMongod-0","msg":"Index build: initialized","attr":{"buildUUID":{"uuid":{"$uuid":"510dd3a5-cdd1-4b67-9cdc-b14c8f6296ba"}},"collectionUUID":{"uuid":{"$uuid":"049d5237-455e-4e70-ac62-8358d4b8a31e"}},"namespace":"appsmith.plugin","initializationTimestamp":{"$timestamp":{"t":1712629582,"i":27}}}}
{"t":{"$date":"2024-04-09T02:26:22.655+00:00"},"s":"I",  "c":"INDEX",    "id":20440,   "ctx":"conn11","msg":"Index build: waiting for index build to complete","attr":{"buildUUID":{"uuid":{"$uuid":"510dd3a5-cdd1-4b67-9cdc-b14c8f6296ba"}},"deadline":{"$date":{"$numberLong":"9223372036854775807"}}}}
{"t":{"$date":"2024-04-09T02:26:22.655+00:00"},"s":"I",  "c":"STORAGE",  "id":4847600, "ctx":"IndexBuildsCoordinatorMongod-0","msg":"Index build: waiting for last optime before interceptors to be majority committed","attr":{"buildUUID":{"uuid":{"$uuid":"510dd3a5-cdd1-4b67-9cdc-b14c8f6296ba"}},"collectionUUID":{"uuid":{"$uuid":"049d5237-455e-4e70-ac62-8358d4b8a31e"}},"deadline":{"$date":"2024-04-09T02:26:32.655Z"},"timeoutMillis":10000,"lastOpTime":{"ts":{"$timestamp":{"t":1712629582,"i":27}},"t":2}}}
{"t":{"$date":"2024-04-09T02:26:22.732+00:00"},"s":"I",  "c":"INDEX",    "id":20391,   "ctx":"IndexBuildsCoordinatorMongod-0","msg":"Index build: collection scan done","attr":{"buildUUID":{"uuid":{"$uuid":"510dd3a5-cdd1-4b67-9cdc-b14c8f6296ba"}},"collectionUUID":{"uuid":{"$uuid":"049d5237-455e-4e70-ac62-8358d4b8a31e"}},"namespace":"appsmith.plugin","totalRecords":3,"readSource":"kMajorityCommitted","durationMillis":0}}
{"t":{"$date":"2024-04-09T02:26:22.739+00:00"},"s":"I",  "c":"INDEX",    "id":20685,   "ctx":"IndexBuildsCoordinatorMongod-0","msg":"Index build: inserted keys from external sorter into index","attr":{"namespace":"appsmith.plugin","index":"type","keysInserted":3,"durationMillis":0}}
{"t":{"$date":"2024-04-09T02:26:22.747+00:00"},"s":"I",  "c":"STORAGE",  "id":3856201, "ctx":"conn19","msg":"Index build: commit quorum satisfied","attr":{"indexBuildEntry":{"_id":{"$uuid":"510dd3a5-cdd1-4b67-9cdc-b14c8f6296ba"},"collectionUUID":{"$uuid":"049d5237-455e-4e70-ac62-8358d4b8a31e"},"commitQuorum":"votingMembers","indexNames":["type"],"commitReadyMembers":["localhost:27017"]}}}
{"t":{"$date":"2024-04-09T02:26:22.748+00:00"},"s":"I",  "c":"CONNPOOL", "id":22566,   "ctx":"conn19","msg":"Ending connection due to bad connection status","attr":{"hostAndPort":"localhost:27017","error":"CallbackCanceled: Callback was canceled","numOpenConns":0}}
{"t":{"$date":"2024-04-09T02:26:22.749+00:00"},"s":"I",  "c":"STORAGE",  "id":3856203, "ctx":"IndexBuildsCoordinatorMongod-0","msg":"Index build: waiting for next action before completing final phase","attr":{"buildUUID":{"uuid":{"$uuid":"510dd3a5-cdd1-4b67-9cdc-b14c8f6296ba"}}}}
{"t":{"$date":"2024-04-09T02:26:22.749+00:00"},"s":"I",  "c":"NETWORK",  "id":22943,   "ctx":"listener","msg":"Connection accepted","attr":{"remote":"127.0.0.1:60892","uuid":"241e3f19-2a36-4d93-853f-120cb61e687f","connectionId":21,"connectionCount":7}}
{"t":{"$date":"2024-04-09T02:26:22.750+00:00"},"s":"I",  "c":"STORAGE",  "id":3856204, "ctx":"IndexBuildsCoordinatorMongod-0","msg":"Index build: received signal","attr":{"buildUUID":{"uuid":{"$uuid":"510dd3a5-cdd1-4b67-9cdc-b14c8f6296ba"}},"action":"Commit quorum Satisfied"}}
{"t":{"$date":"2024-04-09T02:26:22.751+00:00"},"s":"I",  "c":"NETWORK",  "id":22944,   "ctx":"conn19","msg":"Connection ended","attr":{"remote":"127.0.0.1:60888","uuid":"5ffc6525-43e9-4950-89db-a60b12f3d05f","connectionId":19,"connectionCount":6}}
{"t":{"$date":"2024-04-09T02:26:22.752+00:00"},"s":"I",  "c":"NETWORK",  "id":51800,   "ctx":"conn21","msg":"client metadata","attr":{"remote":"127.0.0.1:60892","client":"conn21","negotiatedCompressors":["snappy","zstd","zlib"],"doc":{"driver":{"name":"NetworkInterfaceTL","version":"5.0.26"},"os":{"type":"Linux","name":"Ubuntu","architecture":"x86_64","version":"20.04"}}}}
{"t":{"$date":"2024-04-09T02:26:22.752+00:00"},"s":"I",  "c":"INDEX",    "id":20345,   "ctx":"IndexBuildsCoordinatorMongod-0","msg":"Index build: done building","attr":{"buildUUID":{"uuid":{"$uuid":"510dd3a5-cdd1-4b67-9cdc-b14c8f6296ba"}},"namespace":"appsmith.plugin","index":"type","commitTimestamp":{"$timestamp":{"t":1712629582,"i":29}}}}
{"t":{"$date":"2024-04-09T02:26:22.755+00:00"},"s":"I",  "c":"ACCESS",   "id":20250,   "ctx":"conn21","msg":"Authentication succeeded","attr":{"mechanism":"SCRAM-SHA-256","speculative":true,"principalName":"__system","authenticationDatabase":"local","remote":"127.0.0.1:60892","extraInfo":{}}}
{"t":{"$date":"2024-04-09T02:26:22.758+00:00"},"s":"I",  "c":"STORAGE",  "id":20663,   "ctx":"IndexBuildsCoordinatorMongod-0","msg":"Index build: completed successfully","attr":{"buildUUID":{"uuid":{"$uuid":"510dd3a5-cdd1-4b67-9cdc-b14c8f6296ba"}},"collectionUUID":{"uuid":{"$uuid":"049d5237-455e-4e70-ac62-8358d4b8a31e"}},"namespace":"appsmith.plugin","indexesBuilt":["type"],"numIndexesBefore":2,"numIndexesAfter":3}}
{"t":{"$date":"2024-04-09T02:26:22.760+00:00"},"s":"I",  "c":"INDEX",    "id":20447,   "ctx":"conn11","msg":"Index build: completed","attr":{"buildUUID":{"uuid":{"$uuid":"510dd3a5-cdd1-4b67-9cdc-b14c8f6296ba"}}}}
{"t":{"$date":"2024-04-09T02:26:22.763+00:00"},"s":"I",  "c":"COMMAND",  "id":51803,   "ctx":"conn11","msg":"Slow query","attr":{"type":"command","ns":"appsmith.plugin","command":{"createIndexes":"plugin","indexes":[{"key":{"type":1},"name":"type"}],"$db":"appsmith","$clusterTime":{"clusterTime":{"$timestamp":{"t":1712629582,"i":25}},"signature":{"hash":{"$binary":{"base64":"eo9g3rxCD7m9WowZaq6fXN4WB68=","subType":"0"}},"keyId":7355687795744047109}},"lsid":{"id":{"$uuid":"6083b346-52d2-4c7c-8d49-0fad1e364fcc"}},"$readPreference":{"mode":"primaryPreferred"}},"numYields":0,"reslen":271,"locks":{"ParallelBatchWriterMode":{"acquireCount":{"r":3}},"FeatureCompatibilityVersion":{"acquireCount":{"r":4,"w":2}},"ReplicationStateTransition":{"acquireCount":{"w":6}},"Global":{"acquireCount":{"r":4,"w":2}},"Database":{"acquireCount":{"r":2,"w":1}},"Collection":{"acquireCount":{"r":2,"W":1}},"Mutex":{"acquireCount":{"r":3}}},"flowControl":{"acquireCount":2,"timeAcquiringMicros":2},"readConcern":{"level":"local","provenance":"implicitDefault"},"writeConcern":{"w":"majority","wtimeout":0,"provenance":"implicitDefault"},"waitForWriteConcernDurationMillis":1,"storage":{},"remote":"127.0.0.1:60858","protocol":"op_msg","durationMillis":128}}
{"t":{"$date":"2024-04-09T02:26:22.771+00:00"},"s":"I",  "c":"INDEX",    "id":20438,   "ctx":"conn11","msg":"Index build: registering","attr":{"buildUUID":{"uuid":{"$uuid":"3a03790b-49e7-40e0-9e8f-71bb98a091a6"}},"namespace":"appsmith.plugin","collectionUUID":{"uuid":{"$uuid":"049d5237-455e-4e70-ac62-8358d4b8a31e"}},"indexes":1,"firstIndex":{"name":"packageName"}}}
{"t":{"$date":"2024-04-09T02:26:22.806+00:00"},"s":"I",  "c":"INDEX",    "id":20384,   "ctx":"IndexBuildsCoordinatorMongod-0","msg":"Index build: starting","attr":{"buildUUID":{"uuid":{"$uuid":"3a03790b-49e7-40e0-9e8f-71bb98a091a6"}},"collectionUUID":{"uuid":{"$uuid":"049d5237-455e-4e70-ac62-8358d4b8a31e"}},"namespace":"appsmith.plugin","properties":{"v":2,"unique":true,"key":{"packageName":1},"name":"packageName"},"method":"Hybrid","maxTemporaryMemoryUsageMB":200}}
{"t":{"$date":"2024-04-09T02:26:22.807+00:00"},"s":"I",  "c":"INDEX",    "id":20346,   "ctx":"IndexBuildsCoordinatorMongod-0","msg":"Index build: initialized","attr":{"buildUUID":{"uuid":{"$uuid":"3a03790b-49e7-40e0-9e8f-71bb98a091a6"}},"collectionUUID":{"uuid":{"$uuid":"049d5237-455e-4e70-ac62-8358d4b8a31e"}},"namespace":"appsmith.plugin","initializationTimestamp":{"$timestamp":{"t":1712629582,"i":32}}}}
{"t":{"$date":"2024-04-09T02:26:22.808+00:00"},"s":"I",  "c":"STORAGE",  "id":4847600, "ctx":"IndexBuildsCoordinatorMongod-0","msg":"Index build: waiting for last optime before interceptors to be majority committed","attr":{"buildUUID":{"uuid":{"$uuid":"3a03790b-49e7-40e0-9e8f-71bb98a091a6"}},"collectionUUID":{"uuid":{"$uuid":"049d5237-455e-4e70-ac62-8358d4b8a31e"}},"deadline":{"$date":"2024-04-09T02:26:32.808Z"},"timeoutMillis":10000,"lastOpTime":{"ts":{"$timestamp":{"t":1712629582,"i":32}},"t":2}}}
{"t":{"$date":"2024-04-09T02:26:22.808+00:00"},"s":"I",  "c":"INDEX",    "id":20440,   "ctx":"conn11","msg":"Index build: waiting for index build to complete","attr":{"buildUUID":{"uuid":{"$uuid":"3a03790b-49e7-40e0-9e8f-71bb98a091a6"}},"deadline":{"$date":{"$numberLong":"9223372036854775807"}}}}
{"t":{"$date":"2024-04-09T02:26:22.866+00:00"},"s":"I",  "c":"INDEX",    "id":20391,   "ctx":"IndexBuildsCoordinatorMongod-0","msg":"Index build: collection scan done","attr":{"buildUUID":{"uuid":{"$uuid":"3a03790b-49e7-40e0-9e8f-71bb98a091a6"}},"collectionUUID":{"uuid":{"$uuid":"049d5237-455e-4e70-ac62-8358d4b8a31e"}},"namespace":"appsmith.plugin","totalRecords":3,"readSource":"kMajorityCommitted","durationMillis":0}}
{"t":{"$date":"2024-04-09T02:26:22.877+00:00"},"s":"I",  "c":"INDEX",    "id":20685,   "ctx":"IndexBuildsCoordinatorMongod-0","msg":"Index build: inserted keys from external sorter into index","attr":{"namespace":"appsmith.plugin","index":"packageName","keysInserted":3,"durationMillis":0}}
{"t":{"$date":"2024-04-09T02:26:22.888+00:00"},"s":"I",  "c":"STORAGE",  "id":3856201, "ctx":"conn21","msg":"Index build: commit quorum satisfied","attr":{"indexBuildEntry":{"_id":{"$uuid":"3a03790b-49e7-40e0-9e8f-71bb98a091a6"},"collectionUUID":{"$uuid":"049d5237-455e-4e70-ac62-8358d4b8a31e"},"commitQuorum":"votingMembers","indexNames":["packageName"],"commitReadyMembers":["localhost:27017"]}}}
{"t":{"$date":"2024-04-09T02:26:22.890+00:00"},"s":"I",  "c":"CONNPOOL", "id":22566,   "ctx":"ReplNetwork","msg":"Ending connection due to bad connection status","attr":{"hostAndPort":"localhost:27017","error":"CallbackCanceled: Callback was canceled","numOpenConns":0}}
{"t":{"$date":"2024-04-09T02:26:22.890+00:00"},"s":"I",  "c":"STORAGE",  "id":3856203, "ctx":"IndexBuildsCoordinatorMongod-0","msg":"Index build: waiting for next action before completing final phase","attr":{"buildUUID":{"uuid":{"$uuid":"3a03790b-49e7-40e0-9e8f-71bb98a091a6"}}}}
{"t":{"$date":"2024-04-09T02:26:22.891+00:00"},"s":"I",  "c":"NETWORK",  "id":22944,   "ctx":"conn21","msg":"Connection ended","attr":{"remote":"127.0.0.1:60892","uuid":"241e3f19-2a36-4d93-853f-120cb61e687f","connectionId":21,"connectionCount":5}}
{"t":{"$date":"2024-04-09T02:26:22.891+00:00"},"s":"I",  "c":"NETWORK",  "id":22943,   "ctx":"listener","msg":"Connection accepted","attr":{"remote":"127.0.0.1:60904","uuid":"c145d1f3-3542-4627-a4bf-a6c9bf14529b","connectionId":23,"connectionCount":6}}
{"t":{"$date":"2024-04-09T02:26:22.891+00:00"},"s":"I",  "c":"STORAGE",  "id":3856204, "ctx":"IndexBuildsCoordinatorMongod-0","msg":"Index build: received signal","attr":{"buildUUID":{"uuid":{"$uuid":"3a03790b-49e7-40e0-9e8f-71bb98a091a6"}},"action":"Commit quorum Satisfied"}}
{"t":{"$date":"2024-04-09T02:26:22.893+00:00"},"s":"I",  "c":"INDEX",    "id":20345,   "ctx":"IndexBuildsCoordinatorMongod-0","msg":"Index build: done building","attr":{"buildUUID":{"uuid":{"$uuid":"3a03790b-49e7-40e0-9e8f-71bb98a091a6"}},"namespace":"appsmith.plugin","index":"packageName","commitTimestamp":{"$timestamp":{"t":1712629582,"i":34}}}}
{"t":{"$date":"2024-04-09T02:26:22.893+00:00"},"s":"I",  "c":"NETWORK",  "id":51800,   "ctx":"conn23","msg":"client metadata","attr":{"remote":"127.0.0.1:60904","client":"conn23","negotiatedCompressors":["snappy","zstd","zlib"],"doc":{"driver":{"name":"NetworkInterfaceTL","version":"5.0.26"},"os":{"type":"Linux","name":"Ubuntu","architecture":"x86_64","version":"20.04"}}}}
{"t":{"$date":"2024-04-09T02:26:22.896+00:00"},"s":"I",  "c":"ACCESS",   "id":20250,   "ctx":"conn23","msg":"Authentication succeeded","attr":{"mechanism":"SCRAM-SHA-256","speculative":true,"principalName":"__system","authenticationDatabase":"local","remote":"127.0.0.1:60904","extraInfo":{}}}
{"t":{"$date":"2024-04-09T02:26:22.899+00:00"},"s":"I",  "c":"STORAGE",  "id":20663,   "ctx":"IndexBuildsCoordinatorMongod-0","msg":"Index build: completed successfully","attr":{"buildUUID":{"uuid":{"$uuid":"3a03790b-49e7-40e0-9e8f-71bb98a091a6"}},"collectionUUID":{"uuid":{"$uuid":"049d5237-455e-4e70-ac62-8358d4b8a31e"}},"namespace":"appsmith.plugin","indexesBuilt":["packageName"],"numIndexesBefore":3,"numIndexesAfter":4}}
{"t":{"$date":"2024-04-09T02:26:22.900+00:00"},"s":"I",  "c":"INDEX",    "id":20447,   "ctx":"conn11","msg":"Index build: completed","attr":{"buildUUID":{"uuid":{"$uuid":"3a03790b-49e7-40e0-9e8f-71bb98a091a6"}}}}
{"t":{"$date":"2024-04-09T02:26:22.902+00:00"},"s":"I",  "c":"COMMAND",  "id":51803,   "ctx":"conn11","msg":"Slow query","attr":{"type":"command","ns":"appsmith.plugin","command":{"createIndexes":"plugin","indexes":[{"key":{"packageName":1},"name":"packageName","unique":true}],"$db":"appsmith","$clusterTime":{"clusterTime":{"$timestamp":{"t":1712629582,"i":30}},"signature":{"hash":{"$binary":{"base64":"eo9g3rxCD7m9WowZaq6fXN4WB68=","subType":"0"}},"keyId":7355687795744047109}},"lsid":{"id":{"$uuid":"6083b346-52d2-4c7c-8d49-0fad1e364fcc"}},"$readPreference":{"mode":"primaryPreferred"}},"numYields":0,"reslen":271,"locks":{"ParallelBatchWriterMode":{"acquireCount":{"r":3}},"FeatureCompatibilityVersion":{"acquireCount":{"r":4,"w":2}},"ReplicationStateTransition":{"acquireCount":{"w":6}},"Global":{"acquireCount":{"r":4,"w":2}},"Database":{"acquireCount":{"r":2,"w":1}},"Collection":{"acquireCount":{"r":2,"W":1}},"Mutex":{"acquireCount":{"r":3}}},"flowControl":{"acquireCount":2,"timeAcquiringMicros":3},"readConcern":{"level":"local","provenance":"implicitDefault"},"writeConcern":{"w":"majority","wtimeout":0,"provenance":"implicitDefault"},"waitForWriteConcernDurationMillis":1,"storage":{},"remote":"127.0.0.1:60858","protocol":"op_msg","durationMillis":131}}
{"t":{"$date":"2024-04-09T02:26:22.910+00:00"},"s":"I",  "c":"STORAGE",  "id":20320,   "ctx":"conn11","msg":"createCollection","attr":{"namespace":"appsmith.user","uuidDisposition":"generated","uuid":{"uuid":{"$uuid":"a9713af2-d906-433e-9587-a34147da1e01"}},"options":{}}}
{"t":{"$date":"2024-04-09T02:26:22.932+00:00"},"s":"I",  "c":"INDEX",    "id":20345,   "ctx":"conn11","msg":"Index build: done building","attr":{"buildUUID":null,"namespace":"appsmith.user","index":"_id_","commitTimestamp":{"$timestamp":{"t":1712629582,"i":37}}}}
{"t":{"$date":"2024-04-09T02:26:22.933+00:00"},"s":"I",  "c":"INDEX",    "id":20345,   "ctx":"conn11","msg":"Index build: done building","attr":{"buildUUID":null,"namespace":"appsmith.user","index":"createdAt","commitTimestamp":{"$timestamp":{"t":1712629582,"i":37}}}}
{"t":{"$date":"2024-04-09T02:26:22.937+00:00"},"s":"I",  "c":"INDEX",    "id":20438,   "ctx":"conn11","msg":"Index build: registering","attr":{"buildUUID":{"uuid":{"$uuid":"841103e8-d440-4944-8080-c92a34f3b82f"}},"namespace":"appsmith.user","collectionUUID":{"uuid":{"$uuid":"a9713af2-d906-433e-9587-a34147da1e01"}},"indexes":1,"firstIndex":{"name":"email"}}}
{"t":{"$date":"2024-04-09T02:26:22.945+00:00"},"s":"I",  "c":"INDEX",    "id":20345,   "ctx":"conn11","msg":"Index build: done building","attr":{"buildUUID":null,"namespace":"appsmith.user","index":"email","commitTimestamp":{"$timestamp":{"t":1712629582,"i":38}}}}
{"t":{"$date":"2024-04-09T02:26:22.946+00:00"},"s":"I",  "c":"INDEX",    "id":20440,   "ctx":"conn11","msg":"Index build: waiting for index build to complete","attr":{"buildUUID":{"uuid":{"$uuid":"841103e8-d440-4944-8080-c92a34f3b82f"}},"deadline":{"$date":{"$numberLong":"9223372036854775807"}}}}
{"t":{"$date":"2024-04-09T02:26:22.946+00:00"},"s":"I",  "c":"INDEX",    "id":20447,   "ctx":"conn11","msg":"Index build: completed","attr":{"buildUUID":{"uuid":{"$uuid":"841103e8-d440-4944-8080-c92a34f3b82f"}}}}
{"t":{"$date":"2024-04-09T02:26:22.954+00:00"},"s":"I",  "c":"STORAGE",  "id":20320,   "ctx":"conn11","msg":"createCollection","attr":{"namespace":"appsmith.sequence","uuidDisposition":"generated","uuid":{"uuid":{"$uuid":"f2efbdb6-eb07-437c-85c1-7c9ad518b33d"}},"options":{}}}
{"t":{"$date":"2024-04-09T02:26:22.982+00:00"},"s":"I",  "c":"INDEX",    "id":20345,   "ctx":"conn11","msg":"Index build: done building","attr":{"buildUUID":null,"namespace":"appsmith.sequence","index":"_id_","commitTimestamp":{"$timestamp":{"t":1712629582,"i":40}}}}
{"t":{"$date":"2024-04-09T02:26:22.983+00:00"},"s":"I",  "c":"INDEX",    "id":20345,   "ctx":"conn11","msg":"Index build: done building","attr":{"buildUUID":null,"namespace":"appsmith.sequence","index":"name","commitTimestamp":{"$timestamp":{"t":1712629582,"i":40}}}}
{"t":{"$date":"2024-04-09T02:26:23.192+00:00"},"s":"I",  "c":"COMMAND",  "id":51806,   "ctx":"conn11","msg":"CMD: dropIndexes","attr":{"namespace":"appsmith.passwordResetToken","uuid":{"uuid":{"$uuid":"dde13d3b-a6e9-4b93-a805-b85c54fd2fb1"}},"indexes":"createdAt"}}
{"t":{"$date":"2024-04-09T02:26:23.193+00:00"},"s":"I",  "c":"STORAGE",  "id":22206,   "ctx":"conn11","msg":"Deferring table drop for index","attr":{"index":"createdAt","namespace":"appsmith.passwordResetToken","uuid":{"uuid":{"$uuid":"dde13d3b-a6e9-4b93-a805-b85c54fd2fb1"}},"ident":"index-35--7653259994859265256","dropTime":{"":{"$timestamp":{"t":1712629583,"i":14}}}}}
{"t":{"$date":"2024-04-09T02:26:23.199+00:00"},"s":"I",  "c":"COMMAND",  "id":51806,   "ctx":"conn11","msg":"CMD: dropIndexes","attr":{"namespace":"appsmith.passwordResetToken","uuid":{"uuid":{"$uuid":"dde13d3b-a6e9-4b93-a805-b85c54fd2fb1"}},"indexes":"email"}}
{"t":{"$date":"2024-04-09T02:26:23.200+00:00"},"s":"I",  "c":"STORAGE",  "id":22206,   "ctx":"conn11","msg":"Deferring table drop for index","attr":{"index":"email","namespace":"appsmith.passwordResetToken","uuid":{"uuid":{"$uuid":"dde13d3b-a6e9-4b93-a805-b85c54fd2fb1"}},"ident":"index-36--7653259994859265256","dropTime":{"":{"$timestamp":{"t":1712629583,"i":15}}}}}
{"t":{"$date":"2024-04-09T02:26:23.208+00:00"},"s":"I",  "c":"INDEX",    "id":20438,   "ctx":"conn11","msg":"Index build: registering","attr":{"buildUUID":{"uuid":{"$uuid":"16b6166b-6e97-44fb-b34f-34a4cb6c16eb"}},"namespace":"appsmith.passwordResetToken","collectionUUID":{"uuid":{"$uuid":"dde13d3b-a6e9-4b93-a805-b85c54fd2fb1"}},"indexes":1,"firstIndex":{"name":"createdAt"}}}
{"t":{"$date":"2024-04-09T02:26:23.216+00:00"},"s":"I",  "c":"INDEX",    "id":20345,   "ctx":"conn11","msg":"Index build: done building","attr":{"buildUUID":null,"namespace":"appsmith.passwordResetToken","index":"createdAt","commitTimestamp":{"$timestamp":{"t":1712629583,"i":16}}}}
{"t":{"$date":"2024-04-09T02:26:23.217+00:00"},"s":"I",  "c":"INDEX",    "id":20440,   "ctx":"conn11","msg":"Index build: waiting for index build to complete","attr":{"buildUUID":{"uuid":{"$uuid":"16b6166b-6e97-44fb-b34f-34a4cb6c16eb"}},"deadline":{"$date":{"$numberLong":"9223372036854775807"}}}}
{"t":{"$date":"2024-04-09T02:26:23.217+00:00"},"s":"I",  "c":"INDEX",    "id":20447,   "ctx":"conn11","msg":"Index build: completed","attr":{"buildUUID":{"uuid":{"$uuid":"16b6166b-6e97-44fb-b34f-34a4cb6c16eb"}}}}
{"t":{"$date":"2024-04-09T02:26:23.223+00:00"},"s":"I",  "c":"INDEX",    "id":20438,   "ctx":"conn11","msg":"Index build: registering","attr":{"buildUUID":{"uuid":{"$uuid":"d4b2d516-8a98-4b7f-afc5-8a50d7d5ef1e"}},"namespace":"appsmith.passwordResetToken","collectionUUID":{"uuid":{"$uuid":"dde13d3b-a6e9-4b93-a805-b85c54fd2fb1"}},"indexes":1,"firstIndex":{"name":"email"}}}
{"t":{"$date":"2024-04-09T02:26:23.230+00:00"},"s":"I",  "c":"INDEX",    "id":20345,   "ctx":"conn11","msg":"Index build: done building","attr":{"buildUUID":null,"namespace":"appsmith.passwordResetToken","index":"email","commitTimestamp":{"$timestamp":{"t":1712629583,"i":17}}}}
{"t":{"$date":"2024-04-09T02:26:23.231+00:00"},"s":"I",  "c":"INDEX",    "id":20440,   "ctx":"conn11","msg":"Index build: waiting for index build to complete","attr":{"buildUUID":{"uuid":{"$uuid":"d4b2d516-8a98-4b7f-afc5-8a50d7d5ef1e"}},"deadline":{"$date":{"$numberLong":"9223372036854775807"}}}}
{"t":{"$date":"2024-04-09T02:26:23.231+00:00"},"s":"I",  "c":"INDEX",    "id":20447,   "ctx":"conn11","msg":"Index build: completed","attr":{"buildUUID":{"uuid":{"$uuid":"d4b2d516-8a98-4b7f-afc5-8a50d7d5ef1e"}}}}
{"t":{"$date":"2024-04-09T02:26:23.380+00:00"},"s":"I",  "c":"COMMAND",  "id":518070,  "ctx":"conn11","msg":"CMD: drop","attr":{"namespace":"appsmith.newPage"}}
{"t":{"$date":"2024-04-09T02:26:23.390+00:00"},"s":"I",  "c":"STORAGE",  "id":20320,   "ctx":"conn11","msg":"createCollection","attr":{"namespace":"appsmith.newPage","uuidDisposition":"generated","uuid":{"uuid":{"$uuid":"4f297a9a-e5a8-4320-a4f9-b107584842d5"}},"options":{}}}
{"t":{"$date":"2024-04-09T02:26:23.423+00:00"},"s":"I",  "c":"INDEX",    "id":20345,   "ctx":"conn11","msg":"Index build: done building","attr":{"buildUUID":null,"namespace":"appsmith.newPage","index":"_id_","commitTimestamp":{"$timestamp":{"t":1712629583,"i":30}}}}
{"t":{"$date":"2024-04-09T02:26:23.424+00:00"},"s":"I",  "c":"INDEX",    "id":20345,   "ctx":"conn11","msg":"Index build: done building","attr":{"buildUUID":null,"namespace":"appsmith.newPage","index":"createdAt","commitTimestamp":{"$timestamp":{"t":1712629583,"i":30}}}}
{"t":{"$date":"2024-04-09T02:26:23.434+00:00"},"s":"I",  "c":"COMMAND",  "id":518070,  "ctx":"conn11","msg":"CMD: drop","attr":{"namespace":"appsmith.newAction"}}
{"t":{"$date":"2024-04-09T02:26:23.443+00:00"},"s":"I",  "c":"STORAGE",  "id":20320,   "ctx":"conn11","msg":"createCollection","attr":{"namespace":"appsmith.newAction","uuidDisposition":"generated","uuid":{"uuid":{"$uuid":"c6875e5c-1551-4284-9cea-91504a3940de"}},"options":{}}}
{"t":{"$date":"2024-04-09T02:26:23.471+00:00"},"s":"I",  "c":"INDEX",    "id":20345,   "ctx":"conn11","msg":"Index build: done building","attr":{"buildUUID":null,"namespace":"appsmith.newAction","index":"_id_","commitTimestamp":{"$timestamp":{"t":1712629583,"i":33}}}}
{"t":{"$date":"2024-04-09T02:26:23.471+00:00"},"s":"I",  "c":"INDEX",    "id":20345,   "ctx":"conn11","msg":"Index build: done building","attr":{"buildUUID":null,"namespace":"appsmith.newAction","index":"createdAt","commitTimestamp":{"$timestamp":{"t":1712629583,"i":33}}}}
{"t":{"$date":"2024-04-09T02:26:23.485+00:00"},"s":"I",  "c":"COMMAND",  "id":51806,   "ctx":"conn11","msg":"CMD: dropIndexes","attr":{"namespace":"appsmith.newAction","uuid":{"uuid":{"$uuid":"c6875e5c-1551-4284-9cea-91504a3940de"}},"indexes":"createdAt"}}
{"t":{"$date":"2024-04-09T02:26:23.486+00:00"},"s":"I",  "c":"STORAGE",  "id":22206,   "ctx":"conn11","msg":"Deferring table drop for index","attr":{"index":"createdAt","namespace":"appsmith.newAction","uuid":{"uuid":{"$uuid":"c6875e5c-1551-4284-9cea-91504a3940de"}},"ident":"index-58--7653259994859265256","dropTime":{"":{"$timestamp":{"t":1712629583,"i":35}}}}}
{"t":{"$date":"2024-04-09T02:26:23.493+00:00"},"s":"I",  "c":"COMMAND",  "id":51806,   "ctx":"conn11","msg":"CMD: dropIndexes","attr":{"namespace":"appsmith.newPage","uuid":{"uuid":{"$uuid":"4f297a9a-e5a8-4320-a4f9-b107584842d5"}},"indexes":"createdAt"}}
{"t":{"$date":"2024-04-09T02:26:23.494+00:00"},"s":"I",  "c":"STORAGE",  "id":22206,   "ctx":"conn11","msg":"Deferring table drop for index","attr":{"index":"createdAt","namespace":"appsmith.newPage","uuid":{"uuid":{"$uuid":"4f297a9a-e5a8-4320-a4f9-b107584842d5"}},"ident":"index-55--7653259994859265256","dropTime":{"":{"$timestamp":{"t":1712629583,"i":36}}}}}
{"t":{"$date":"2024-04-09T02:26:23.505+00:00"},"s":"I",  "c":"INDEX",    "id":20438,   "ctx":"conn11","msg":"Index build: registering","attr":{"buildUUID":{"uuid":{"$uuid":"b288dce2-39cb-4d12-bd6f-05da86c7ab19"}},"namespace":"appsmith.newPage","collectionUUID":{"uuid":{"$uuid":"4f297a9a-e5a8-4320-a4f9-b107584842d5"}},"indexes":1,"firstIndex":{"name":"applicationId_deleted_compound_index"}}}
{"t":{"$date":"2024-04-09T02:26:23.518+00:00"},"s":"I",  "c":"INDEX",    "id":20345,   "ctx":"conn11","msg":"Index build: done building","attr":{"buildUUID":null,"namespace":"appsmith.newPage","index":"applicationId_deleted_compound_index","commitTimestamp":{"$timestamp":{"t":1712629583,"i":37}}}}
{"t":{"$date":"2024-04-09T02:26:23.518+00:00"},"s":"I",  "c":"INDEX",    "id":20440,   "ctx":"conn11","msg":"Index build: waiting for index build to complete","attr":{"buildUUID":{"uuid":{"$uuid":"b288dce2-39cb-4d12-bd6f-05da86c7ab19"}},"deadline":{"$date":{"$numberLong":"9223372036854775807"}}}}
{"t":{"$date":"2024-04-09T02:26:23.519+00:00"},"s":"I",  "c":"INDEX",    "id":20447,   "ctx":"conn11","msg":"Index build: completed","attr":{"buildUUID":{"uuid":{"$uuid":"b288dce2-39cb-4d12-bd6f-05da86c7ab19"}}}}
{"t":{"$date":"2024-04-09T02:26:23.537+00:00"},"s":"I",  "c":"INDEX",    "id":20438,   "ctx":"conn11","msg":"Index build: registering","attr":{"buildUUID":{"uuid":{"$uuid":"b4634acb-7d51-4aa8-84d5-33ea67ef2712"}},"namespace":"appsmith.newAction","collectionUUID":{"uuid":{"$uuid":"c6875e5c-1551-4284-9cea-91504a3940de"}},"indexes":1,"firstIndex":{"name":"applicationId"}}}
{"t":{"$date":"2024-04-09T02:26:23.550+00:00"},"s":"I",  "c":"INDEX",    "id":20345,   "ctx":"conn11","msg":"Index build: done building","attr":{"buildUUID":null,"namespace":"appsmith.newAction","index":"applicationId","commitTimestamp":{"$timestamp":{"t":1712629583,"i":39}}}}
{"t":{"$date":"2024-04-09T02:26:23.551+00:00"},"s":"I",  "c":"INDEX",    "id":20440,   "ctx":"conn11","msg":"Index build: waiting for index build to complete","attr":{"buildUUID":{"uuid":{"$uuid":"b4634acb-7d51-4aa8-84d5-33ea67ef2712"}},"deadline":{"$date":{"$numberLong":"9223372036854775807"}}}}
{"t":{"$date":"2024-04-09T02:26:23.552+00:00"},"s":"I",  "c":"INDEX",    "id":20447,   "ctx":"conn11","msg":"Index build: completed","attr":{"buildUUID":{"uuid":{"$uuid":"b4634acb-7d51-4aa8-84d5-33ea67ef2712"}}}}
{"t":{"$date":"2024-04-09T02:26:23.561+00:00"},"s":"I",  "c":"INDEX",    "id":20438,   "ctx":"conn11","msg":"Index build: registering","attr":{"buildUUID":{"uuid":{"$uuid":"1b5d1512-2c98-424e-b51b-011be66b0f79"}},"namespace":"appsmith.newAction","collectionUUID":{"uuid":{"$uuid":"c6875e5c-1551-4284-9cea-91504a3940de"}},"indexes":1,"firstIndex":{"name":"deleted"}}}
{"t":{"$date":"2024-04-09T02:26:23.573+00:00"},"s":"I",  "c":"INDEX",    "id":20345,   "ctx":"conn11","msg":"Index build: done building","attr":{"buildUUID":null,"namespace":"appsmith.newAction","index":"deleted","commitTimestamp":{"$timestamp":{"t":1712629583,"i":40}}}}
{"t":{"$date":"2024-04-09T02:26:23.574+00:00"},"s":"I",  "c":"INDEX",    "id":20440,   "ctx":"conn11","msg":"Index build: waiting for index build to complete","attr":{"buildUUID":{"uuid":{"$uuid":"1b5d1512-2c98-424e-b51b-011be66b0f79"}},"deadline":{"$date":{"$numberLong":"9223372036854775807"}}}}
{"t":{"$date":"2024-04-09T02:26:23.574+00:00"},"s":"I",  "c":"INDEX",    "id":20447,   "ctx":"conn11","msg":"Index build: completed","attr":{"buildUUID":{"uuid":{"$uuid":"1b5d1512-2c98-424e-b51b-011be66b0f79"}}}}
{"t":{"$date":"2024-04-09T02:26:23.654+00:00"},"s":"I",  "c":"COMMAND",  "id":518070,  "ctx":"conn11","msg":"CMD: drop","attr":{"namespace":"appsmith.userData"}}
{"t":{"$date":"2024-04-09T02:26:24.261+00:00"},"s":"I",  "c":"COMMAND",  "id":51806,   "ctx":"conn11","msg":"CMD: dropIndexes","attr":{"namespace":"appsmith.plugin","uuid":{"uuid":{"$uuid":"049d5237-455e-4e70-ac62-8358d4b8a31e"}},"indexes":"packageName"}}
{"t":{"$date":"2024-04-09T02:26:24.262+00:00"},"s":"I",  "c":"STORAGE",  "id":22206,   "ctx":"conn11","msg":"Deferring table drop for index","attr":{"index":"packageName","namespace":"appsmith.plugin","uuid":{"uuid":{"$uuid":"049d5237-455e-4e70-ac62-8358d4b8a31e"}},"ident":"index-41--7653259994859265256","dropTime":{"":{"$timestamp":{"t":1712629584,"i":15}}}}}
{"t":{"$date":"2024-04-09T02:26:24.269+00:00"},"s":"I",  "c":"INDEX",    "id":20438,   "ctx":"conn11","msg":"Index build: registering","attr":{"buildUUID":{"uuid":{"$uuid":"9e27543a-843e-41c1-ab8e-baf0bec64537"}},"namespace":"appsmith.plugin","collectionUUID":{"uuid":{"$uuid":"049d5237-455e-4e70-ac62-8358d4b8a31e"}},"indexes":1,"firstIndex":{"name":"plugin_name_package_name_version_index"}}}
{"t":{"$date":"2024-04-09T02:26:24.295+00:00"},"s":"I",  "c":"INDEX",    "id":20384,   "ctx":"IndexBuildsCoordinatorMongod-0","msg":"Index build: starting","attr":{"buildUUID":{"uuid":{"$uuid":"9e27543a-843e-41c1-ab8e-baf0bec64537"}},"collectionUUID":{"uuid":{"$uuid":"049d5237-455e-4e70-ac62-8358d4b8a31e"}},"namespace":"appsmith.plugin","properties":{"v":2,"unique":true,"key":{"pluginName":1,"packageName":1,"version":1},"name":"plugin_name_package_name_version_index"},"method":"Hybrid","maxTemporaryMemoryUsageMB":200}}
{"t":{"$date":"2024-04-09T02:26:24.296+00:00"},"s":"I",  "c":"INDEX",    "id":20346,   "ctx":"IndexBuildsCoordinatorMongod-0","msg":"Index build: initialized","attr":{"buildUUID":{"uuid":{"$uuid":"9e27543a-843e-41c1-ab8e-baf0bec64537"}},"collectionUUID":{"uuid":{"$uuid":"049d5237-455e-4e70-ac62-8358d4b8a31e"}},"namespace":"appsmith.plugin","initializationTimestamp":{"$timestamp":{"t":1712629584,"i":17}}}}
{"t":{"$date":"2024-04-09T02:26:24.296+00:00"},"s":"I",  "c":"INDEX",    "id":20440,   "ctx":"conn11","msg":"Index build: waiting for index build to complete","attr":{"buildUUID":{"uuid":{"$uuid":"9e27543a-843e-41c1-ab8e-baf0bec64537"}},"deadline":{"$date":{"$numberLong":"9223372036854775807"}}}}
{"t":{"$date":"2024-04-09T02:26:24.296+00:00"},"s":"I",  "c":"STORAGE",  "id":4847600, "ctx":"IndexBuildsCoordinatorMongod-0","msg":"Index build: waiting for last optime before interceptors to be majority committed","attr":{"buildUUID":{"uuid":{"$uuid":"9e27543a-843e-41c1-ab8e-baf0bec64537"}},"collectionUUID":{"uuid":{"$uuid":"049d5237-455e-4e70-ac62-8358d4b8a31e"}},"deadline":{"$date":"2024-04-09T02:26:34.296Z"},"timeoutMillis":10000,"lastOpTime":{"ts":{"$timestamp":{"t":1712629584,"i":17}},"t":2}}}
{"t":{"$date":"2024-04-09T02:26:24.365+00:00"},"s":"I",  "c":"INDEX",    "id":20391,   "ctx":"IndexBuildsCoordinatorMongod-0","msg":"Index build: collection scan done","attr":{"buildUUID":{"uuid":{"$uuid":"9e27543a-843e-41c1-ab8e-baf0bec64537"}},"collectionUUID":{"uuid":{"$uuid":"049d5237-455e-4e70-ac62-8358d4b8a31e"}},"namespace":"appsmith.plugin","totalRecords":15,"readSource":"kMajorityCommitted","durationMillis":0}}
{"t":{"$date":"2024-04-09T02:26:24.371+00:00"},"s":"I",  "c":"INDEX",    "id":20685,   "ctx":"IndexBuildsCoordinatorMongod-0","msg":"Index build: inserted keys from external sorter into index","attr":{"namespace":"appsmith.plugin","index":"plugin_name_package_name_version_index","keysInserted":15,"durationMillis":0}}
{"t":{"$date":"2024-04-09T02:26:24.380+00:00"},"s":"I",  "c":"STORAGE",  "id":3856201, "ctx":"conn23","msg":"Index build: commit quorum satisfied","attr":{"indexBuildEntry":{"_id":{"$uuid":"9e27543a-843e-41c1-ab8e-baf0bec64537"},"collectionUUID":{"$uuid":"049d5237-455e-4e70-ac62-8358d4b8a31e"},"commitQuorum":"votingMembers","indexNames":["plugin_name_package_name_version_index"],"commitReadyMembers":["localhost:27017"]}}}
{"t":{"$date":"2024-04-09T02:26:24.381+00:00"},"s":"I",  "c":"CONNPOOL", "id":22566,   "ctx":"ReplNetwork","msg":"Ending connection due to bad connection status","attr":{"hostAndPort":"localhost:27017","error":"CallbackCanceled: Callback was canceled","numOpenConns":0}}
{"t":{"$date":"2024-04-09T02:26:24.382+00:00"},"s":"I",  "c":"STORAGE",  "id":3856203, "ctx":"IndexBuildsCoordinatorMongod-0","msg":"Index build: waiting for next action before completing final phase","attr":{"buildUUID":{"uuid":{"$uuid":"9e27543a-843e-41c1-ab8e-baf0bec64537"}}}}
{"t":{"$date":"2024-04-09T02:26:24.382+00:00"},"s":"I",  "c":"CONNPOOL", "id":22576,   "ctx":"ReplNetwork","msg":"Connecting","attr":{"hostAndPort":"localhost:27017"}}
{"t":{"$date":"2024-04-09T02:26:24.383+00:00"},"s":"I",  "c":"STORAGE",  "id":3856204, "ctx":"IndexBuildsCoordinatorMongod-0","msg":"Index build: received signal","attr":{"buildUUID":{"uuid":{"$uuid":"9e27543a-843e-41c1-ab8e-baf0bec64537"}},"action":"Commit quorum Satisfied"}}
{"t":{"$date":"2024-04-09T02:26:24.384+00:00"},"s":"I",  "c":"NETWORK",  "id":22944,   "ctx":"conn23","msg":"Connection ended","attr":{"remote":"127.0.0.1:60904","uuid":"c145d1f3-3542-4627-a4bf-a6c9bf14529b","connectionId":23,"connectionCount":5}}
{"t":{"$date":"2024-04-09T02:26:24.384+00:00"},"s":"I",  "c":"NETWORK",  "id":22943,   "ctx":"listener","msg":"Connection accepted","attr":{"remote":"127.0.0.1:60908","uuid":"fd3a3966-f2ee-4eb5-a915-294b78e05347","connectionId":25,"connectionCount":6}}
{"t":{"$date":"2024-04-09T02:26:24.385+00:00"},"s":"I",  "c":"INDEX",    "id":20345,   "ctx":"IndexBuildsCoordinatorMongod-0","msg":"Index build: done building","attr":{"buildUUID":{"uuid":{"$uuid":"9e27543a-843e-41c1-ab8e-baf0bec64537"}},"namespace":"appsmith.plugin","index":"plugin_name_package_name_version_index","commitTimestamp":{"$timestamp":{"t":1712629584,"i":19}}}}
{"t":{"$date":"2024-04-09T02:26:24.386+00:00"},"s":"I",  "c":"NETWORK",  "id":51800,   "ctx":"conn25","msg":"client metadata","attr":{"remote":"127.0.0.1:60908","client":"conn25","negotiatedCompressors":["snappy","zstd","zlib"],"doc":{"driver":{"name":"NetworkInterfaceTL","version":"5.0.26"},"os":{"type":"Linux","name":"Ubuntu","architecture":"x86_64","version":"20.04"}}}}
{"t":{"$date":"2024-04-09T02:26:24.389+00:00"},"s":"I",  "c":"ACCESS",   "id":20250,   "ctx":"conn25","msg":"Authentication succeeded","attr":{"mechanism":"SCRAM-SHA-256","speculative":true,"principalName":"__system","authenticationDatabase":"local","remote":"127.0.0.1:60908","extraInfo":{}}}
{"t":{"$date":"2024-04-09T02:26:24.393+00:00"},"s":"I",  "c":"STORAGE",  "id":20663,   "ctx":"IndexBuildsCoordinatorMongod-0","msg":"Index build: completed successfully","attr":{"buildUUID":{"uuid":{"$uuid":"9e27543a-843e-41c1-ab8e-baf0bec64537"}},"collectionUUID":{"uuid":{"$uuid":"049d5237-455e-4e70-ac62-8358d4b8a31e"}},"namespace":"appsmith.plugin","indexesBuilt":["plugin_name_package_name_version_index"],"numIndexesBefore":3,"numIndexesAfter":4}}
{"t":{"$date":"2024-04-09T02:26:24.394+00:00"},"s":"I",  "c":"INDEX",    "id":20447,   "ctx":"conn11","msg":"Index build: completed","attr":{"buildUUID":{"uuid":{"$uuid":"9e27543a-843e-41c1-ab8e-baf0bec64537"}}}}
{"t":{"$date":"2024-04-09T02:26:24.395+00:00"},"s":"I",  "c":"COMMAND",  "id":51803,   "ctx":"conn11","msg":"Slow query","attr":{"type":"command","ns":"appsmith.plugin","command":{"createIndexes":"plugin","indexes":[{"key":{"pluginName":1,"packageName":1,"version":1},"name":"plugin_name_package_name_version_index","unique":true}],"$db":"appsmith","$clusterTime":{"clusterTime":{"$timestamp":{"t":1712629584,"i":15}},"signature":{"hash":{"$binary":{"base64":"KsizHP8LOZ2TA0tE5hkBTM4dGOM=","subType":"0"}},"keyId":7355687795744047109}},"lsid":{"id":{"$uuid":"6083b346-52d2-4c7c-8d49-0fad1e364fcc"}},"$readPreference":{"mode":"primaryPreferred"}},"numYields":0,"reslen":271,"locks":{"ParallelBatchWriterMode":{"acquireCount":{"r":3}},"FeatureCompatibilityVersion":{"acquireCount":{"r":4,"w":2}},"ReplicationStateTransition":{"acquireCount":{"w":6}},"Global":{"acquireCount":{"r":4,"w":2}},"Database":{"acquireCount":{"r":2,"w":1}},"Collection":{"acquireCount":{"r":2,"W":1}},"Mutex":{"acquireCount":{"r":3}}},"flowControl":{"acquireCount":2,"timeAcquiringMicros":4},"readConcern":{"level":"local","provenance":"implicitDefault"},"writeConcern":{"w":"majority","wtimeout":0,"provenance":"implicitDefault"},"storage":{},"remote":"127.0.0.1:60858","protocol":"op_msg","durationMillis":126}}
{"t":{"$date":"2024-04-09T02:26:24.624+00:00"},"s":"I",  "c":"INDEX",    "id":20438,   "ctx":"conn11","msg":"Index build: registering","attr":{"buildUUID":{"uuid":{"$uuid":"1836a90c-c5fb-4a19-813e-464b41d2e694"}},"namespace":"appsmith.newAction","collectionUUID":{"uuid":{"$uuid":"c6875e5c-1551-4284-9cea-91504a3940de"}},"indexes":1,"firstIndex":{"name":"defaultActionId_branchName_deleted_compound_index"}}}
{"t":{"$date":"2024-04-09T02:26:24.648+00:00"},"s":"I",  "c":"INDEX",    "id":20345,   "ctx":"conn11","msg":"Index build: done building","attr":{"buildUUID":null,"namespace":"appsmith.newAction","index":"defaultActionId_branchName_deleted_compound_index","commitTimestamp":{"$timestamp":{"t":1712629584,"i":31}}}}
{"t":{"$date":"2024-04-09T02:26:24.649+00:00"},"s":"I",  "c":"INDEX",    "id":20440,   "ctx":"conn11","msg":"Index build: waiting for index build to complete","attr":{"buildUUID":{"uuid":{"$uuid":"1836a90c-c5fb-4a19-813e-464b41d2e694"}},"deadline":{"$date":{"$numberLong":"9223372036854775807"}}}}
{"t":{"$date":"2024-04-09T02:26:24.650+00:00"},"s":"I",  "c":"INDEX",    "id":20447,   "ctx":"conn11","msg":"Index build: completed","attr":{"buildUUID":{"uuid":{"$uuid":"1836a90c-c5fb-4a19-813e-464b41d2e694"}}}}
{"t":{"$date":"2024-04-09T02:26:24.665+00:00"},"s":"I",  "c":"STORAGE",  "id":20320,   "ctx":"conn11","msg":"createCollection","attr":{"namespace":"appsmith.actionCollection","uuidDisposition":"generated","uuid":{"uuid":{"$uuid":"9f67c6b7-6ddd-4347-8cb9-7467c42fe4fd"}},"options":{}}}
{"t":{"$date":"2024-04-09T02:26:24.703+00:00"},"s":"I",  "c":"INDEX",    "id":20345,   "ctx":"conn11","msg":"Index build: done building","attr":{"buildUUID":null,"namespace":"appsmith.actionCollection","index":"_id_","commitTimestamp":{"$timestamp":{"t":1712629584,"i":33}}}}
{"t":{"$date":"2024-04-09T02:26:24.704+00:00"},"s":"I",  "c":"INDEX",    "id":20345,   "ctx":"conn11","msg":"Index build: done building","attr":{"buildUUID":null,"namespace":"appsmith.actionCollection","index":"defaultCollectionId_branchName_deleted","commitTimestamp":{"$timestamp":{"t":1712629584,"i":33}}}}
{"t":{"$date":"2024-04-09T02:26:24.714+00:00"},"s":"I",  "c":"INDEX",    "id":20438,   "ctx":"conn11","msg":"Index build: registering","attr":{"buildUUID":{"uuid":{"$uuid":"*************-4d07-8fba-a996e14aa170"}},"namespace":"appsmith.newPage","collectionUUID":{"uuid":{"$uuid":"4f297a9a-e5a8-4320-a4f9-b107584842d5"}},"indexes":1,"firstIndex":{"name":"defaultPageId_branchName_deleted_compound_index"}}}
{"t":{"$date":"2024-04-09T02:26:24.729+00:00"},"s":"I",  "c":"INDEX",    "id":20345,   "ctx":"conn11","msg":"Index build: done building","attr":{"buildUUID":null,"namespace":"appsmith.newPage","index":"defaultPageId_branchName_deleted_compound_index","commitTimestamp":{"$timestamp":{"t":1712629584,"i":34}}}}
{"t":{"$date":"2024-04-09T02:26:24.730+00:00"},"s":"I",  "c":"INDEX",    "id":20440,   "ctx":"conn11","msg":"Index build: waiting for index build to complete","attr":{"buildUUID":{"uuid":{"$uuid":"*************-4d07-8fba-a996e14aa170"}},"deadline":{"$date":{"$numberLong":"9223372036854775807"}}}}
{"t":{"$date":"2024-04-09T02:26:24.731+00:00"},"s":"I",  "c":"INDEX",    "id":20447,   "ctx":"conn11","msg":"Index build: completed","attr":{"buildUUID":{"uuid":{"$uuid":"*************-4d07-8fba-a996e14aa170"}}}}
{"t":{"$date":"2024-04-09T02:26:24.752+00:00"},"s":"I",  "c":"INDEX",    "id":20438,   "ctx":"conn11","msg":"Index build: registering","attr":{"buildUUID":{"uuid":{"$uuid":"93f2dff9-eaa8-460d-af0f-9d4102336a24"}},"namespace":"appsmith.application","collectionUUID":{"uuid":{"$uuid":"ff48fded-c9f9-4807-a12d-a235fe9711f4"}},"indexes":1,"firstIndex":{"name":"defaultApplicationId_branchName_deleted"}}}
{"t":{"$date":"2024-04-09T02:26:24.765+00:00"},"s":"I",  "c":"INDEX",    "id":20345,   "ctx":"conn11","msg":"Index build: done building","attr":{"buildUUID":null,"namespace":"appsmith.application","index":"defaultApplicationId_branchName_deleted","commitTimestamp":{"$timestamp":{"t":1712629584,"i":35}}}}
{"t":{"$date":"2024-04-09T02:26:24.766+00:00"},"s":"I",  "c":"INDEX",    "id":20440,   "ctx":"conn11","msg":"Index build: waiting for index build to complete","attr":{"buildUUID":{"uuid":{"$uuid":"93f2dff9-eaa8-460d-af0f-9d4102336a24"}},"deadline":{"$date":{"$numberLong":"9223372036854775807"}}}}
{"t":{"$date":"2024-04-09T02:26:24.767+00:00"},"s":"I",  "c":"INDEX",    "id":20447,   "ctx":"conn11","msg":"Index build: completed","attr":{"buildUUID":{"uuid":{"$uuid":"93f2dff9-eaa8-460d-af0f-9d4102336a24"}}}}
{"t":{"$date":"2024-04-09T02:26:24.881+00:00"},"s":"I",  "c":"INDEX",    "id":20438,   "ctx":"conn11","msg":"Index build: registering","attr":{"buildUUID":{"uuid":{"$uuid":"1cdb19e2-339e-4003-bc9b-32f7af7a7d3e"}},"namespace":"appsmith.actionCollection","collectionUUID":{"uuid":{"$uuid":"9f67c6b7-6ddd-4347-8cb9-7467c42fe4fd"}},"indexes":1,"firstIndex":{"name":"applicationId"}}}
{"t":{"$date":"2024-04-09T02:26:24.888+00:00"},"s":"I",  "c":"INDEX",    "id":20345,   "ctx":"conn11","msg":"Index build: done building","attr":{"buildUUID":null,"namespace":"appsmith.actionCollection","index":"applicationId","commitTimestamp":{"$timestamp":{"t":1712629584,"i":53}}}}
{"t":{"$date":"2024-04-09T02:26:24.889+00:00"},"s":"I",  "c":"INDEX",    "id":20440,   "ctx":"conn11","msg":"Index build: waiting for index build to complete","attr":{"buildUUID":{"uuid":{"$uuid":"1cdb19e2-339e-4003-bc9b-32f7af7a7d3e"}},"deadline":{"$date":{"$numberLong":"9223372036854775807"}}}}
{"t":{"$date":"2024-04-09T02:26:24.889+00:00"},"s":"I",  "c":"INDEX",    "id":20447,   "ctx":"conn11","msg":"Index build: completed","attr":{"buildUUID":{"uuid":{"$uuid":"1cdb19e2-339e-4003-bc9b-32f7af7a7d3e"}}}}
{"t":{"$date":"2024-04-09T02:26:24.895+00:00"},"s":"I",  "c":"STORAGE",  "id":20320,   "ctx":"conn11","msg":"createCollection","attr":{"namespace":"appsmith.userData","uuidDisposition":"generated","uuid":{"uuid":{"$uuid":"141d16c9-40e8-4143-b3e9-b8c5677b2349"}},"options":{}}}
{"t":{"$date":"2024-04-09T02:26:24.918+00:00"},"s":"I",  "c":"INDEX",    "id":20345,   "ctx":"conn11","msg":"Index build: done building","attr":{"buildUUID":null,"namespace":"appsmith.userData","index":"_id_","commitTimestamp":{"$timestamp":{"t":1712629584,"i":55}}}}
{"t":{"$date":"2024-04-09T02:26:24.919+00:00"},"s":"I",  "c":"INDEX",    "id":20345,   "ctx":"conn11","msg":"Index build: done building","attr":{"buildUUID":null,"namespace":"appsmith.userData","index":"userId","commitTimestamp":{"$timestamp":{"t":1712629584,"i":55}}}}
{"t":{"$date":"2024-04-09T02:26:24.953+00:00"},"s":"I",  "c":"INDEX",    "id":20438,   "ctx":"conn11","msg":"Index build: registering","attr":{"buildUUID":{"uuid":{"$uuid":"c5f0eabe-0a12-4ed5-b574-510142b5676c"}},"namespace":"appsmith.newAction","collectionUUID":{"uuid":{"$uuid":"c6875e5c-1551-4284-9cea-91504a3940de"}},"indexes":1,"firstIndex":{"name":"unpublishedActionPageId_deleted_compound_index"}}}
{"t":{"$date":"2024-04-09T02:26:24.963+00:00"},"s":"I",  "c":"INDEX",    "id":20345,   "ctx":"conn11","msg":"Index build: done building","attr":{"buildUUID":null,"namespace":"appsmith.newAction","index":"unpublishedActionPageId_deleted_compound_index","commitTimestamp":{"$timestamp":{"t":1712629584,"i":59}}}}
{"t":{"$date":"2024-04-09T02:26:24.963+00:00"},"s":"I",  "c":"INDEX",    "id":20440,   "ctx":"conn11","msg":"Index build: waiting for index build to complete","attr":{"buildUUID":{"uuid":{"$uuid":"c5f0eabe-0a12-4ed5-b574-510142b5676c"}},"deadline":{"$date":{"$numberLong":"9223372036854775807"}}}}
{"t":{"$date":"2024-04-09T02:26:24.964+00:00"},"s":"I",  "c":"INDEX",    "id":20447,   "ctx":"conn11","msg":"Index build: completed","attr":{"buildUUID":{"uuid":{"$uuid":"c5f0eabe-0a12-4ed5-b574-510142b5676c"}}}}
{"t":{"$date":"2024-04-09T02:26:24.971+00:00"},"s":"I",  "c":"INDEX",    "id":20438,   "ctx":"conn11","msg":"Index build: registering","attr":{"buildUUID":{"uuid":{"$uuid":"6cf2eda0-d68e-4d4a-abeb-bb3b30218850"}},"namespace":"appsmith.newAction","collectionUUID":{"uuid":{"$uuid":"c6875e5c-1551-4284-9cea-91504a3940de"}},"indexes":1,"firstIndex":{"name":"publishedActionPageId_deleted_compound_index"}}}
{"t":{"$date":"2024-04-09T02:26:24.981+00:00"},"s":"I",  "c":"INDEX",    "id":20345,   "ctx":"conn11","msg":"Index build: done building","attr":{"buildUUID":null,"namespace":"appsmith.newAction","index":"publishedActionPageId_deleted_compound_index","commitTimestamp":{"$timestamp":{"t":1712629584,"i":60}}}}
{"t":{"$date":"2024-04-09T02:26:24.982+00:00"},"s":"I",  "c":"INDEX",    "id":20440,   "ctx":"conn11","msg":"Index build: waiting for index build to complete","attr":{"buildUUID":{"uuid":{"$uuid":"6cf2eda0-d68e-4d4a-abeb-bb3b30218850"}},"deadline":{"$date":{"$numberLong":"9223372036854775807"}}}}
{"t":{"$date":"2024-04-09T02:26:24.983+00:00"},"s":"I",  "c":"INDEX",    "id":20447,   "ctx":"conn11","msg":"Index build: completed","attr":{"buildUUID":{"uuid":{"$uuid":"6cf2eda0-d68e-4d4a-abeb-bb3b30218850"}}}}
{"t":{"$date":"2024-04-09T02:26:24.990+00:00"},"s":"I",  "c":"INDEX",    "id":20438,   "ctx":"conn11","msg":"Index build: registering","attr":{"buildUUID":{"uuid":{"$uuid":"d095b9b2-f724-47d6-97f9-188698e9cbbb"}},"namespace":"appsmith.actionCollection","collectionUUID":{"uuid":{"$uuid":"9f67c6b7-6ddd-4347-8cb9-7467c42fe4fd"}},"indexes":1,"firstIndex":{"name":"unpublishedCollectionPageId_deleted"}}}
{"t":{"$date":"2024-04-09T02:26:25.002+00:00"},"s":"I",  "c":"INDEX",    "id":20345,   "ctx":"conn11","msg":"Index build: done building","attr":{"buildUUID":null,"namespace":"appsmith.actionCollection","index":"unpublishedCollectionPageId_deleted","commitTimestamp":{"$timestamp":{"t":1712629584,"i":61}}}}
{"t":{"$date":"2024-04-09T02:26:25.003+00:00"},"s":"I",  "c":"INDEX",    "id":20440,   "ctx":"conn11","msg":"Index build: waiting for index build to complete","attr":{"buildUUID":{"uuid":{"$uuid":"d095b9b2-f724-47d6-97f9-188698e9cbbb"}},"deadline":{"$date":{"$numberLong":"9223372036854775807"}}}}
{"t":{"$date":"2024-04-09T02:26:25.004+00:00"},"s":"I",  "c":"INDEX",    "id":20447,   "ctx":"conn11","msg":"Index build: completed","attr":{"buildUUID":{"uuid":{"$uuid":"d095b9b2-f724-47d6-97f9-188698e9cbbb"}}}}
{"t":{"$date":"2024-04-09T02:26:25.009+00:00"},"s":"I",  "c":"INDEX",    "id":20438,   "ctx":"conn11","msg":"Index build: registering","attr":{"buildUUID":{"uuid":{"$uuid":"2539bd8e-dce9-4818-99a3-4b4f9361efeb"}},"namespace":"appsmith.actionCollection","collectionUUID":{"uuid":{"$uuid":"9f67c6b7-6ddd-4347-8cb9-7467c42fe4fd"}},"indexes":1,"firstIndex":{"name":"publishedCollectionPageId_deleted"}}}
{"t":{"$date":"2024-04-09T02:26:25.021+00:00"},"s":"I",  "c":"INDEX",    "id":20345,   "ctx":"conn11","msg":"Index build: done building","attr":{"buildUUID":null,"namespace":"appsmith.actionCollection","index":"publishedCollectionPageId_deleted","commitTimestamp":{"$timestamp":{"t":1712629585,"i":1}}}}
{"t":{"$date":"2024-04-09T02:26:25.022+00:00"},"s":"I",  "c":"INDEX",    "id":20440,   "ctx":"conn11","msg":"Index build: waiting for index build to complete","attr":{"buildUUID":{"uuid":{"$uuid":"2539bd8e-dce9-4818-99a3-4b4f9361efeb"}},"deadline":{"$date":{"$numberLong":"9223372036854775807"}}}}
{"t":{"$date":"2024-04-09T02:26:25.023+00:00"},"s":"I",  "c":"INDEX",    "id":20447,   "ctx":"conn11","msg":"Index build: completed","attr":{"buildUUID":{"uuid":{"$uuid":"2539bd8e-dce9-4818-99a3-4b4f9361efeb"}}}}
{"t":{"$date":"2024-04-09T02:26:25.091+00:00"},"s":"I",  "c":"INDEX",    "id":20438,   "ctx":"conn11","msg":"Index build: registering","attr":{"buildUUID":{"uuid":{"$uuid":"e22c3c00-dc57-4fe6-b1ff-d05b6b85fd06"}},"namespace":"appsmith.actionCollection","collectionUUID":{"uuid":{"$uuid":"9f67c6b7-6ddd-4347-8cb9-7467c42fe4fd"}},"indexes":1,"firstIndex":{"name":"defaultApplicationId_gitSyncId_deleted"}}}
{"t":{"$date":"2024-04-09T02:26:25.103+00:00"},"s":"I",  "c":"INDEX",    "id":20345,   "ctx":"conn11","msg":"Index build: done building","attr":{"buildUUID":null,"namespace":"appsmith.actionCollection","index":"defaultApplicationId_gitSyncId_deleted","commitTimestamp":{"$timestamp":{"t":1712629585,"i":7}}}}
{"t":{"$date":"2024-04-09T02:26:25.104+00:00"},"s":"I",  "c":"INDEX",    "id":20440,   "ctx":"conn11","msg":"Index build: waiting for index build to complete","attr":{"buildUUID":{"uuid":{"$uuid":"e22c3c00-dc57-4fe6-b1ff-d05b6b85fd06"}},"deadline":{"$date":{"$numberLong":"9223372036854775807"}}}}
{"t":{"$date":"2024-04-09T02:26:25.105+00:00"},"s":"I",  "c":"INDEX",    "id":20447,   "ctx":"conn11","msg":"Index build: completed","attr":{"buildUUID":{"uuid":{"$uuid":"e22c3c00-dc57-4fe6-b1ff-d05b6b85fd06"}}}}
{"t":{"$date":"2024-04-09T02:26:25.121+00:00"},"s":"I",  "c":"INDEX",    "id":20438,   "ctx":"conn11","msg":"Index build: registering","attr":{"buildUUID":{"uuid":{"$uuid":"39970174-bd8b-484d-940e-1880b299e730"}},"namespace":"appsmith.newAction","collectionUUID":{"uuid":{"$uuid":"c6875e5c-1551-4284-9cea-91504a3940de"}},"indexes":1,"firstIndex":{"name":"defaultApplicationId_gitSyncId_deleted"}}}
{"t":{"$date":"2024-04-09T02:26:25.140+00:00"},"s":"I",  "c":"INDEX",    "id":20345,   "ctx":"conn11","msg":"Index build: done building","attr":{"buildUUID":null,"namespace":"appsmith.newAction","index":"defaultApplicationId_gitSyncId_deleted","commitTimestamp":{"$timestamp":{"t":1712629585,"i":8}}}}
{"t":{"$date":"2024-04-09T02:26:25.147+00:00"},"s":"I",  "c":"INDEX",    "id":20440,   "ctx":"conn11","msg":"Index build: waiting for index build to complete","attr":{"buildUUID":{"uuid":{"$uuid":"39970174-bd8b-484d-940e-1880b299e730"}},"deadline":{"$date":{"$numberLong":"9223372036854775807"}}}}
{"t":{"$date":"2024-04-09T02:26:25.150+00:00"},"s":"I",  "c":"INDEX",    "id":20447,   "ctx":"conn11","msg":"Index build: completed","attr":{"buildUUID":{"uuid":{"$uuid":"39970174-bd8b-484d-940e-1880b299e730"}}}}
{"t":{"$date":"2024-04-09T02:26:25.159+00:00"},"s":"I",  "c":"INDEX",    "id":20438,   "ctx":"conn11","msg":"Index build: registering","attr":{"buildUUID":{"uuid":{"$uuid":"d32bc330-e56a-4a57-a547-14577221d908"}},"namespace":"appsmith.newPage","collectionUUID":{"uuid":{"$uuid":"4f297a9a-e5a8-4320-a4f9-b107584842d5"}},"indexes":1,"firstIndex":{"name":"defaultApplicationId_gitSyncId_deleted"}}}
{"t":{"$date":"2024-04-09T02:26:25.172+00:00"},"s":"I",  "c":"INDEX",    "id":20345,   "ctx":"conn11","msg":"Index build: done building","attr":{"buildUUID":null,"namespace":"appsmith.newPage","index":"defaultApplicationId_gitSyncId_deleted","commitTimestamp":{"$timestamp":{"t":1712629585,"i":9}}}}
{"t":{"$date":"2024-04-09T02:26:25.173+00:00"},"s":"I",  "c":"INDEX",    "id":20440,   "ctx":"conn11","msg":"Index build: waiting for index build to complete","attr":{"buildUUID":{"uuid":{"$uuid":"d32bc330-e56a-4a57-a547-14577221d908"}},"deadline":{"$date":{"$numberLong":"9223372036854775807"}}}}
{"t":{"$date":"2024-04-09T02:26:25.174+00:00"},"s":"I",  "c":"INDEX",    "id":20447,   "ctx":"conn11","msg":"Index build: completed","attr":{"buildUUID":{"uuid":{"$uuid":"d32bc330-e56a-4a57-a547-14577221d908"}}}}
{"t":{"$date":"2024-04-09T02:26:25.196+00:00"},"s":"I",  "c":"STORAGE",  "id":20320,   "ctx":"conn11","msg":"createCollection","attr":{"namespace":"appsmith.workspace","uuidDisposition":"generated","uuid":{"uuid":{"$uuid":"8f643824-f3e0-4603-8204-a1092fca887e"}},"options":{}}}
{"t":{"$date":"2024-04-09T02:26:25.226+00:00"},"s":"I",  "c":"INDEX",    "id":20345,   "ctx":"conn11","msg":"Index build: done building","attr":{"buildUUID":null,"namespace":"appsmith.workspace","index":"_id_","commitTimestamp":{"$timestamp":{"t":1712629585,"i":12}}}}
{"t":{"$date":"2024-04-09T02:26:25.227+00:00"},"s":"I",  "c":"INDEX",    "id":20345,   "ctx":"conn11","msg":"Index build: done building","attr":{"buildUUID":null,"namespace":"appsmith.workspace","index":"createdAt","commitTimestamp":{"$timestamp":{"t":1712629585,"i":12}}}}
{"t":{"$date":"2024-04-09T02:26:25.269+00:00"},"s":"I",  "c":"STORAGE",  "id":20320,   "ctx":"conn11","msg":"createCollection","attr":{"namespace":"appsmith.tenant","uuidDisposition":"generated","uuid":{"uuid":{"$uuid":"12bc2c99-b663-4c72-8123-33c8654683be"}},"options":{}}}
{"t":{"$date":"2024-04-09T02:26:25.288+00:00"},"s":"I",  "c":"INDEX",    "id":20345,   "ctx":"conn11","msg":"Index build: done building","attr":{"buildUUID":null,"namespace":"appsmith.tenant","index":"_id_","commitTimestamp":{"$timestamp":{"t":1712629585,"i":14}}}}
{"t":{"$date":"2024-04-09T02:26:25.303+00:00"},"s":"I",  "c":"COMMAND",  "id":51806,   "ctx":"conn11","msg":"CMD: dropIndexes","attr":{"namespace":"appsmith.application","uuid":{"uuid":{"$uuid":"ff48fded-c9f9-4807-a12d-a235fe9711f4"}},"indexes":"workspace_app_deleted_gitApplicationMetadata"}}
{"t":{"$date":"2024-04-09T02:26:25.315+00:00"},"s":"I",  "c":"COMMAND",  "id":51806,   "ctx":"conn11","msg":"CMD: dropIndexes","attr":{"namespace":"appsmith.datasource","uuid":{"uuid":{"$uuid":"d2744a10-b6c0-4f57-b427-a25d4aacb1cc"}},"indexes":"workspace_datasource_deleted_compound_index"}}
{"t":{"$date":"2024-04-09T02:26:25.323+00:00"},"s":"I",  "c":"INDEX",    "id":20438,   "ctx":"conn11","msg":"Index build: registering","attr":{"buildUUID":{"uuid":{"$uuid":"cba9428f-c97d-49da-86c0-3c86b82f37ea"}},"namespace":"appsmith.application","collectionUUID":{"uuid":{"$uuid":"ff48fded-c9f9-4807-a12d-a235fe9711f4"}},"indexes":1,"firstIndex":{"name":"workspace_app_deleted_gitApplicationMetadata"}}}
{"t":{"$date":"2024-04-09T02:26:25.332+00:00"},"s":"I",  "c":"INDEX",    "id":20345,   "ctx":"conn11","msg":"Index build: done building","attr":{"buildUUID":null,"namespace":"appsmith.application","index":"workspace_app_deleted_gitApplicationMetadata","commitTimestamp":{"$timestamp":{"t":1712629585,"i":17}}}}
{"t":{"$date":"2024-04-09T02:26:25.333+00:00"},"s":"I",  "c":"INDEX",    "id":20440,   "ctx":"conn11","msg":"Index build: waiting for index build to complete","attr":{"buildUUID":{"uuid":{"$uuid":"cba9428f-c97d-49da-86c0-3c86b82f37ea"}},"deadline":{"$date":{"$numberLong":"9223372036854775807"}}}}
{"t":{"$date":"2024-04-09T02:26:25.333+00:00"},"s":"I",  "c":"INDEX",    "id":20447,   "ctx":"conn11","msg":"Index build: completed","attr":{"buildUUID":{"uuid":{"$uuid":"cba9428f-c97d-49da-86c0-3c86b82f37ea"}}}}
{"t":{"$date":"2024-04-09T02:26:25.339+00:00"},"s":"I",  "c":"INDEX",    "id":20438,   "ctx":"conn11","msg":"Index build: registering","attr":{"buildUUID":{"uuid":{"$uuid":"ce5c40f4-0f32-4c18-9868-5009652b1621"}},"namespace":"appsmith.datasource","collectionUUID":{"uuid":{"$uuid":"d2744a10-b6c0-4f57-b427-a25d4aacb1cc"}},"indexes":1,"firstIndex":{"name":"workspace_datasource_deleted_compound_index"}}}
{"t":{"$date":"2024-04-09T02:26:25.352+00:00"},"s":"I",  "c":"INDEX",    "id":20345,   "ctx":"conn11","msg":"Index build: done building","attr":{"buildUUID":null,"namespace":"appsmith.datasource","index":"workspace_datasource_deleted_compound_index","commitTimestamp":{"$timestamp":{"t":1712629585,"i":18}}}}
{"t":{"$date":"2024-04-09T02:26:25.354+00:00"},"s":"I",  "c":"INDEX",    "id":20440,   "ctx":"conn11","msg":"Index build: waiting for index build to complete","attr":{"buildUUID":{"uuid":{"$uuid":"ce5c40f4-0f32-4c18-9868-5009652b1621"}},"deadline":{"$date":{"$numberLong":"9223372036854775807"}}}}
{"t":{"$date":"2024-04-09T02:26:25.355+00:00"},"s":"I",  "c":"INDEX",    "id":20447,   "ctx":"conn11","msg":"Index build: completed","attr":{"buildUUID":{"uuid":{"$uuid":"ce5c40f4-0f32-4c18-9868-5009652b1621"}}}}
{"t":{"$date":"2024-04-09T02:26:25.486+00:00"},"s":"I",  "c":"STORAGE",  "id":20320,   "ctx":"conn11","msg":"createCollection","attr":{"namespace":"appsmith.permissionGroup","uuidDisposition":"generated","uuid":{"uuid":{"$uuid":"5b9e43cc-277d-4622-af41-98746e3cbbc4"}},"options":{}}}
{"t":{"$date":"2024-04-09T02:26:25.503+00:00"},"s":"I",  "c":"INDEX",    "id":20345,   "ctx":"conn11","msg":"Index build: done building","attr":{"buildUUID":null,"namespace":"appsmith.permissionGroup","index":"_id_","commitTimestamp":{"$timestamp":{"t":1712629585,"i":24}}}}
{"t":{"$date":"2024-04-09T02:26:25.582+00:00"},"s":"I",  "c":"STORAGE",  "id":20320,   "ctx":"conn11","msg":"createCollection","attr":{"namespace":"appsmith.theme","uuidDisposition":"generated","uuid":{"uuid":{"$uuid":"fa177fad-3d5e-4524-aa76-dddc6145c4d6"}},"options":{}}}
{"t":{"$date":"2024-04-09T02:26:25.606+00:00"},"s":"I",  "c":"INDEX",    "id":20345,   "ctx":"conn11","msg":"Index build: done building","attr":{"buildUUID":null,"namespace":"appsmith.theme","index":"_id_","commitTimestamp":{"$timestamp":{"t":1712629585,"i":33}}}}
{"t":{"$date":"2024-04-09T02:26:25.607+00:00"},"s":"I",  "c":"INDEX",    "id":20345,   "ctx":"conn11","msg":"Index build: done building","attr":{"buildUUID":null,"namespace":"appsmith.theme","index":"system_theme_index","commitTimestamp":{"$timestamp":{"t":1712629585,"i":33}}}}
{"t":{"$date":"2024-04-09T02:26:25.613+00:00"},"s":"I",  "c":"INDEX",    "id":20438,   "ctx":"conn11","msg":"Index build: registering","attr":{"buildUUID":{"uuid":{"$uuid":"16c4db93-790a-4e0b-a952-eaa1c4cdff89"}},"namespace":"appsmith.theme","collectionUUID":{"uuid":{"$uuid":"fa177fad-3d5e-4524-aa76-dddc6145c4d6"}},"indexes":1,"firstIndex":{"name":"application_id_index"}}}
{"t":{"$date":"2024-04-09T02:26:25.624+00:00"},"s":"I",  "c":"INDEX",    "id":20345,   "ctx":"conn11","msg":"Index build: done building","attr":{"buildUUID":null,"namespace":"appsmith.theme","index":"application_id_index","commitTimestamp":{"$timestamp":{"t":1712629585,"i":34}}}}
{"t":{"$date":"2024-04-09T02:26:25.625+00:00"},"s":"I",  "c":"INDEX",    "id":20440,   "ctx":"conn11","msg":"Index build: waiting for index build to complete","attr":{"buildUUID":{"uuid":{"$uuid":"16c4db93-790a-4e0b-a952-eaa1c4cdff89"}},"deadline":{"$date":{"$numberLong":"9223372036854775807"}}}}
{"t":{"$date":"2024-04-09T02:26:25.625+00:00"},"s":"I",  "c":"INDEX",    "id":20447,   "ctx":"conn11","msg":"Index build: completed","attr":{"buildUUID":{"uuid":{"$uuid":"16c4db93-790a-4e0b-a952-eaa1c4cdff89"}}}}
{"t":{"$date":"2024-04-09T02:26:25.814+00:00"},"s":"I",  "c":"COMMAND",  "id":51806,   "ctx":"conn11","msg":"CMD: dropIndexes","attr":{"namespace":"appsmith.permissionGroup","uuid":{"uuid":{"$uuid":"5b9e43cc-277d-4622-af41-98746e3cbbc4"}},"indexes":"permission_group_workspace_deleted_compound_index"}}
{"t":{"$date":"2024-04-09T02:26:25.821+00:00"},"s":"I",  "c":"COMMAND",  "id":51806,   "ctx":"conn11","msg":"CMD: dropIndexes","attr":{"namespace":"appsmith.permissionGroup","uuid":{"uuid":{"$uuid":"5b9e43cc-277d-4622-af41-98746e3cbbc4"}},"indexes":"permission_group_assignedUserIds_deleted_compound_index"}}
{"t":{"$date":"2024-04-09T02:26:25.826+00:00"},"s":"I",  "c":"COMMAND",  "id":51806,   "ctx":"conn11","msg":"CMD: dropIndexes","attr":{"namespace":"appsmith.permissionGroup","uuid":{"uuid":{"$uuid":"5b9e43cc-277d-4622-af41-98746e3cbbc4"}},"indexes":"permission_group_assignedUserIds_deleted"}}
{"t":{"$date":"2024-04-09T02:26:25.833+00:00"},"s":"I",  "c":"INDEX",    "id":20438,   "ctx":"conn11","msg":"Index build: registering","attr":{"buildUUID":{"uuid":{"$uuid":"50f6f925-2617-4fb9-8b9c-b916627a7bd1"}},"namespace":"appsmith.permissionGroup","collectionUUID":{"uuid":{"$uuid":"5b9e43cc-277d-4622-af41-98746e3cbbc4"}},"indexes":1,"firstIndex":{"name":"permission_group_assignedUserIds_deleted"}}}
{"t":{"$date":"2024-04-09T02:26:25.851+00:00"},"s":"I",  "c":"INDEX",    "id":20384,   "ctx":"IndexBuildsCoordinatorMongod-0","msg":"Index build: starting","attr":{"buildUUID":{"uuid":{"$uuid":"50f6f925-2617-4fb9-8b9c-b916627a7bd1"}},"collectionUUID":{"uuid":{"$uuid":"5b9e43cc-277d-4622-af41-98746e3cbbc4"}},"namespace":"appsmith.permissionGroup","properties":{"v":2,"key":{"assignedToUserIds":1,"deleted":1},"name":"permission_group_assignedUserIds_deleted"},"method":"Hybrid","maxTemporaryMemoryUsageMB":200}}
{"t":{"$date":"2024-04-09T02:26:25.851+00:00"},"s":"I",  "c":"INDEX",    "id":20346,   "ctx":"IndexBuildsCoordinatorMongod-0","msg":"Index build: initialized","attr":{"buildUUID":{"uuid":{"$uuid":"50f6f925-2617-4fb9-8b9c-b916627a7bd1"}},"collectionUUID":{"uuid":{"$uuid":"5b9e43cc-277d-4622-af41-98746e3cbbc4"}},"namespace":"appsmith.permissionGroup","initializationTimestamp":{"$timestamp":{"t":1712629585,"i":48}}}}
{"t":{"$date":"2024-04-09T02:26:25.852+00:00"},"s":"I",  "c":"STORAGE",  "id":4847600, "ctx":"IndexBuildsCoordinatorMongod-0","msg":"Index build: waiting for last optime before interceptors to be majority committed","attr":{"buildUUID":{"uuid":{"$uuid":"50f6f925-2617-4fb9-8b9c-b916627a7bd1"}},"collectionUUID":{"uuid":{"$uuid":"5b9e43cc-277d-4622-af41-98746e3cbbc4"}},"deadline":{"$date":"2024-04-09T02:26:35.851Z"},"timeoutMillis":10000,"lastOpTime":{"ts":{"$timestamp":{"t":1712629585,"i":48}},"t":2}}}
{"t":{"$date":"2024-04-09T02:26:25.852+00:00"},"s":"I",  "c":"INDEX",    "id":20440,   "ctx":"conn11","msg":"Index build: waiting for index build to complete","attr":{"buildUUID":{"uuid":{"$uuid":"50f6f925-2617-4fb9-8b9c-b916627a7bd1"}},"deadline":{"$date":{"$numberLong":"9223372036854775807"}}}}
{"t":{"$date":"2024-04-09T02:26:25.929+00:00"},"s":"I",  "c":"INDEX",    "id":20391,   "ctx":"IndexBuildsCoordinatorMongod-0","msg":"Index build: collection scan done","attr":{"buildUUID":{"uuid":{"$uuid":"50f6f925-2617-4fb9-8b9c-b916627a7bd1"}},"collectionUUID":{"uuid":{"$uuid":"5b9e43cc-277d-4622-af41-98746e3cbbc4"}},"namespace":"appsmith.permissionGroup","totalRecords":2,"readSource":"kMajorityCommitted","durationMillis":0}}
{"t":{"$date":"2024-04-09T02:26:25.934+00:00"},"s":"I",  "c":"INDEX",    "id":20685,   "ctx":"IndexBuildsCoordinatorMongod-0","msg":"Index build: inserted keys from external sorter into index","attr":{"namespace":"appsmith.permissionGroup","index":"permission_group_assignedUserIds_deleted","keysInserted":2,"durationMillis":0}}
{"t":{"$date":"2024-04-09T02:26:25.941+00:00"},"s":"I",  "c":"STORAGE",  "id":3856201, "ctx":"conn25","msg":"Index build: commit quorum satisfied","attr":{"indexBuildEntry":{"_id":{"$uuid":"50f6f925-2617-4fb9-8b9c-b916627a7bd1"},"collectionUUID":{"$uuid":"5b9e43cc-277d-4622-af41-98746e3cbbc4"},"commitQuorum":"votingMembers","indexNames":["permission_group_assignedUserIds_deleted"],"commitReadyMembers":["localhost:27017"]}}}
{"t":{"$date":"2024-04-09T02:26:25.942+00:00"},"s":"I",  "c":"CONNPOOL", "id":22566,   "ctx":"ReplNetwork","msg":"Ending connection due to bad connection status","attr":{"hostAndPort":"localhost:27017","error":"CallbackCanceled: Callback was canceled","numOpenConns":0}}
{"t":{"$date":"2024-04-09T02:26:25.942+00:00"},"s":"I",  "c":"STORAGE",  "id":3856203, "ctx":"IndexBuildsCoordinatorMongod-0","msg":"Index build: waiting for next action before completing final phase","attr":{"buildUUID":{"uuid":{"$uuid":"50f6f925-2617-4fb9-8b9c-b916627a7bd1"}}}}
{"t":{"$date":"2024-04-09T02:26:25.943+00:00"},"s":"I",  "c":"STORAGE",  "id":3856204, "ctx":"IndexBuildsCoordinatorMongod-0","msg":"Index build: received signal","attr":{"buildUUID":{"uuid":{"$uuid":"50f6f925-2617-4fb9-8b9c-b916627a7bd1"}},"action":"Commit quorum Satisfied"}}
{"t":{"$date":"2024-04-09T02:26:25.942+00:00"},"s":"I",  "c":"CONNPOOL", "id":22576,   "ctx":"ReplNetwork","msg":"Connecting","attr":{"hostAndPort":"localhost:27017"}}
{"t":{"$date":"2024-04-09T02:26:25.944+00:00"},"s":"I",  "c":"NETWORK",  "id":22944,   "ctx":"conn25","msg":"Connection ended","attr":{"remote":"127.0.0.1:60908","uuid":"fd3a3966-f2ee-4eb5-a915-294b78e05347","connectionId":25,"connectionCount":5}}
{"t":{"$date":"2024-04-09T02:26:25.945+00:00"},"s":"I",  "c":"INDEX",    "id":20345,   "ctx":"IndexBuildsCoordinatorMongod-0","msg":"Index build: done building","attr":{"buildUUID":{"uuid":{"$uuid":"50f6f925-2617-4fb9-8b9c-b916627a7bd1"}},"namespace":"appsmith.permissionGroup","index":"permission_group_assignedUserIds_deleted","commitTimestamp":{"$timestamp":{"t":1712629585,"i":50}}}}
{"t":{"$date":"2024-04-09T02:26:25.945+00:00"},"s":"I",  "c":"NETWORK",  "id":22943,   "ctx":"listener","msg":"Connection accepted","attr":{"remote":"127.0.0.1:36388","uuid":"4fec3ae2-4f63-4150-a2f8-d01145326c3f","connectionId":26,"connectionCount":6}}
{"t":{"$date":"2024-04-09T02:26:25.947+00:00"},"s":"I",  "c":"NETWORK",  "id":51800,   "ctx":"conn26","msg":"client metadata","attr":{"remote":"127.0.0.1:36388","client":"conn26","negotiatedCompressors":["snappy","zstd","zlib"],"doc":{"driver":{"name":"NetworkInterfaceTL","version":"5.0.26"},"os":{"type":"Linux","name":"Ubuntu","architecture":"x86_64","version":"20.04"}}}}
{"t":{"$date":"2024-04-09T02:26:25.949+00:00"},"s":"I",  "c":"STORAGE",  "id":20663,   "ctx":"IndexBuildsCoordinatorMongod-0","msg":"Index build: completed successfully","attr":{"buildUUID":{"uuid":{"$uuid":"50f6f925-2617-4fb9-8b9c-b916627a7bd1"}},"collectionUUID":{"uuid":{"$uuid":"5b9e43cc-277d-4622-af41-98746e3cbbc4"}},"namespace":"appsmith.permissionGroup","indexesBuilt":["permission_group_assignedUserIds_deleted"],"numIndexesBefore":1,"numIndexesAfter":2}}
{"t":{"$date":"2024-04-09T02:26:25.949+00:00"},"s":"I",  "c":"ACCESS",   "id":20250,   "ctx":"conn26","msg":"Authentication succeeded","attr":{"mechanism":"SCRAM-SHA-256","speculative":true,"principalName":"__system","authenticationDatabase":"local","remote":"127.0.0.1:36388","extraInfo":{}}}
{"t":{"$date":"2024-04-09T02:26:25.950+00:00"},"s":"I",  "c":"INDEX",    "id":20447,   "ctx":"conn11","msg":"Index build: completed","attr":{"buildUUID":{"uuid":{"$uuid":"50f6f925-2617-4fb9-8b9c-b916627a7bd1"}}}}
{"t":{"$date":"2024-04-09T02:26:25.952+00:00"},"s":"I",  "c":"COMMAND",  "id":51803,   "ctx":"conn11","msg":"Slow query","attr":{"type":"command","ns":"appsmith.permissionGroup","command":{"createIndexes":"permissionGroup","indexes":[{"key":{"assignedToUserIds":1,"deleted":1},"name":"permission_group_assignedUserIds_deleted"}],"$db":"appsmith","$clusterTime":{"clusterTime":{"$timestamp":{"t":1712629585,"i":46}},"signature":{"hash":{"$binary":{"base64":"GP4c3Du3wu6OHAlWwtAgcnL2r/Q=","subType":"0"}},"keyId":7355687795744047109}},"lsid":{"id":{"$uuid":"6083b346-52d2-4c7c-8d49-0fad1e364fcc"}},"$readPreference":{"mode":"primaryPreferred"}},"numYields":0,"reslen":271,"locks":{"ParallelBatchWriterMode":{"acquireCount":{"r":3}},"FeatureCompatibilityVersion":{"acquireCount":{"r":4,"w":2}},"ReplicationStateTransition":{"acquireCount":{"w":6}},"Global":{"acquireCount":{"r":4,"w":2}},"Database":{"acquireCount":{"r":2,"w":1}},"Collection":{"acquireCount":{"r":2,"W":1}},"Mutex":{"acquireCount":{"r":3}}},"flowControl":{"acquireCount":2,"timeAcquiringMicros":2},"readConcern":{"level":"local","provenance":"implicitDefault"},"writeConcern":{"w":"majority","wtimeout":0,"provenance":"implicitDefault"},"waitForWriteConcernDurationMillis":1,"storage":{},"remote":"127.0.0.1:60858","protocol":"op_msg","durationMillis":118}}
{"t":{"$date":"2024-04-09T02:26:26.081+00:00"},"s":"I",  "c":"COMMAND",  "id":51806,   "ctx":"conn11","msg":"CMD: dropIndexes","attr":{"namespace":"appsmith.newPage","uuid":{"uuid":{"$uuid":"4f297a9a-e5a8-4320-a4f9-b107584842d5"}},"indexes":"deleted"}}
{"t":{"$date":"2024-04-09T02:26:26.090+00:00"},"s":"I",  "c":"INDEX",    "id":20438,   "ctx":"conn11","msg":"Index build: registering","attr":{"buildUUID":{"uuid":{"$uuid":"9ab74cf3-c906-4128-8954-fbd4f252f4da"}},"namespace":"appsmith.newPage","collectionUUID":{"uuid":{"$uuid":"4f297a9a-e5a8-4320-a4f9-b107584842d5"}},"indexes":1,"firstIndex":{"name":"deleted"}}}
{"t":{"$date":"2024-04-09T02:26:26.098+00:00"},"s":"I",  "c":"INDEX",    "id":20345,   "ctx":"conn11","msg":"Index build: done building","attr":{"buildUUID":null,"namespace":"appsmith.newPage","index":"deleted","commitTimestamp":{"$timestamp":{"t":1712629586,"i":6}}}}
{"t":{"$date":"2024-04-09T02:26:26.099+00:00"},"s":"I",  "c":"INDEX",    "id":20440,   "ctx":"conn11","msg":"Index build: waiting for index build to complete","attr":{"buildUUID":{"uuid":{"$uuid":"9ab74cf3-c906-4128-8954-fbd4f252f4da"}},"deadline":{"$date":{"$numberLong":"9223372036854775807"}}}}
{"t":{"$date":"2024-04-09T02:26:26.099+00:00"},"s":"I",  "c":"INDEX",    "id":20447,   "ctx":"conn11","msg":"Index build: completed","attr":{"buildUUID":{"uuid":{"$uuid":"9ab74cf3-c906-4128-8954-fbd4f252f4da"}}}}
{"t":{"$date":"2024-04-09T02:26:26.105+00:00"},"s":"I",  "c":"COMMAND",  "id":51806,   "ctx":"conn11","msg":"CMD: dropIndexes","attr":{"namespace":"appsmith.application","uuid":{"uuid":{"$uuid":"ff48fded-c9f9-4807-a12d-a235fe9711f4"}},"indexes":"deleted"}}
{"t":{"$date":"2024-04-09T02:26:26.112+00:00"},"s":"I",  "c":"INDEX",    "id":20438,   "ctx":"conn11","msg":"Index build: registering","attr":{"buildUUID":{"uuid":{"$uuid":"a056b14b-0551-43b6-b96d-8ff3e49cfd63"}},"namespace":"appsmith.application","collectionUUID":{"uuid":{"$uuid":"ff48fded-c9f9-4807-a12d-a235fe9711f4"}},"indexes":1,"firstIndex":{"name":"deleted"}}}
{"t":{"$date":"2024-04-09T02:26:26.122+00:00"},"s":"I",  "c":"INDEX",    "id":20345,   "ctx":"conn11","msg":"Index build: done building","attr":{"buildUUID":null,"namespace":"appsmith.application","index":"deleted","commitTimestamp":{"$timestamp":{"t":1712629586,"i":7}}}}
{"t":{"$date":"2024-04-09T02:26:26.122+00:00"},"s":"I",  "c":"INDEX",    "id":20440,   "ctx":"conn11","msg":"Index build: waiting for index build to complete","attr":{"buildUUID":{"uuid":{"$uuid":"a056b14b-0551-43b6-b96d-8ff3e49cfd63"}},"deadline":{"$date":{"$numberLong":"9223372036854775807"}}}}
{"t":{"$date":"2024-04-09T02:26:26.123+00:00"},"s":"I",  "c":"INDEX",    "id":20447,   "ctx":"conn11","msg":"Index build: completed","attr":{"buildUUID":{"uuid":{"$uuid":"a056b14b-0551-43b6-b96d-8ff3e49cfd63"}}}}
{"t":{"$date":"2024-04-09T02:26:26.128+00:00"},"s":"I",  "c":"COMMAND",  "id":51806,   "ctx":"conn11","msg":"CMD: dropIndexes","attr":{"namespace":"appsmith.workspace","uuid":{"uuid":{"$uuid":"8f643824-f3e0-4603-8204-a1092fca887e"}},"indexes":"tenantId_deleted"}}
{"t":{"$date":"2024-04-09T02:26:26.134+00:00"},"s":"I",  "c":"INDEX",    "id":20438,   "ctx":"conn11","msg":"Index build: registering","attr":{"buildUUID":{"uuid":{"$uuid":"8f09e8bf-61d8-498a-92b8-293ab4d65aba"}},"namespace":"appsmith.workspace","collectionUUID":{"uuid":{"$uuid":"8f643824-f3e0-4603-8204-a1092fca887e"}},"indexes":1,"firstIndex":{"name":"tenantId_deleted"}}}
{"t":{"$date":"2024-04-09T02:26:26.144+00:00"},"s":"I",  "c":"INDEX",    "id":20345,   "ctx":"conn11","msg":"Index build: done building","attr":{"buildUUID":null,"namespace":"appsmith.workspace","index":"tenantId_deleted","commitTimestamp":{"$timestamp":{"t":1712629586,"i":8}}}}
{"t":{"$date":"2024-04-09T02:26:26.145+00:00"},"s":"I",  "c":"INDEX",    "id":20440,   "ctx":"conn11","msg":"Index build: waiting for index build to complete","attr":{"buildUUID":{"uuid":{"$uuid":"8f09e8bf-61d8-498a-92b8-293ab4d65aba"}},"deadline":{"$date":{"$numberLong":"9223372036854775807"}}}}
{"t":{"$date":"2024-04-09T02:26:26.145+00:00"},"s":"I",  "c":"INDEX",    "id":20447,   "ctx":"conn11","msg":"Index build: completed","attr":{"buildUUID":{"uuid":{"$uuid":"8f09e8bf-61d8-498a-92b8-293ab4d65aba"}}}}
{"t":{"$date":"2024-04-09T02:26:26.161+00:00"},"s":"I",  "c":"STORAGE",  "id":20320,   "ctx":"conn11","msg":"createCollection","attr":{"namespace":"appsmith.customJSLib","uuidDisposition":"generated","uuid":{"uuid":{"$uuid":"b20f9e09-74eb-47e5-88d6-ae87d796b263"}},"options":{}}}
{"t":{"$date":"2024-04-09T02:26:26.206+00:00"},"s":"I",  "c":"INDEX",    "id":20345,   "ctx":"conn11","msg":"Index build: done building","attr":{"buildUUID":null,"namespace":"appsmith.customJSLib","index":"_id_","commitTimestamp":{"$timestamp":{"t":1712629586,"i":11}}}}
{"t":{"$date":"2024-04-09T02:26:26.207+00:00"},"s":"I",  "c":"INDEX",    "id":20345,   "ctx":"conn11","msg":"Index build: done building","attr":{"buildUUID":null,"namespace":"appsmith.customJSLib","index":"customjslibs_uidstring_index","commitTimestamp":{"$timestamp":{"t":1712629586,"i":11}}}}
{"t":{"$date":"2024-04-09T02:26:26.453+00:00"},"s":"I",  "c":"STORAGE",  "id":20320,   "ctx":"conn11","msg":"createCollection","attr":{"namespace":"appsmith.applicationSnapshot","uuidDisposition":"generated","uuid":{"uuid":{"$uuid":"b74f22e8-6ede-46a6-ad6c-a2c2d4e73d5b"}},"options":{}}}
{"t":{"$date":"2024-04-09T02:26:26.500+00:00"},"s":"I",  "c":"INDEX",    "id":20345,   "ctx":"conn11","msg":"Index build: done building","attr":{"buildUUID":null,"namespace":"appsmith.applicationSnapshot","index":"_id_","commitTimestamp":{"$timestamp":{"t":1712629586,"i":27}}}}
{"t":{"$date":"2024-04-09T02:26:26.501+00:00"},"s":"I",  "c":"INDEX",    "id":20345,   "ctx":"conn11","msg":"Index build: done building","attr":{"buildUUID":null,"namespace":"appsmith.applicationSnapshot","index":"applicationId_chunkOrder_unique_index","commitTimestamp":{"$timestamp":{"t":1712629586,"i":27}}}}
{"t":{"$date":"2024-04-09T02:26:26.792+00:00"},"s":"I",  "c":"COMMAND",  "id":51806,   "ctx":"conn11","msg":"CMD: dropIndexes","attr":{"namespace":"appsmith.permissionGroup","uuid":{"uuid":{"$uuid":"5b9e43cc-277d-4622-af41-98746e3cbbc4"}},"indexes":"permission_group_workspace_deleted_compound_index"}}
{"t":{"$date":"2024-04-09T02:26:26.803+00:00"},"s":"I",  "c":"COMMAND",  "id":51806,   "ctx":"conn11","msg":"CMD: dropIndexes","attr":{"namespace":"appsmith.permissionGroup","uuid":{"uuid":{"$uuid":"5b9e43cc-277d-4622-af41-98746e3cbbc4"}},"indexes":"permission_group_domainId_domainType_deleted"}}
{"t":{"$date":"2024-04-09T02:26:26.811+00:00"},"s":"I",  "c":"INDEX",    "id":20438,   "ctx":"conn11","msg":"Index build: registering","attr":{"buildUUID":{"uuid":{"$uuid":"cad6aa24-3e4b-4c39-b33d-3e79adabee49"}},"namespace":"appsmith.permissionGroup","collectionUUID":{"uuid":{"$uuid":"5b9e43cc-277d-4622-af41-98746e3cbbc4"}},"indexes":1,"firstIndex":{"name":"permission_group_domainId_domainType_deleted"}}}
{"t":{"$date":"2024-04-09T02:26:26.841+00:00"},"s":"I",  "c":"INDEX",    "id":20384,   "ctx":"IndexBuildsCoordinatorMongod-0","msg":"Index build: starting","attr":{"buildUUID":{"uuid":{"$uuid":"cad6aa24-3e4b-4c39-b33d-3e79adabee49"}},"collectionUUID":{"uuid":{"$uuid":"5b9e43cc-277d-4622-af41-98746e3cbbc4"}},"namespace":"appsmith.permissionGroup","properties":{"v":2,"key":{"defaultDomainId":1,"defaultDomainType":1,"deleted":1,"deletedAt":1},"name":"permission_group_domainId_domainType_deleted"},"method":"Hybrid","maxTemporaryMemoryUsageMB":200}}
{"t":{"$date":"2024-04-09T02:26:26.842+00:00"},"s":"I",  "c":"INDEX",    "id":20346,   "ctx":"IndexBuildsCoordinatorMongod-0","msg":"Index build: initialized","attr":{"buildUUID":{"uuid":{"$uuid":"cad6aa24-3e4b-4c39-b33d-3e79adabee49"}},"collectionUUID":{"uuid":{"$uuid":"5b9e43cc-277d-4622-af41-98746e3cbbc4"}},"namespace":"appsmith.permissionGroup","initializationTimestamp":{"$timestamp":{"t":1712629586,"i":59}}}}
{"t":{"$date":"2024-04-09T02:26:26.844+00:00"},"s":"I",  "c":"STORAGE",  "id":4847600, "ctx":"IndexBuildsCoordinatorMongod-0","msg":"Index build: waiting for last optime before interceptors to be majority committed","attr":{"buildUUID":{"uuid":{"$uuid":"cad6aa24-3e4b-4c39-b33d-3e79adabee49"}},"collectionUUID":{"uuid":{"$uuid":"5b9e43cc-277d-4622-af41-98746e3cbbc4"}},"deadline":{"$date":"2024-04-09T02:26:36.843Z"},"timeoutMillis":10000,"lastOpTime":{"ts":{"$timestamp":{"t":1712629586,"i":59}},"t":2}}}
{"t":{"$date":"2024-04-09T02:26:26.844+00:00"},"s":"I",  "c":"INDEX",    "id":20440,   "ctx":"conn11","msg":"Index build: waiting for index build to complete","attr":{"buildUUID":{"uuid":{"$uuid":"cad6aa24-3e4b-4c39-b33d-3e79adabee49"}},"deadline":{"$date":{"$numberLong":"9223372036854775807"}}}}
{"t":{"$date":"2024-04-09T02:26:26.906+00:00"},"s":"I",  "c":"INDEX",    "id":20391,   "ctx":"IndexBuildsCoordinatorMongod-0","msg":"Index build: collection scan done","attr":{"buildUUID":{"uuid":{"$uuid":"cad6aa24-3e4b-4c39-b33d-3e79adabee49"}},"collectionUUID":{"uuid":{"$uuid":"5b9e43cc-277d-4622-af41-98746e3cbbc4"}},"namespace":"appsmith.permissionGroup","totalRecords":2,"readSource":"kMajorityCommitted","durationMillis":0}}
{"t":{"$date":"2024-04-09T02:26:26.914+00:00"},"s":"I",  "c":"INDEX",    "id":20685,   "ctx":"IndexBuildsCoordinatorMongod-0","msg":"Index build: inserted keys from external sorter into index","attr":{"namespace":"appsmith.permissionGroup","index":"permission_group_domainId_domainType_deleted","keysInserted":2,"durationMillis":0}}
{"t":{"$date":"2024-04-09T02:26:26.923+00:00"},"s":"I",  "c":"STORAGE",  "id":3856201, "ctx":"conn26","msg":"Index build: commit quorum satisfied","attr":{"indexBuildEntry":{"_id":{"$uuid":"cad6aa24-3e4b-4c39-b33d-3e79adabee49"},"collectionUUID":{"$uuid":"5b9e43cc-277d-4622-af41-98746e3cbbc4"},"commitQuorum":"votingMembers","indexNames":["permission_group_domainId_domainType_deleted"],"commitReadyMembers":["localhost:27017"]}}}
{"t":{"$date":"2024-04-09T02:26:26.924+00:00"},"s":"I",  "c":"CONNPOOL", "id":22566,   "ctx":"ReplNetwork","msg":"Ending connection due to bad connection status","attr":{"hostAndPort":"localhost:27017","error":"CallbackCanceled: Callback was canceled","numOpenConns":0}}
{"t":{"$date":"2024-04-09T02:26:26.924+00:00"},"s":"I",  "c":"STORAGE",  "id":3856203, "ctx":"IndexBuildsCoordinatorMongod-0","msg":"Index build: waiting for next action before completing final phase","attr":{"buildUUID":{"uuid":{"$uuid":"cad6aa24-3e4b-4c39-b33d-3e79adabee49"}}}}
{"t":{"$date":"2024-04-09T02:26:26.926+00:00"},"s":"I",  "c":"STORAGE",  "id":3856204, "ctx":"IndexBuildsCoordinatorMongod-0","msg":"Index build: received signal","attr":{"buildUUID":{"uuid":{"$uuid":"cad6aa24-3e4b-4c39-b33d-3e79adabee49"}},"action":"Commit quorum Satisfied"}}
{"t":{"$date":"2024-04-09T02:26:26.926+00:00"},"s":"I",  "c":"NETWORK",  "id":22944,   "ctx":"conn26","msg":"Connection ended","attr":{"remote":"127.0.0.1:36388","uuid":"4fec3ae2-4f63-4150-a2f8-d01145326c3f","connectionId":26,"connectionCount":6}}
{"t":{"$date":"2024-04-09T02:26:26.926+00:00"},"s":"I",  "c":"NETWORK",  "id":22943,   "ctx":"listener","msg":"Connection accepted","attr":{"remote":"127.0.0.1:36392","uuid":"f2bf0733-c98b-48ed-b27a-0e1e664c71ff","connectionId":29,"connectionCount":7}}
{"t":{"$date":"2024-04-09T02:26:26.927+00:00"},"s":"I",  "c":"INDEX",    "id":20345,   "ctx":"IndexBuildsCoordinatorMongod-0","msg":"Index build: done building","attr":{"buildUUID":{"uuid":{"$uuid":"cad6aa24-3e4b-4c39-b33d-3e79adabee49"}},"namespace":"appsmith.permissionGroup","index":"permission_group_domainId_domainType_deleted","commitTimestamp":{"$timestamp":{"t":1712629586,"i":61}}}}
{"t":{"$date":"2024-04-09T02:26:26.928+00:00"},"s":"I",  "c":"NETWORK",  "id":51800,   "ctx":"conn29","msg":"client metadata","attr":{"remote":"127.0.0.1:36392","client":"conn29","negotiatedCompressors":["snappy","zstd","zlib"],"doc":{"driver":{"name":"NetworkInterfaceTL","version":"5.0.26"},"os":{"type":"Linux","name":"Ubuntu","architecture":"x86_64","version":"20.04"}}}}
{"t":{"$date":"2024-04-09T02:26:26.930+00:00"},"s":"I",  "c":"ACCESS",   "id":20250,   "ctx":"conn29","msg":"Authentication succeeded","attr":{"mechanism":"SCRAM-SHA-256","speculative":true,"principalName":"__system","authenticationDatabase":"local","remote":"127.0.0.1:36392","extraInfo":{}}}
{"t":{"$date":"2024-04-09T02:26:26.931+00:00"},"s":"I",  "c":"STORAGE",  "id":20663,   "ctx":"IndexBuildsCoordinatorMongod-0","msg":"Index build: completed successfully","attr":{"buildUUID":{"uuid":{"$uuid":"cad6aa24-3e4b-4c39-b33d-3e79adabee49"}},"collectionUUID":{"uuid":{"$uuid":"5b9e43cc-277d-4622-af41-98746e3cbbc4"}},"namespace":"appsmith.permissionGroup","indexesBuilt":["permission_group_domainId_domainType_deleted"],"numIndexesBefore":2,"numIndexesAfter":3}}
{"t":{"$date":"2024-04-09T02:26:26.933+00:00"},"s":"I",  "c":"INDEX",    "id":20447,   "ctx":"conn11","msg":"Index build: completed","attr":{"buildUUID":{"uuid":{"$uuid":"cad6aa24-3e4b-4c39-b33d-3e79adabee49"}}}}
{"t":{"$date":"2024-04-09T02:26:26.936+00:00"},"s":"I",  "c":"COMMAND",  "id":51803,   "ctx":"conn11","msg":"Slow query","attr":{"type":"command","ns":"appsmith.permissionGroup","command":{"createIndexes":"permissionGroup","indexes":[{"key":{"defaultDomainId":1,"defaultDomainType":1,"deleted":1,"deletedAt":1},"name":"permission_group_domainId_domainType_deleted"}],"$db":"appsmith","$clusterTime":{"clusterTime":{"$timestamp":{"t":1712629586,"i":57}},"signature":{"hash":{"$binary":{"base64":"qyoc8Cd9+x3cjz7oN4abqwSj/+A=","subType":"0"}},"keyId":7355687795744047109}},"lsid":{"id":{"$uuid":"6083b346-52d2-4c7c-8d49-0fad1e364fcc"}},"$readPreference":{"mode":"primaryPreferred"}},"numYields":0,"reslen":271,"locks":{"ParallelBatchWriterMode":{"acquireCount":{"r":3}},"FeatureCompatibilityVersion":{"acquireCount":{"r":4,"w":2}},"ReplicationStateTransition":{"acquireCount":{"w":6}},"Global":{"acquireCount":{"r":4,"w":2}},"Database":{"acquireCount":{"r":2,"w":1}},"Collection":{"acquireCount":{"r":2,"W":1}},"Mutex":{"acquireCount":{"r":3}}},"flowControl":{"acquireCount":2,"timeAcquiringMicros":3},"readConcern":{"level":"local","provenance":"implicitDefault"},"writeConcern":{"w":"majority","wtimeout":0,"provenance":"implicitDefault"},"waitForWriteConcernDurationMillis":1,"storage":{},"remote":"127.0.0.1:60858","protocol":"op_msg","durationMillis":125}}
{"t":{"$date":"2024-04-09T02:26:26.962+00:00"},"s":"I",  "c":"STORAGE",  "id":20320,   "ctx":"conn11","msg":"createCollection","attr":{"namespace":"appsmith.datasourceStorageStructure","uuidDisposition":"generated","uuid":{"uuid":{"$uuid":"735ca011-92b0-458a-9110-ab41beec7762"}},"options":{}}}
{"t":{"$date":"2024-04-09T02:26:26.988+00:00"},"s":"I",  "c":"INDEX",    "id":20345,   "ctx":"conn11","msg":"Index build: done building","attr":{"buildUUID":null,"namespace":"appsmith.datasourceStorageStructure","index":"_id_","commitTimestamp":{"$timestamp":{"t":1712629586,"i":65}}}}
{"t":{"$date":"2024-04-09T02:26:26.989+00:00"},"s":"I",  "c":"INDEX",    "id":20345,   "ctx":"conn11","msg":"Index build: done building","attr":{"buildUUID":null,"namespace":"appsmith.datasourceStorageStructure","index":"dsConfigStructure_dsId_envId","commitTimestamp":{"$timestamp":{"t":1712629586,"i":65}}}}
{"t":{"$date":"2024-04-09T02:26:27.034+00:00"},"s":"I",  "c":"STORAGE",  "id":20320,   "ctx":"conn11","msg":"createCollection","attr":{"namespace":"appsmith.datasourceStorage","uuidDisposition":"generated","uuid":{"uuid":{"$uuid":"cc649aad-623d-45a9-88a4-10d2d75c7af2"}},"options":{}}}
{"t":{"$date":"2024-04-09T02:26:27.063+00:00"},"s":"I",  "c":"INDEX",    "id":20345,   "ctx":"conn11","msg":"Index build: done building","attr":{"buildUUID":null,"namespace":"appsmith.datasourceStorage","index":"_id_","commitTimestamp":{"$timestamp":{"t":1712629587,"i":4}}}}
{"t":{"$date":"2024-04-09T02:26:27.064+00:00"},"s":"I",  "c":"INDEX",    "id":20345,   "ctx":"conn11","msg":"Index build: done building","attr":{"buildUUID":null,"namespace":"appsmith.datasourceStorage","index":"datasource_storage_compound_index","commitTimestamp":{"$timestamp":{"t":1712629587,"i":4}}}}
{"t":{"$date":"2024-04-09T02:26:27.075+00:00"},"s":"I",  "c":"INDEX",    "id":20438,   "ctx":"conn11","msg":"Index build: registering","attr":{"buildUUID":{"uuid":{"$uuid":"b78328e9-240b-4c0b-8e23-666e82f93f9d"}},"namespace":"appsmith.newAction","collectionUUID":{"uuid":{"$uuid":"c6875e5c-1551-4284-9cea-91504a3940de"}},"indexes":1,"firstIndex":{"name":"applicationId_pluginType_deletedAt_compound_index"}}}
{"t":{"$date":"2024-04-09T02:26:27.085+00:00"},"s":"I",  "c":"INDEX",    "id":20345,   "ctx":"conn11","msg":"Index build: done building","attr":{"buildUUID":null,"namespace":"appsmith.newAction","index":"applicationId_pluginType_deletedAt_compound_index","commitTimestamp":{"$timestamp":{"t":1712629587,"i":6}}}}
{"t":{"$date":"2024-04-09T02:26:27.086+00:00"},"s":"I",  "c":"INDEX",    "id":20440,   "ctx":"conn11","msg":"Index build: waiting for index build to complete","attr":{"buildUUID":{"uuid":{"$uuid":"b78328e9-240b-4c0b-8e23-666e82f93f9d"}},"deadline":{"$date":{"$numberLong":"9223372036854775807"}}}}
{"t":{"$date":"2024-04-09T02:26:27.086+00:00"},"s":"I",  "c":"INDEX",    "id":20447,   "ctx":"conn11","msg":"Index build: completed","attr":{"buildUUID":{"uuid":{"$uuid":"b78328e9-240b-4c0b-8e23-666e82f93f9d"}}}}
{"t":{"$date":"2024-04-09T02:26:27.097+00:00"},"s":"I",  "c":"COMMAND",  "id":51806,   "ctx":"conn11","msg":"CMD: dropIndexes","attr":{"namespace":"appsmith.application","uuid":{"uuid":{"$uuid":"ff48fded-c9f9-4807-a12d-a235fe9711f4"}},"indexes":"defaultCollectionId_branchName_deleted_compound_index"}}
{"t":{"$date":"2024-04-09T02:26:27.107+00:00"},"s":"I",  "c":"COMMAND",  "id":51806,   "ctx":"conn11","msg":"CMD: dropIndexes","attr":{"namespace":"appsmith.application","uuid":{"uuid":{"$uuid":"ff48fded-c9f9-4807-a12d-a235fe9711f4"}},"indexes":"defaultApplicationId_branchName_deleted_compound_index"}}
{"t":{"$date":"2024-04-09T02:26:27.112+00:00"},"s":"I",  "c":"COMMAND",  "id":51806,   "ctx":"conn11","msg":"CMD: dropIndexes","attr":{"namespace":"appsmith.actionCollection","uuid":{"uuid":{"$uuid":"9f67c6b7-6ddd-4347-8cb9-7467c42fe4fd"}},"indexes":"unpublishedCollectionPageId_deleted_compound_index"}}
{"t":{"$date":"2024-04-09T02:26:27.116+00:00"},"s":"I",  "c":"COMMAND",  "id":51806,   "ctx":"conn11","msg":"CMD: dropIndexes","attr":{"namespace":"appsmith.actionCollection","uuid":{"uuid":{"$uuid":"9f67c6b7-6ddd-4347-8cb9-7467c42fe4fd"}},"indexes":"publishedCollectionPageId_deleted_compound_index"}}
{"t":{"$date":"2024-04-09T02:26:27.120+00:00"},"s":"I",  "c":"COMMAND",  "id":51806,   "ctx":"conn11","msg":"CMD: dropIndexes","attr":{"namespace":"appsmith.actionCollection","uuid":{"uuid":{"$uuid":"9f67c6b7-6ddd-4347-8cb9-7467c42fe4fd"}},"indexes":"defaultApplicationId_gitSyncId_deleted_compound_index"}}
{"t":{"$date":"2024-04-09T02:26:27.125+00:00"},"s":"I",  "c":"COMMAND",  "id":51806,   "ctx":"conn11","msg":"CMD: dropIndexes","attr":{"namespace":"appsmith.newAction","uuid":{"uuid":{"$uuid":"c6875e5c-1551-4284-9cea-91504a3940de"}},"indexes":"defaultApplicationId_gitSyncId_deleted_compound_index"}}
{"t":{"$date":"2024-04-09T02:26:27.128+00:00"},"s":"I",  "c":"COMMAND",  "id":51806,   "ctx":"conn11","msg":"CMD: dropIndexes","attr":{"namespace":"appsmith.newPage","uuid":{"uuid":{"$uuid":"4f297a9a-e5a8-4320-a4f9-b107584842d5"}},"indexes":"defaultApplicationId_gitSyncId_deleted_compound_index"}}
{"t":{"$date":"2024-04-09T02:26:27.141+00:00"},"s":"I",  "c":"COMMAND",  "id":51806,   "ctx":"conn11","msg":"CMD: dropIndexes","attr":{"namespace":"appsmith.application","uuid":{"uuid":{"$uuid":"ff48fded-c9f9-4807-a12d-a235fe9711f4"}},"indexes":"workspace_application_deleted_gitApplicationMetadata_compound_index"}}
{"t":{"$date":"2024-04-09T02:26:27.144+00:00"},"s":"I",  "c":"COMMAND",  "id":51806,   "ctx":"conn11","msg":"CMD: dropIndexes","attr":{"namespace":"appsmith.permissionGroup","uuid":{"uuid":{"$uuid":"5b9e43cc-277d-4622-af41-98746e3cbbc4"}},"indexes":"permission_group_workspace_deleted_compound_index"}}
{"t":{"$date":"2024-04-09T02:26:27.148+00:00"},"s":"I",  "c":"COMMAND",  "id":51806,   "ctx":"conn11","msg":"CMD: dropIndexes","attr":{"namespace":"appsmith.permissionGroup","uuid":{"uuid":{"$uuid":"5b9e43cc-277d-4622-af41-98746e3cbbc4"}},"indexes":"permission_group_assignedUserIds_deleted_compound_index"}}
{"t":{"$date":"2024-04-09T02:26:27.151+00:00"},"s":"I",  "c":"COMMAND",  "id":51806,   "ctx":"conn11","msg":"CMD: dropIndexes","attr":{"namespace":"appsmith.permissionGroup","uuid":{"uuid":{"$uuid":"5b9e43cc-277d-4622-af41-98746e3cbbc4"}},"indexes":"permission_group_assignedUserIds_deleted"}}
{"t":{"$date":"2024-04-09T02:26:27.152+00:00"},"s":"I",  "c":"STORAGE",  "id":22206,   "ctx":"conn11","msg":"Deferring table drop for index","attr":{"index":"permission_group_assignedUserIds_deleted","namespace":"appsmith.permissionGroup","uuid":{"uuid":{"$uuid":"5b9e43cc-277d-4622-af41-98746e3cbbc4"}},"ident":"index-95--7653259994859265256","dropTime":{"":{"$timestamp":{"t":1712629587,"i":8}}}}}
{"t":{"$date":"2024-04-09T02:26:27.158+00:00"},"s":"I",  "c":"INDEX",    "id":20438,   "ctx":"conn11","msg":"Index build: registering","attr":{"buildUUID":{"uuid":{"$uuid":"54f74795-2d4a-4a52-89b2-6a39a8ea3314"}},"namespace":"appsmith.permissionGroup","collectionUUID":{"uuid":{"$uuid":"5b9e43cc-277d-4622-af41-98746e3cbbc4"}},"indexes":1,"firstIndex":{"name":"permission_group_assignedUserIds_deleted"}}}
{"t":{"$date":"2024-04-09T02:26:27.177+00:00"},"s":"I",  "c":"INDEX",    "id":20384,   "ctx":"IndexBuildsCoordinatorMongod-0","msg":"Index build: starting","attr":{"buildUUID":{"uuid":{"$uuid":"54f74795-2d4a-4a52-89b2-6a39a8ea3314"}},"collectionUUID":{"uuid":{"$uuid":"5b9e43cc-277d-4622-af41-98746e3cbbc4"}},"namespace":"appsmith.permissionGroup","properties":{"v":2,"key":{"assignedToUserIds":1,"deleted":1},"name":"permission_group_assignedUserIds_deleted"},"method":"Hybrid","maxTemporaryMemoryUsageMB":200}}
{"t":{"$date":"2024-04-09T02:26:27.177+00:00"},"s":"I",  "c":"INDEX",    "id":20346,   "ctx":"IndexBuildsCoordinatorMongod-0","msg":"Index build: initialized","attr":{"buildUUID":{"uuid":{"$uuid":"54f74795-2d4a-4a52-89b2-6a39a8ea3314"}},"collectionUUID":{"uuid":{"$uuid":"5b9e43cc-277d-4622-af41-98746e3cbbc4"}},"namespace":"appsmith.permissionGroup","initializationTimestamp":{"$timestamp":{"t":1712629587,"i":10}}}}
{"t":{"$date":"2024-04-09T02:26:27.178+00:00"},"s":"I",  "c":"STORAGE",  "id":4847600, "ctx":"IndexBuildsCoordinatorMongod-0","msg":"Index build: waiting for last optime before interceptors to be majority committed","attr":{"buildUUID":{"uuid":{"$uuid":"54f74795-2d4a-4a52-89b2-6a39a8ea3314"}},"collectionUUID":{"uuid":{"$uuid":"5b9e43cc-277d-4622-af41-98746e3cbbc4"}},"deadline":{"$date":"2024-04-09T02:26:37.178Z"},"timeoutMillis":10000,"lastOpTime":{"ts":{"$timestamp":{"t":1712629587,"i":10}},"t":2}}}
{"t":{"$date":"2024-04-09T02:26:27.178+00:00"},"s":"I",  "c":"INDEX",    "id":20440,   "ctx":"conn11","msg":"Index build: waiting for index build to complete","attr":{"buildUUID":{"uuid":{"$uuid":"54f74795-2d4a-4a52-89b2-6a39a8ea3314"}},"deadline":{"$date":{"$numberLong":"9223372036854775807"}}}}
{"t":{"$date":"2024-04-09T02:26:27.256+00:00"},"s":"I",  "c":"INDEX",    "id":20391,   "ctx":"IndexBuildsCoordinatorMongod-0","msg":"Index build: collection scan done","attr":{"buildUUID":{"uuid":{"$uuid":"54f74795-2d4a-4a52-89b2-6a39a8ea3314"}},"collectionUUID":{"uuid":{"$uuid":"5b9e43cc-277d-4622-af41-98746e3cbbc4"}},"namespace":"appsmith.permissionGroup","totalRecords":2,"readSource":"kMajorityCommitted","durationMillis":0}}
{"t":{"$date":"2024-04-09T02:26:27.264+00:00"},"s":"I",  "c":"INDEX",    "id":20685,   "ctx":"IndexBuildsCoordinatorMongod-0","msg":"Index build: inserted keys from external sorter into index","attr":{"namespace":"appsmith.permissionGroup","index":"permission_group_assignedUserIds_deleted","keysInserted":2,"durationMillis":0}}
{"t":{"$date":"2024-04-09T02:26:27.272+00:00"},"s":"I",  "c":"STORAGE",  "id":3856201, "ctx":"conn29","msg":"Index build: commit quorum satisfied","attr":{"indexBuildEntry":{"_id":{"$uuid":"54f74795-2d4a-4a52-89b2-6a39a8ea3314"},"collectionUUID":{"$uuid":"5b9e43cc-277d-4622-af41-98746e3cbbc4"},"commitQuorum":"votingMembers","indexNames":["permission_group_assignedUserIds_deleted"],"commitReadyMembers":["localhost:27017"]}}}
{"t":{"$date":"2024-04-09T02:26:27.272+00:00"},"s":"I",  "c":"CONNPOOL", "id":22566,   "ctx":"ReplNetwork","msg":"Ending connection due to bad connection status","attr":{"hostAndPort":"localhost:27017","error":"CallbackCanceled: Callback was canceled","numOpenConns":0}}
{"t":{"$date":"2024-04-09T02:26:27.273+00:00"},"s":"I",  "c":"STORAGE",  "id":3856203, "ctx":"IndexBuildsCoordinatorMongod-0","msg":"Index build: waiting for next action before completing final phase","attr":{"buildUUID":{"uuid":{"$uuid":"54f74795-2d4a-4a52-89b2-6a39a8ea3314"}}}}
{"t":{"$date":"2024-04-09T02:26:27.274+00:00"},"s":"I",  "c":"CONNPOOL", "id":22576,   "ctx":"ReplNetwork","msg":"Connecting","attr":{"hostAndPort":"localhost:27017"}}
{"t":{"$date":"2024-04-09T02:26:27.275+00:00"},"s":"I",  "c":"NETWORK",  "id":22944,   "ctx":"conn29","msg":"Connection ended","attr":{"remote":"127.0.0.1:36392","uuid":"f2bf0733-c98b-48ed-b27a-0e1e664c71ff","connectionId":29,"connectionCount":5}}
{"t":{"$date":"2024-04-09T02:26:27.276+00:00"},"s":"I",  "c":"NETWORK",  "id":22943,   "ctx":"listener","msg":"Connection accepted","attr":{"remote":"127.0.0.1:36408","uuid":"165872f0-0703-4713-848a-d6123f34068c","connectionId":31,"connectionCount":6}}
{"t":{"$date":"2024-04-09T02:26:27.274+00:00"},"s":"I",  "c":"STORAGE",  "id":3856204, "ctx":"IndexBuildsCoordinatorMongod-0","msg":"Index build: received signal","attr":{"buildUUID":{"uuid":{"$uuid":"54f74795-2d4a-4a52-89b2-6a39a8ea3314"}},"action":"Commit quorum Satisfied"}}
{"t":{"$date":"2024-04-09T02:26:27.277+00:00"},"s":"I",  "c":"NETWORK",  "id":51800,   "ctx":"conn31","msg":"client metadata","attr":{"remote":"127.0.0.1:36408","client":"conn31","negotiatedCompressors":["snappy","zstd","zlib"],"doc":{"driver":{"name":"NetworkInterfaceTL","version":"5.0.26"},"os":{"type":"Linux","name":"Ubuntu","architecture":"x86_64","version":"20.04"}}}}
{"t":{"$date":"2024-04-09T02:26:27.278+00:00"},"s":"I",  "c":"INDEX",    "id":20345,   "ctx":"IndexBuildsCoordinatorMongod-0","msg":"Index build: done building","attr":{"buildUUID":{"uuid":{"$uuid":"54f74795-2d4a-4a52-89b2-6a39a8ea3314"}},"namespace":"appsmith.permissionGroup","index":"permission_group_assignedUserIds_deleted","commitTimestamp":{"$timestamp":{"t":1712629587,"i":12}}}}
{"t":{"$date":"2024-04-09T02:26:27.280+00:00"},"s":"I",  "c":"ACCESS",   "id":20250,   "ctx":"conn31","msg":"Authentication succeeded","attr":{"mechanism":"SCRAM-SHA-256","speculative":true,"principalName":"__system","authenticationDatabase":"local","remote":"127.0.0.1:36408","extraInfo":{}}}
{"t":{"$date":"2024-04-09T02:26:27.282+00:00"},"s":"I",  "c":"STORAGE",  "id":20663,   "ctx":"IndexBuildsCoordinatorMongod-0","msg":"Index build: completed successfully","attr":{"buildUUID":{"uuid":{"$uuid":"54f74795-2d4a-4a52-89b2-6a39a8ea3314"}},"collectionUUID":{"uuid":{"$uuid":"5b9e43cc-277d-4622-af41-98746e3cbbc4"}},"namespace":"appsmith.permissionGroup","indexesBuilt":["permission_group_assignedUserIds_deleted"],"numIndexesBefore":2,"numIndexesAfter":3}}
{"t":{"$date":"2024-04-09T02:26:27.283+00:00"},"s":"I",  "c":"INDEX",    "id":20447,   "ctx":"conn11","msg":"Index build: completed","attr":{"buildUUID":{"uuid":{"$uuid":"54f74795-2d4a-4a52-89b2-6a39a8ea3314"}}}}
{"t":{"$date":"2024-04-09T02:26:27.285+00:00"},"s":"I",  "c":"COMMAND",  "id":51803,   "ctx":"conn11","msg":"Slow query","attr":{"type":"command","ns":"appsmith.permissionGroup","command":{"createIndexes":"permissionGroup","indexes":[{"key":{"assignedToUserIds":1,"deleted":1},"name":"permission_group_assignedUserIds_deleted"}],"$db":"appsmith","$clusterTime":{"clusterTime":{"$timestamp":{"t":1712629587,"i":8}},"signature":{"hash":{"$binary":{"base64":"DTlC1Auzm0aLeED8Bx1W+U/xcCo=","subType":"0"}},"keyId":7355687795744047109}},"lsid":{"id":{"$uuid":"6083b346-52d2-4c7c-8d49-0fad1e364fcc"}},"$readPreference":{"mode":"primaryPreferred"}},"numYields":0,"reslen":271,"locks":{"ParallelBatchWriterMode":{"acquireCount":{"r":3}},"FeatureCompatibilityVersion":{"acquireCount":{"r":4,"w":2}},"ReplicationStateTransition":{"acquireCount":{"w":6}},"Global":{"acquireCount":{"r":4,"w":2}},"Database":{"acquireCount":{"r":2,"w":1}},"Collection":{"acquireCount":{"r":2,"W":1}},"Mutex":{"acquireCount":{"r":3}}},"flowControl":{"acquireCount":2,"timeAcquiringMicros":2},"readConcern":{"level":"local","provenance":"implicitDefault"},"writeConcern":{"w":"majority","wtimeout":0,"provenance":"implicitDefault"},"waitForWriteConcernDurationMillis":1,"storage":{},"remote":"127.0.0.1:60858","protocol":"op_msg","durationMillis":126}}
{"t":{"$date":"2024-04-09T02:26:27.288+00:00"},"s":"I",  "c":"COMMAND",  "id":51806,   "ctx":"conn11","msg":"CMD: dropIndexes","attr":{"namespace":"appsmith.permissionGroup","uuid":{"uuid":{"$uuid":"5b9e43cc-277d-4622-af41-98746e3cbbc4"}},"indexes":"permission_group_domainId_domainType_deleted_deleted_compound_index"}}
{"t":{"$date":"2024-04-09T02:26:27.295+00:00"},"s":"I",  "c":"COMMAND",  "id":51806,   "ctx":"conn11","msg":"CMD: dropIndexes","attr":{"namespace":"appsmith.datasourceStorageStructure","uuid":{"uuid":{"$uuid":"735ca011-92b0-458a-9110-ab41beec7762"}},"indexes":"dsConfigStructure_datasourceId_envId_compound_index"}}
{"t":{"$date":"2024-04-09T02:26:27.433+00:00"},"s":"I",  "c":"COMMAND",  "id":51806,   "ctx":"conn11","msg":"CMD: dropIndexes","attr":{"namespace":"appsmith.application","uuid":{"uuid":{"$uuid":"ff48fded-c9f9-4807-a12d-a235fe9711f4"}},"indexes":"deleted_compound_index"}}
{"t":{"$date":"2024-04-09T02:26:27.439+00:00"},"s":"I",  "c":"INDEX",    "id":20438,   "ctx":"conn11","msg":"Index build: registering","attr":{"buildUUID":{"uuid":{"$uuid":"1e13f9f6-3de1-47e6-bf47-ee1423d3687c"}},"namespace":"appsmith.application","collectionUUID":{"uuid":{"$uuid":"ff48fded-c9f9-4807-a12d-a235fe9711f4"}},"indexes":1,"firstIndex":{"name":"deleted_compound_index"}}}
{"t":{"$date":"2024-04-09T02:26:27.447+00:00"},"s":"I",  "c":"INDEX",    "id":20345,   "ctx":"conn11","msg":"Index build: done building","attr":{"buildUUID":null,"namespace":"appsmith.application","index":"deleted_compound_index","commitTimestamp":{"$timestamp":{"t":1712629587,"i":25}}}}
{"t":{"$date":"2024-04-09T02:26:27.448+00:00"},"s":"I",  "c":"INDEX",    "id":20440,   "ctx":"conn11","msg":"Index build: waiting for index build to complete","attr":{"buildUUID":{"uuid":{"$uuid":"1e13f9f6-3de1-47e6-bf47-ee1423d3687c"}},"deadline":{"$date":{"$numberLong":"9223372036854775807"}}}}
{"t":{"$date":"2024-04-09T02:26:27.448+00:00"},"s":"I",  "c":"INDEX",    "id":20447,   "ctx":"conn11","msg":"Index build: completed","attr":{"buildUUID":{"uuid":{"$uuid":"1e13f9f6-3de1-47e6-bf47-ee1423d3687c"}}}}
{"t":{"$date":"2024-04-09T02:26:27.478+00:00"},"s":"I",  "c":"COMMAND",  "id":51806,   "ctx":"conn11","msg":"CMD: dropIndexes","attr":{"namespace":"appsmith.workspace","uuid":{"uuid":{"$uuid":"8f643824-f3e0-4603-8204-a1092fca887e"}},"indexes":"tenantId_deleted_compound_index"}}
{"t":{"$date":"2024-04-09T02:26:27.483+00:00"},"s":"I",  "c":"INDEX",    "id":20438,   "ctx":"conn11","msg":"Index build: registering","attr":{"buildUUID":{"uuid":{"$uuid":"fc4cbf7c-1755-4c8a-91f4-c373b210844a"}},"namespace":"appsmith.workspace","collectionUUID":{"uuid":{"$uuid":"8f643824-f3e0-4603-8204-a1092fca887e"}},"indexes":1,"firstIndex":{"name":"tenantId_deleted_compound_index"}}}
{"t":{"$date":"2024-04-09T02:26:27.489+00:00"},"s":"I",  "c":"INDEX",    "id":20345,   "ctx":"conn11","msg":"Index build: done building","attr":{"buildUUID":null,"namespace":"appsmith.workspace","index":"tenantId_deleted_compound_index","commitTimestamp":{"$timestamp":{"t":1712629587,"i":28}}}}
{"t":{"$date":"2024-04-09T02:26:27.490+00:00"},"s":"I",  "c":"INDEX",    "id":20440,   "ctx":"conn11","msg":"Index build: waiting for index build to complete","attr":{"buildUUID":{"uuid":{"$uuid":"fc4cbf7c-1755-4c8a-91f4-c373b210844a"}},"deadline":{"$date":{"$numberLong":"9223372036854775807"}}}}
{"t":{"$date":"2024-04-09T02:26:27.490+00:00"},"s":"I",  "c":"INDEX",    "id":20447,   "ctx":"conn11","msg":"Index build: completed","attr":{"buildUUID":{"uuid":{"$uuid":"fc4cbf7c-1755-4c8a-91f4-c373b210844a"}}}}
{"t":{"$date":"2024-04-09T02:26:27.503+00:00"},"s":"I",  "c":"INDEX",    "id":20438,   "ctx":"conn11","msg":"Index build: registering","attr":{"buildUUID":{"uuid":{"$uuid":"3b94a7fc-ba16-49c8-9396-cea8127e03a1"}},"namespace":"appsmith.newAction","collectionUUID":{"uuid":{"$uuid":"c6875e5c-1551-4284-9cea-91504a3940de"}},"indexes":1,"firstIndex":{"name":"publishedAction_datasourceId_deleted_compound_index"}}}
{"t":{"$date":"2024-04-09T02:26:27.512+00:00"},"s":"I",  "c":"INDEX",    "id":20345,   "ctx":"conn11","msg":"Index build: done building","attr":{"buildUUID":null,"namespace":"appsmith.newAction","index":"publishedAction_datasourceId_deleted_compound_index","commitTimestamp":{"$timestamp":{"t":1712629587,"i":30}}}}
{"t":{"$date":"2024-04-09T02:26:27.513+00:00"},"s":"I",  "c":"INDEX",    "id":20440,   "ctx":"conn11","msg":"Index build: waiting for index build to complete","attr":{"buildUUID":{"uuid":{"$uuid":"3b94a7fc-ba16-49c8-9396-cea8127e03a1"}},"deadline":{"$date":{"$numberLong":"9223372036854775807"}}}}
{"t":{"$date":"2024-04-09T02:26:27.513+00:00"},"s":"I",  "c":"INDEX",    "id":20447,   "ctx":"conn11","msg":"Index build: completed","attr":{"buildUUID":{"uuid":{"$uuid":"3b94a7fc-ba16-49c8-9396-cea8127e03a1"}}}}
{"t":{"$date":"2024-04-09T02:26:27.520+00:00"},"s":"I",  "c":"INDEX",    "id":20438,   "ctx":"conn11","msg":"Index build: registering","attr":{"buildUUID":{"uuid":{"$uuid":"464d9217-ec11-4556-ab37-ad7593b20219"}},"namespace":"appsmith.newAction","collectionUUID":{"uuid":{"$uuid":"c6875e5c-1551-4284-9cea-91504a3940de"}},"indexes":1,"firstIndex":{"name":"unpublishedAction_datasourceId_deleted_compound_index"}}}
{"t":{"$date":"2024-04-09T02:26:27.529+00:00"},"s":"I",  "c":"INDEX",    "id":20345,   "ctx":"conn11","msg":"Index build: done building","attr":{"buildUUID":null,"namespace":"appsmith.newAction","index":"unpublishedAction_datasourceId_deleted_compound_index","commitTimestamp":{"$timestamp":{"t":1712629587,"i":31}}}}
{"t":{"$date":"2024-04-09T02:26:27.530+00:00"},"s":"I",  "c":"INDEX",    "id":20440,   "ctx":"conn11","msg":"Index build: waiting for index build to complete","attr":{"buildUUID":{"uuid":{"$uuid":"464d9217-ec11-4556-ab37-ad7593b20219"}},"deadline":{"$date":{"$numberLong":"9223372036854775807"}}}}
{"t":{"$date":"2024-04-09T02:26:27.530+00:00"},"s":"I",  "c":"INDEX",    "id":20447,   "ctx":"conn11","msg":"Index build: completed","attr":{"buildUUID":{"uuid":{"$uuid":"464d9217-ec11-4556-ab37-ad7593b20219"}}}}
{"t":{"$date":"2024-04-09T02:26:27.757+00:00"},"s":"I",  "c":"COMMAND",  "id":51806,   "ctx":"conn11","msg":"CMD: dropIndexes","attr":{"namespace":"appsmith.workspace","uuid":{"uuid":{"$uuid":"8f643824-f3e0-4603-8204-a1092fca887e"}},"indexes":"name_deleted_compound_index"}}
{"t":{"$date":"2024-04-09T02:26:27.764+00:00"},"s":"I",  "c":"INDEX",    "id":20438,   "ctx":"conn11","msg":"Index build: registering","attr":{"buildUUID":{"uuid":{"$uuid":"e4a55986-bcc1-4036-89e1-2a56ab4a163a"}},"namespace":"appsmith.workspace","collectionUUID":{"uuid":{"$uuid":"8f643824-f3e0-4603-8204-a1092fca887e"}},"indexes":1,"firstIndex":{"name":"name_deleted_compound_index"}}}
{"t":{"$date":"2024-04-09T02:26:27.776+00:00"},"s":"I",  "c":"INDEX",    "id":20345,   "ctx":"conn11","msg":"Index build: done building","attr":{"buildUUID":null,"namespace":"appsmith.workspace","index":"name_deleted_compound_index","commitTimestamp":{"$timestamp":{"t":1712629587,"i":47}}}}
{"t":{"$date":"2024-04-09T02:26:27.777+00:00"},"s":"I",  "c":"INDEX",    "id":20440,   "ctx":"conn11","msg":"Index build: waiting for index build to complete","attr":{"buildUUID":{"uuid":{"$uuid":"e4a55986-bcc1-4036-89e1-2a56ab4a163a"}},"deadline":{"$date":{"$numberLong":"9223372036854775807"}}}}
{"t":{"$date":"2024-04-09T02:26:27.778+00:00"},"s":"I",  "c":"INDEX",    "id":20447,   "ctx":"conn11","msg":"Index build: completed","attr":{"buildUUID":{"uuid":{"$uuid":"e4a55986-bcc1-4036-89e1-2a56ab4a163a"}}}}
{"t":{"$date":"2024-04-09T02:26:27.782+00:00"},"s":"I",  "c":"COMMAND",  "id":51806,   "ctx":"conn11","msg":"CMD: dropIndexes","attr":{"namespace":"appsmith.application","uuid":{"uuid":{"$uuid":"ff48fded-c9f9-4807-a12d-a235fe9711f4"}},"indexes":"name_deleted_compound_index"}}
{"t":{"$date":"2024-04-09T02:26:27.787+00:00"},"s":"I",  "c":"INDEX",    "id":20438,   "ctx":"conn11","msg":"Index build: registering","attr":{"buildUUID":{"uuid":{"$uuid":"1372005d-b7cf-482d-bac7-6ab406247c44"}},"namespace":"appsmith.application","collectionUUID":{"uuid":{"$uuid":"ff48fded-c9f9-4807-a12d-a235fe9711f4"}},"indexes":1,"firstIndex":{"name":"name_deleted_compound_index"}}}
{"t":{"$date":"2024-04-09T02:26:27.799+00:00"},"s":"I",  "c":"INDEX",    "id":20345,   "ctx":"conn11","msg":"Index build: done building","attr":{"buildUUID":null,"namespace":"appsmith.application","index":"name_deleted_compound_index","commitTimestamp":{"$timestamp":{"t":1712629587,"i":48}}}}
{"t":{"$date":"2024-04-09T02:26:27.799+00:00"},"s":"I",  "c":"INDEX",    "id":20440,   "ctx":"conn11","msg":"Index build: waiting for index build to complete","attr":{"buildUUID":{"uuid":{"$uuid":"1372005d-b7cf-482d-bac7-6ab406247c44"}},"deadline":{"$date":{"$numberLong":"9223372036854775807"}}}}
{"t":{"$date":"2024-04-09T02:26:27.800+00:00"},"s":"I",  "c":"INDEX",    "id":20447,   "ctx":"conn11","msg":"Index build: completed","attr":{"buildUUID":{"uuid":{"$uuid":"1372005d-b7cf-482d-bac7-6ab406247c44"}}}}
{"t":{"$date":"2024-04-09T02:26:27.831+00:00"},"s":"I",  "c":"COMMAND",  "id":51806,   "ctx":"conn11","msg":"CMD: dropIndexes","attr":{"namespace":"appsmith.actionCollection","uuid":{"uuid":{"$uuid":"9f67c6b7-6ddd-4347-8cb9-7467c42fe4fd"}},"indexes":"deleted_1_deletedAt_1_unpublishedCollection.deletedAt_1_createdAt_1_unpublishedCollection.defaultResources.pageId_1"}}
{"t":{"$date":"2024-04-09T02:26:27.838+00:00"},"s":"I",  "c":"COMMAND",  "id":51806,   "ctx":"conn11","msg":"CMD: dropIndexes","attr":{"namespace":"appsmith.actionCollection","uuid":{"uuid":{"$uuid":"9f67c6b7-6ddd-4347-8cb9-7467c42fe4fd"}},"indexes":"action_collection_compound_index_dec23"}}
{"t":{"$date":"2024-04-09T02:26:27.847+00:00"},"s":"I",  "c":"INDEX",    "id":20438,   "ctx":"conn11","msg":"Index build: registering","attr":{"buildUUID":{"uuid":{"$uuid":"54087835-8c67-46b0-86f5-16fa0d3e73a5"}},"namespace":"appsmith.actionCollection","collectionUUID":{"uuid":{"$uuid":"9f67c6b7-6ddd-4347-8cb9-7467c42fe4fd"}},"indexes":1,"firstIndex":{"name":"action_collection_compound_index_dec23"}}}
{"t":{"$date":"2024-04-09T02:26:27.857+00:00"},"s":"I",  "c":"INDEX",    "id":20345,   "ctx":"conn11","msg":"Index build: done building","attr":{"buildUUID":null,"namespace":"appsmith.actionCollection","index":"action_collection_compound_index_dec23","commitTimestamp":{"$timestamp":{"t":1712629587,"i":52}}}}
{"t":{"$date":"2024-04-09T02:26:27.857+00:00"},"s":"I",  "c":"INDEX",    "id":20440,   "ctx":"conn11","msg":"Index build: waiting for index build to complete","attr":{"buildUUID":{"uuid":{"$uuid":"54087835-8c67-46b0-86f5-16fa0d3e73a5"}},"deadline":{"$date":{"$numberLong":"9223372036854775807"}}}}
{"t":{"$date":"2024-04-09T02:26:27.858+00:00"},"s":"I",  "c":"INDEX",    "id":20447,   "ctx":"conn11","msg":"Index build: completed","attr":{"buildUUID":{"uuid":{"$uuid":"54087835-8c67-46b0-86f5-16fa0d3e73a5"}}}}
{"t":{"$date":"2024-04-09T02:26:27.889+00:00"},"s":"I",  "c":"COMMAND",  "id":51806,   "ctx":"conn11","msg":"CMD: dropIndexes","attr":{"namespace":"appsmith.datasourceStorage","uuid":{"uuid":{"$uuid":"cc649aad-623d-45a9-88a4-10d2d75c7af2"}},"indexes":"environmentId_1_deleted_1"}}
{"t":{"$date":"2024-04-09T02:26:27.898+00:00"},"s":"I",  "c":"INDEX",    "id":20438,   "ctx":"conn11","msg":"Index build: registering","attr":{"buildUUID":{"uuid":{"$uuid":"97d6a16c-ff0a-41cf-baf0-2632b7cf447b"}},"namespace":"appsmith.datasourceStorage","collectionUUID":{"uuid":{"$uuid":"cc649aad-623d-45a9-88a4-10d2d75c7af2"}},"indexes":1,"firstIndex":{"name":"environmentId_1_deleted_1"}}}
{"t":{"$date":"2024-04-09T02:26:27.909+00:00"},"s":"I",  "c":"INDEX",    "id":20345,   "ctx":"conn11","msg":"Index build: done building","attr":{"buildUUID":null,"namespace":"appsmith.datasourceStorage","index":"environmentId_1_deleted_1","commitTimestamp":{"$timestamp":{"t":1712629587,"i":56}}}}
{"t":{"$date":"2024-04-09T02:26:27.910+00:00"},"s":"I",  "c":"INDEX",    "id":20440,   "ctx":"conn11","msg":"Index build: waiting for index build to complete","attr":{"buildUUID":{"uuid":{"$uuid":"97d6a16c-ff0a-41cf-baf0-2632b7cf447b"}},"deadline":{"$date":{"$numberLong":"9223372036854775807"}}}}
{"t":{"$date":"2024-04-09T02:26:27.911+00:00"},"s":"I",  "c":"INDEX",    "id":20447,   "ctx":"conn11","msg":"Index build: completed","attr":{"buildUUID":{"uuid":{"$uuid":"97d6a16c-ff0a-41cf-baf0-2632b7cf447b"}}}}
{"t":{"$date":"2024-04-09T02:26:28.049+00:00"},"s":"I",  "c":"COMMAND",  "id":51806,   "ctx":"conn11","msg":"CMD: dropIndexes","attr":{"namespace":"appsmith.actionCollection","uuid":{"uuid":{"$uuid":"9f67c6b7-6ddd-4347-8cb9-7467c42fe4fd"}},"indexes":"unpublishedCollection.defaultResources.pageId_1"}}
{"t":{"$date":"2024-04-09T02:26:28.063+00:00"},"s":"I",  "c":"COMMAND",  "id":51806,   "ctx":"conn11","msg":"CMD: dropIndexes","attr":{"namespace":"appsmith.actionCollection","uuid":{"uuid":{"$uuid":"9f67c6b7-6ddd-4347-8cb9-7467c42fe4fd"}},"indexes":"unpublishedCollection.defaultResources.pageId"}}
{"t":{"$date":"2024-04-09T02:26:28.075+00:00"},"s":"I",  "c":"INDEX",    "id":20438,   "ctx":"conn11","msg":"Index build: registering","attr":{"buildUUID":{"uuid":{"$uuid":"7fd7d519-3eab-48e9-b7fa-bccba40eca63"}},"namespace":"appsmith.actionCollection","collectionUUID":{"uuid":{"$uuid":"9f67c6b7-6ddd-4347-8cb9-7467c42fe4fd"}},"indexes":1,"firstIndex":{"name":"unpublishedCollection.defaultResources.pageId"}}}
{"t":{"$date":"2024-04-09T02:26:28.088+00:00"},"s":"I",  "c":"INDEX",    "id":20345,   "ctx":"conn11","msg":"Index build: done building","attr":{"buildUUID":null,"namespace":"appsmith.actionCollection","index":"unpublishedCollection.defaultResources.pageId","commitTimestamp":{"$timestamp":{"t":1712629588,"i":4}}}}
{"t":{"$date":"2024-04-09T02:26:28.089+00:00"},"s":"I",  "c":"INDEX",    "id":20440,   "ctx":"conn11","msg":"Index build: waiting for index build to complete","attr":{"buildUUID":{"uuid":{"$uuid":"7fd7d519-3eab-48e9-b7fa-bccba40eca63"}},"deadline":{"$date":{"$numberLong":"9223372036854775807"}}}}
{"t":{"$date":"2024-04-09T02:26:28.089+00:00"},"s":"I",  "c":"INDEX",    "id":20447,   "ctx":"conn11","msg":"Index build: completed","attr":{"buildUUID":{"uuid":{"$uuid":"7fd7d519-3eab-48e9-b7fa-bccba40eca63"}}}}
{"t":{"$date":"2024-04-09T02:26:28.105+00:00"},"s":"I",  "c":"COMMAND",  "id":51806,   "ctx":"conn11","msg":"CMD: dropIndexes","attr":{"namespace":"appsmith.application","uuid":{"uuid":{"$uuid":"ff48fded-c9f9-4807-a12d-a235fe9711f4"}},"indexes":"policies.permissionGroups_1"}}
{"t":{"$date":"2024-04-09T02:26:28.115+00:00"},"s":"I",  "c":"COMMAND",  "id":51806,   "ctx":"conn11","msg":"CMD: dropIndexes","attr":{"namespace":"appsmith.application","uuid":{"uuid":{"$uuid":"ff48fded-c9f9-4807-a12d-a235fe9711f4"}},"indexes":"policies.permissionGroups"}}
{"t":{"$date":"2024-04-09T02:26:28.146+00:00"},"s":"I",  "c":"INDEX",    "id":20438,   "ctx":"conn11","msg":"Index build: registering","attr":{"buildUUID":{"uuid":{"$uuid":"e112dd7b-400d-449d-95e3-60435b646093"}},"namespace":"appsmith.application","collectionUUID":{"uuid":{"$uuid":"ff48fded-c9f9-4807-a12d-a235fe9711f4"}},"indexes":1,"firstIndex":{"name":"policies.permissionGroups"}}}
{"t":{"$date":"2024-04-09T02:26:28.169+00:00"},"s":"I",  "c":"INDEX",    "id":20345,   "ctx":"conn11","msg":"Index build: done building","attr":{"buildUUID":null,"namespace":"appsmith.application","index":"policies.permissionGroups","commitTimestamp":{"$timestamp":{"t":1712629588,"i":6}}}}
{"t":{"$date":"2024-04-09T02:26:28.170+00:00"},"s":"I",  "c":"INDEX",    "id":20440,   "ctx":"conn11","msg":"Index build: waiting for index build to complete","attr":{"buildUUID":{"uuid":{"$uuid":"e112dd7b-400d-449d-95e3-60435b646093"}},"deadline":{"$date":{"$numberLong":"9223372036854775807"}}}}
{"t":{"$date":"2024-04-09T02:26:28.171+00:00"},"s":"I",  "c":"INDEX",    "id":20447,   "ctx":"conn11","msg":"Index build: completed","attr":{"buildUUID":{"uuid":{"$uuid":"e112dd7b-400d-449d-95e3-60435b646093"}}}}
{"t":{"$date":"2024-04-09T02:26:28.177+00:00"},"s":"I",  "c":"COMMAND",  "id":51806,   "ctx":"conn11","msg":"CMD: dropIndexes","attr":{"namespace":"appsmith.workspace","uuid":{"uuid":{"$uuid":"8f643824-f3e0-4603-8204-a1092fca887e"}},"indexes":"policies.permissionGroups_1"}}
{"t":{"$date":"2024-04-09T02:26:28.183+00:00"},"s":"I",  "c":"COMMAND",  "id":51806,   "ctx":"conn11","msg":"CMD: dropIndexes","attr":{"namespace":"appsmith.workspace","uuid":{"uuid":{"$uuid":"8f643824-f3e0-4603-8204-a1092fca887e"}},"indexes":"policies.permissionGroups"}}
{"t":{"$date":"2024-04-09T02:26:28.190+00:00"},"s":"I",  "c":"INDEX",    "id":20438,   "ctx":"conn11","msg":"Index build: registering","attr":{"buildUUID":{"uuid":{"$uuid":"cd788cee-38dc-46a0-9935-751642a7862e"}},"namespace":"appsmith.workspace","collectionUUID":{"uuid":{"$uuid":"8f643824-f3e0-4603-8204-a1092fca887e"}},"indexes":1,"firstIndex":{"name":"policies.permissionGroups"}}}
{"t":{"$date":"2024-04-09T02:26:28.200+00:00"},"s":"I",  "c":"INDEX",    "id":20345,   "ctx":"conn11","msg":"Index build: done building","attr":{"buildUUID":null,"namespace":"appsmith.workspace","index":"policies.permissionGroups","commitTimestamp":{"$timestamp":{"t":1712629588,"i":7}}}}
{"t":{"$date":"2024-04-09T02:26:28.201+00:00"},"s":"I",  "c":"INDEX",    "id":20440,   "ctx":"conn11","msg":"Index build: waiting for index build to complete","attr":{"buildUUID":{"uuid":{"$uuid":"cd788cee-38dc-46a0-9935-751642a7862e"}},"deadline":{"$date":{"$numberLong":"9223372036854775807"}}}}
{"t":{"$date":"2024-04-09T02:26:28.201+00:00"},"s":"I",  "c":"INDEX",    "id":20447,   "ctx":"conn11","msg":"Index build: completed","attr":{"buildUUID":{"uuid":{"$uuid":"cd788cee-38dc-46a0-9935-751642a7862e"}}}}
{"t":{"$date":"2024-04-09T02:26:28.207+00:00"},"s":"I",  "c":"COMMAND",  "id":51806,   "ctx":"conn11","msg":"CMD: dropIndexes","attr":{"namespace":"appsmith.datasourceStorage","uuid":{"uuid":{"$uuid":"cc649aad-623d-45a9-88a4-10d2d75c7af2"}},"indexes":"environmentId_1"}}
{"t":{"$date":"2024-04-09T02:26:28.213+00:00"},"s":"I",  "c":"COMMAND",  "id":51806,   "ctx":"conn11","msg":"CMD: dropIndexes","attr":{"namespace":"appsmith.datasourceStorage","uuid":{"uuid":{"$uuid":"cc649aad-623d-45a9-88a4-10d2d75c7af2"}},"indexes":"environmentId"}}
{"t":{"$date":"2024-04-09T02:26:28.220+00:00"},"s":"I",  "c":"INDEX",    "id":20438,   "ctx":"conn11","msg":"Index build: registering","attr":{"buildUUID":{"uuid":{"$uuid":"ad0baebc-8f63-45af-880b-03a7246b994c"}},"namespace":"appsmith.datasourceStorage","collectionUUID":{"uuid":{"$uuid":"cc649aad-623d-45a9-88a4-10d2d75c7af2"}},"indexes":1,"firstIndex":{"name":"environmentId"}}}
{"t":{"$date":"2024-04-09T02:26:28.233+00:00"},"s":"I",  "c":"INDEX",    "id":20345,   "ctx":"conn11","msg":"Index build: done building","attr":{"buildUUID":null,"namespace":"appsmith.datasourceStorage","index":"environmentId","commitTimestamp":{"$timestamp":{"t":1712629588,"i":8}}}}
{"t":{"$date":"2024-04-09T02:26:28.234+00:00"},"s":"I",  "c":"INDEX",    "id":20440,   "ctx":"conn11","msg":"Index build: waiting for index build to complete","attr":{"buildUUID":{"uuid":{"$uuid":"ad0baebc-8f63-45af-880b-03a7246b994c"}},"deadline":{"$date":{"$numberLong":"9223372036854775807"}}}}
{"t":{"$date":"2024-04-09T02:26:28.235+00:00"},"s":"I",  "c":"INDEX",    "id":20447,   "ctx":"conn11","msg":"Index build: completed","attr":{"buildUUID":{"uuid":{"$uuid":"ad0baebc-8f63-45af-880b-03a7246b994c"}}}}
{"t":{"$date":"2024-04-09T02:26:28.327+00:00"},"s":"I",  "c":"COMMAND",  "id":51806,   "ctx":"conn11","msg":"CMD: dropIndexes","attr":{"namespace":"appsmith.user","uuid":{"uuid":{"$uuid":"a9713af2-d906-433e-9587-a34147da1e01"}},"indexes":"deleted_1_deletedAt_1_email_1_createdAt_-1"}}
{"t":{"$date":"2024-04-09T02:26:28.333+00:00"},"s":"I",  "c":"COMMAND",  "id":51806,   "ctx":"conn11","msg":"CMD: dropIndexes","attr":{"namespace":"appsmith.user","uuid":{"uuid":{"$uuid":"a9713af2-d906-433e-9587-a34147da1e01"}},"indexes":"user_deleted_deletedAt_email_createdAt_compound_index"}}
{"t":{"$date":"2024-04-09T02:26:28.342+00:00"},"s":"I",  "c":"INDEX",    "id":20438,   "ctx":"conn11","msg":"Index build: registering","attr":{"buildUUID":{"uuid":{"$uuid":"20642e26-e783-4cde-8315-fb0effd69ef3"}},"namespace":"appsmith.user","collectionUUID":{"uuid":{"$uuid":"a9713af2-d906-433e-9587-a34147da1e01"}},"indexes":1,"firstIndex":{"name":"user_deleted_deletedAt_email_createdAt_compound_index"}}}
{"t":{"$date":"2024-04-09T02:26:28.368+00:00"},"s":"I",  "c":"INDEX",    "id":20384,   "ctx":"IndexBuildsCoordinatorMongod-0","msg":"Index build: starting","attr":{"buildUUID":{"uuid":{"$uuid":"20642e26-e783-4cde-8315-fb0effd69ef3"}},"collectionUUID":{"uuid":{"$uuid":"a9713af2-d906-433e-9587-a34147da1e01"}},"namespace":"appsmith.user","properties":{"v":2,"key":{"deleted":1,"deletedAt":1,"email":1,"createdAt":-1},"name":"user_deleted_deletedAt_email_createdAt_compound_index"},"method":"Hybrid","maxTemporaryMemoryUsageMB":200}}
{"t":{"$date":"2024-04-09T02:26:28.369+00:00"},"s":"I",  "c":"INDEX",    "id":20346,   "ctx":"IndexBuildsCoordinatorMongod-0","msg":"Index build: initialized","attr":{"buildUUID":{"uuid":{"$uuid":"20642e26-e783-4cde-8315-fb0effd69ef3"}},"collectionUUID":{"uuid":{"$uuid":"a9713af2-d906-433e-9587-a34147da1e01"}},"namespace":"appsmith.user","initializationTimestamp":{"$timestamp":{"t":1712629588,"i":14}}}}
{"t":{"$date":"2024-04-09T02:26:28.370+00:00"},"s":"I",  "c":"STORAGE",  "id":4847600, "ctx":"IndexBuildsCoordinatorMongod-0","msg":"Index build: waiting for last optime before interceptors to be majority committed","attr":{"buildUUID":{"uuid":{"$uuid":"20642e26-e783-4cde-8315-fb0effd69ef3"}},"collectionUUID":{"uuid":{"$uuid":"a9713af2-d906-433e-9587-a34147da1e01"}},"deadline":{"$date":"2024-04-09T02:26:38.370Z"},"timeoutMillis":10000,"lastOpTime":{"ts":{"$timestamp":{"t":1712629588,"i":14}},"t":2}}}
{"t":{"$date":"2024-04-09T02:26:28.370+00:00"},"s":"I",  "c":"INDEX",    "id":20440,   "ctx":"conn11","msg":"Index build: waiting for index build to complete","attr":{"buildUUID":{"uuid":{"$uuid":"20642e26-e783-4cde-8315-fb0effd69ef3"}},"deadline":{"$date":{"$numberLong":"9223372036854775807"}}}}
{"t":{"$date":"2024-04-09T02:26:28.438+00:00"},"s":"I",  "c":"INDEX",    "id":20391,   "ctx":"IndexBuildsCoordinatorMongod-0","msg":"Index build: collection scan done","attr":{"buildUUID":{"uuid":{"$uuid":"20642e26-e783-4cde-8315-fb0effd69ef3"}},"collectionUUID":{"uuid":{"$uuid":"a9713af2-d906-433e-9587-a34147da1e01"}},"namespace":"appsmith.user","totalRecords":1,"readSource":"kMajorityCommitted","durationMillis":0}}
{"t":{"$date":"2024-04-09T02:26:28.464+00:00"},"s":"I",  "c":"INDEX",    "id":20685,   "ctx":"IndexBuildsCoordinatorMongod-0","msg":"Index build: inserted keys from external sorter into index","attr":{"namespace":"appsmith.user","index":"user_deleted_deletedAt_email_createdAt_compound_index","keysInserted":1,"durationMillis":0}}
{"t":{"$date":"2024-04-09T02:26:28.473+00:00"},"s":"I",  "c":"STORAGE",  "id":3856201, "ctx":"conn31","msg":"Index build: commit quorum satisfied","attr":{"indexBuildEntry":{"_id":{"$uuid":"20642e26-e783-4cde-8315-fb0effd69ef3"},"collectionUUID":{"$uuid":"a9713af2-d906-433e-9587-a34147da1e01"},"commitQuorum":"votingMembers","indexNames":["user_deleted_deletedAt_email_createdAt_compound_index"],"commitReadyMembers":["localhost:27017"]}}}
{"t":{"$date":"2024-04-09T02:26:28.474+00:00"},"s":"I",  "c":"CONNPOOL", "id":22566,   "ctx":"ReplNetwork","msg":"Ending connection due to bad connection status","attr":{"hostAndPort":"localhost:27017","error":"CallbackCanceled: Callback was canceled","numOpenConns":0}}
{"t":{"$date":"2024-04-09T02:26:28.474+00:00"},"s":"I",  "c":"CONNPOOL", "id":22576,   "ctx":"ReplNetwork","msg":"Connecting","attr":{"hostAndPort":"localhost:27017"}}
{"t":{"$date":"2024-04-09T02:26:28.475+00:00"},"s":"I",  "c":"NETWORK",  "id":22944,   "ctx":"conn31","msg":"Connection ended","attr":{"remote":"127.0.0.1:36408","uuid":"165872f0-0703-4713-848a-d6123f34068c","connectionId":31,"connectionCount":5}}
{"t":{"$date":"2024-04-09T02:26:28.474+00:00"},"s":"I",  "c":"STORAGE",  "id":3856203, "ctx":"IndexBuildsCoordinatorMongod-0","msg":"Index build: waiting for next action before completing final phase","attr":{"buildUUID":{"uuid":{"$uuid":"20642e26-e783-4cde-8315-fb0effd69ef3"}}}}
{"t":{"$date":"2024-04-09T02:26:28.476+00:00"},"s":"I",  "c":"NETWORK",  "id":22943,   "ctx":"listener","msg":"Connection accepted","attr":{"remote":"127.0.0.1:36410","uuid":"c583cf4b-1fef-4de7-8238-86e6b0a249bd","connectionId":33,"connectionCount":6}}
{"t":{"$date":"2024-04-09T02:26:28.477+00:00"},"s":"I",  "c":"STORAGE",  "id":3856204, "ctx":"IndexBuildsCoordinatorMongod-0","msg":"Index build: received signal","attr":{"buildUUID":{"uuid":{"$uuid":"20642e26-e783-4cde-8315-fb0effd69ef3"}},"action":"Commit quorum Satisfied"}}
{"t":{"$date":"2024-04-09T02:26:28.479+00:00"},"s":"I",  "c":"NETWORK",  "id":51800,   "ctx":"conn33","msg":"client metadata","attr":{"remote":"127.0.0.1:36410","client":"conn33","negotiatedCompressors":["snappy","zstd","zlib"],"doc":{"driver":{"name":"NetworkInterfaceTL","version":"5.0.26"},"os":{"type":"Linux","name":"Ubuntu","architecture":"x86_64","version":"20.04"}}}}
{"t":{"$date":"2024-04-09T02:26:28.479+00:00"},"s":"I",  "c":"INDEX",    "id":20345,   "ctx":"IndexBuildsCoordinatorMongod-0","msg":"Index build: done building","attr":{"buildUUID":{"uuid":{"$uuid":"20642e26-e783-4cde-8315-fb0effd69ef3"}},"namespace":"appsmith.user","index":"user_deleted_deletedAt_email_createdAt_compound_index","commitTimestamp":{"$timestamp":{"t":1712629588,"i":16}}}}
{"t":{"$date":"2024-04-09T02:26:28.481+00:00"},"s":"I",  "c":"ACCESS",   "id":20250,   "ctx":"conn33","msg":"Authentication succeeded","attr":{"mechanism":"SCRAM-SHA-256","speculative":true,"principalName":"__system","authenticationDatabase":"local","remote":"127.0.0.1:36410","extraInfo":{}}}
{"t":{"$date":"2024-04-09T02:26:28.483+00:00"},"s":"I",  "c":"STORAGE",  "id":20663,   "ctx":"IndexBuildsCoordinatorMongod-0","msg":"Index build: completed successfully","attr":{"buildUUID":{"uuid":{"$uuid":"20642e26-e783-4cde-8315-fb0effd69ef3"}},"collectionUUID":{"uuid":{"$uuid":"a9713af2-d906-433e-9587-a34147da1e01"}},"namespace":"appsmith.user","indexesBuilt":["user_deleted_deletedAt_email_createdAt_compound_index"],"numIndexesBefore":3,"numIndexesAfter":4}}
{"t":{"$date":"2024-04-09T02:26:28.485+00:00"},"s":"I",  "c":"INDEX",    "id":20447,   "ctx":"conn11","msg":"Index build: completed","attr":{"buildUUID":{"uuid":{"$uuid":"20642e26-e783-4cde-8315-fb0effd69ef3"}}}}
{"t":{"$date":"2024-04-09T02:26:28.487+00:00"},"s":"I",  "c":"COMMAND",  "id":51803,   "ctx":"conn11","msg":"Slow query","attr":{"type":"command","ns":"appsmith.user","command":{"createIndexes":"user","indexes":[{"key":{"deleted":1,"deletedAt":1,"email":1,"createdAt":-1},"name":"user_deleted_deletedAt_email_createdAt_compound_index"}],"$db":"appsmith","$clusterTime":{"clusterTime":{"$timestamp":{"t":1712629588,"i":12}},"signature":{"hash":{"$binary":{"base64":"kAqf/z2ZF1Ar06Obgl7nHhnQNrs=","subType":"0"}},"keyId":7355687795744047109}},"lsid":{"id":{"$uuid":"6083b346-52d2-4c7c-8d49-0fad1e364fcc"}},"$readPreference":{"mode":"primaryPreferred"}},"numYields":0,"reslen":271,"locks":{"ParallelBatchWriterMode":{"acquireCount":{"r":3}},"FeatureCompatibilityVersion":{"acquireCount":{"r":4,"w":2}},"ReplicationStateTransition":{"acquireCount":{"w":6}},"Global":{"acquireCount":{"r":4,"w":2}},"Database":{"acquireCount":{"r":2,"w":1}},"Collection":{"acquireCount":{"r":2,"W":1}},"Mutex":{"acquireCount":{"r":3}}},"flowControl":{"acquireCount":2,"timeAcquiringMicros":3},"readConcern":{"level":"local","provenance":"implicitDefault"},"writeConcern":{"w":"majority","wtimeout":0,"provenance":"implicitDefault"},"waitForWriteConcernDurationMillis":1,"storage":{},"remote":"127.0.0.1:60858","protocol":"op_msg","durationMillis":144}}
{"t":{"$date":"2024-04-09T02:26:30.262+00:00"},"s":"I",  "c":"NETWORK",  "id":22943,   "ctx":"listener","msg":"Connection accepted","attr":{"remote":"127.0.0.1:36416","uuid":"7084596d-16b5-4ce3-9fa8-38c80af0e2d5","connectionId":34,"connectionCount":7}}
{"t":{"$date":"2024-04-09T02:26:30.269+00:00"},"s":"I",  "c":"NETWORK",  "id":51800,   "ctx":"conn34","msg":"client metadata","attr":{"remote":"127.0.0.1:36416","client":"conn34","negotiatedCompressors":[],"doc":{"driver":{"name":"mongo-java-driver|reactive-streams|spring-boot","version":"4.8.2"},"os":{"type":"Linux","name":"Linux","architecture":"amd64","version":"5.15.49-linuxkit"},"platform":"Java/Eclipse Adoptium/17.0.9+9"}}}
{"t":{"$date":"2024-04-09T02:26:30.331+00:00"},"s":"I",  "c":"ACCESS",   "id":20250,   "ctx":"conn34","msg":"Authentication succeeded","attr":{"mechanism":"SCRAM-SHA-256","speculative":true,"principalName":"appsmith","authenticationDatabase":"appsmith","remote":"127.0.0.1:36416","extraInfo":{}}}
{"t":{"$date":"2024-04-09T02:26:56.105+00:00"},"s":"I",  "c":"NETWORK",  "id":22943,   "ctx":"listener","msg":"Connection accepted","attr":{"remote":"127.0.0.1:41342","uuid":"c3b1e1b9-af5e-455a-8664-339e958a8a45","connectionId":35,"connectionCount":8}}
{"t":{"$date":"2024-04-09T02:26:56.113+00:00"},"s":"I",  "c":"NETWORK",  "id":22943,   "ctx":"listener","msg":"Connection accepted","attr":{"remote":"127.0.0.1:41354","uuid":"8f5b2f8f-cf7a-47a6-a5a3-24e6bee18ed5","connectionId":36,"connectionCount":9}}
{"t":{"$date":"2024-04-09T02:26:56.113+00:00"},"s":"I",  "c":"NETWORK",  "id":51800,   "ctx":"conn35","msg":"client metadata","attr":{"remote":"127.0.0.1:41342","client":"conn35","negotiatedCompressors":[],"doc":{"driver":{"name":"mongo-java-driver|reactive-streams|spring-boot","version":"4.8.2"},"os":{"type":"Linux","name":"Linux","architecture":"amd64","version":"5.15.49-linuxkit"},"platform":"Java/Eclipse Adoptium/17.0.9+9"}}}
{"t":{"$date":"2024-04-09T02:26:56.144+00:00"},"s":"I",  "c":"NETWORK",  "id":51800,   "ctx":"conn36","msg":"client metadata","attr":{"remote":"127.0.0.1:41354","client":"conn36","negotiatedCompressors":[],"doc":{"driver":{"name":"mongo-java-driver|reactive-streams|spring-boot","version":"4.8.2"},"os":{"type":"Linux","name":"Linux","architecture":"amd64","version":"5.15.49-linuxkit"},"platform":"Java/Eclipse Adoptium/17.0.9+9"}}}
{"t":{"$date":"2024-04-09T02:26:56.169+00:00"},"s":"I",  "c":"ACCESS",   "id":20250,   "ctx":"conn35","msg":"Authentication succeeded","attr":{"mechanism":"SCRAM-SHA-256","speculative":true,"principalName":"appsmith","authenticationDatabase":"appsmith","remote":"127.0.0.1:41342","extraInfo":{}}}
{"t":{"$date":"2024-04-09T02:26:56.218+00:00"},"s":"I",  "c":"ACCESS",   "id":20250,   "ctx":"conn36","msg":"Authentication succeeded","attr":{"mechanism":"SCRAM-SHA-256","speculative":true,"principalName":"appsmith","authenticationDatabase":"appsmith","remote":"127.0.0.1:41354","extraInfo":{}}}
{"t":{"$date":"2024-04-09T02:27:02.882+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"Checkpointer","msg":"WiredTiger message","attr":{"message":"[1712629622:882762][1780:0x7f647559a700], WT_SESSION.checkpoint: [WT_VERB_CHECKPOINT_PROGRESS] saving checkpoint snapshot min: 1112, snapshot max: 1112 snapshot count: 0, oldest timestamp: (**********, 1) , meta checkpoint timestamp: (1712629621, 3) base write gen: 33"}}
{"t":{"$date":"2024-04-09T02:28:02.935+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"Checkpointer","msg":"WiredTiger message","attr":{"message":"[1712629682:935660][1780:0x7f647559a700], WT_SESSION.checkpoint: [WT_VERB_CHECKPOINT_PROGRESS] saving checkpoint snapshot min: 1125, snapshot max: 1125 snapshot count: 0, oldest timestamp: (**********, 1) , meta checkpoint timestamp: (1712629673, 1) base write gen: 33"}}
{"t":{"$date":"2024-04-09T02:28:30.135+00:00"},"s":"I",  "c":"NETWORK",  "id":22943,   "ctx":"listener","msg":"Connection accepted","attr":{"remote":"127.0.0.1:39948","uuid":"78c5ca8f-b2a1-407f-8822-f1814cf11e8f","connectionId":37,"connectionCount":10}}
{"t":{"$date":"2024-04-09T02:28:30.141+00:00"},"s":"I",  "c":"NETWORK",  "id":51800,   "ctx":"conn37","msg":"client metadata","attr":{"remote":"127.0.0.1:39948","client":"conn37","negotiatedCompressors":[],"doc":{"driver":{"name":"mongo-java-driver|reactive-streams|spring-boot","version":"4.8.2"},"os":{"type":"Linux","name":"Linux","architecture":"amd64","version":"5.15.49-linuxkit"},"platform":"Java/Eclipse Adoptium/17.0.9+9"}}}
{"t":{"$date":"2024-04-09T02:28:30.166+00:00"},"s":"I",  "c":"ACCESS",   "id":20250,   "ctx":"conn37","msg":"Authentication succeeded","attr":{"mechanism":"SCRAM-SHA-256","speculative":true,"principalName":"appsmith","authenticationDatabase":"appsmith","remote":"127.0.0.1:39948","extraInfo":{}}}
{"t":{"$date":"2024-04-09T02:29:02.990+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"Checkpointer","msg":"WiredTiger message","attr":{"message":"[1712629742:990882][1780:0x7f647559a700], WT_SESSION.checkpoint: [WT_VERB_CHECKPOINT_PROGRESS] saving checkpoint snapshot min: 1140, snapshot max: 1140 snapshot count: 0, oldest timestamp: (**********, 1) , meta checkpoint timestamp: (1712629733, 1) base write gen: 33"}}
{"t":{"$date":"2024-04-09T02:30:03.175+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"Checkpointer","msg":"WiredTiger message","attr":{"message":"[1712629803:175962][1780:0x7f647559a700], WT_SESSION.checkpoint: [WT_VERB_CHECKPOINT_PROGRESS] saving checkpoint snapshot min: 1207, snapshot max: 1207 snapshot count: 0, oldest timestamp: (**********, 1) , meta checkpoint timestamp: (1712629793, 1) base write gen: 33"}}
{"t":{"$date":"2024-04-09T02:31:03.262+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"Checkpointer","msg":"WiredTiger message","attr":{"message":"[1712629863:262955][1780:0x7f647559a700], WT_SESSION.checkpoint: [WT_VERB_CHECKPOINT_PROGRESS] saving checkpoint snapshot min: 1230, snapshot max: 1230 snapshot count: 0, oldest timestamp: (1712629562, 7) , meta checkpoint timestamp: (1712629862, 7) base write gen: 33"}}
{"t":{"$date":"2024-04-09T02:31:28.476+00:00"},"s":"I",  "c":"CONNPOOL", "id":22572,   "ctx":"ReplNetwork","msg":"Dropping all pooled connections","attr":{"hostAndPort":"localhost:27017","error":"ConnectionPoolExpired: Pool for localhost:27017 has expired."}}
{"t":{"$date":"2024-04-09T02:31:28.486+00:00"},"s":"I",  "c":"NETWORK",  "id":22944,   "ctx":"conn33","msg":"Connection ended","attr":{"remote":"127.0.0.1:36410","uuid":"c583cf4b-1fef-4de7-8238-86e6b0a249bd","connectionId":33,"connectionCount":9}}
{"t":{"$date":"2024-04-09T02:31:33.741+00:00"},"s":"I",  "c":"STORAGE",  "id":22260,   "ctx":"TimestampMonitor","msg":"Removing drop-pending idents with drop timestamps before timestamp","attr":{"timestamp":{"$timestamp":{"t":1712629593,"i":1}}}}
{"t":{"$date":"2024-04-09T02:31:33.742+00:00"},"s":"I",  "c":"STORAGE",  "id":22237,   "ctx":"TimestampMonitor","msg":"Completing drop for ident","attr":{"ident":"index-35--7653259994859265256","dropTimestamp":{"$timestamp":{"t":1712629583,"i":14}}}}
{"t":{"$date":"2024-04-09T02:31:33.749+00:00"},"s":"I",  "c":"STORAGE",  "id":6776600, "ctx":"TimestampMonitor","msg":"The ident was successfully dropped","attr":{"ident":"index-35--7653259994859265256","dropTimestamp":{"$timestamp":{"t":1712629583,"i":14}}}}
{"t":{"$date":"2024-04-09T02:31:33.750+00:00"},"s":"I",  "c":"STORAGE",  "id":22237,   "ctx":"TimestampMonitor","msg":"Completing drop for ident","attr":{"ident":"index-36--7653259994859265256","dropTimestamp":{"$timestamp":{"t":1712629583,"i":15}}}}
{"t":{"$date":"2024-04-09T02:31:33.753+00:00"},"s":"I",  "c":"STORAGE",  "id":6776600, "ctx":"TimestampMonitor","msg":"The ident was successfully dropped","attr":{"ident":"index-36--7653259994859265256","dropTimestamp":{"$timestamp":{"t":1712629583,"i":15}}}}
{"t":{"$date":"2024-04-09T02:31:33.754+00:00"},"s":"I",  "c":"STORAGE",  "id":22237,   "ctx":"TimestampMonitor","msg":"Completing drop for ident","attr":{"ident":"index-58--7653259994859265256","dropTimestamp":{"$timestamp":{"t":1712629583,"i":35}}}}
{"t":{"$date":"2024-04-09T02:31:33.759+00:00"},"s":"I",  "c":"STORAGE",  "id":6776600, "ctx":"TimestampMonitor","msg":"The ident was successfully dropped","attr":{"ident":"index-58--7653259994859265256","dropTimestamp":{"$timestamp":{"t":1712629583,"i":35}}}}
{"t":{"$date":"2024-04-09T02:31:33.760+00:00"},"s":"I",  "c":"STORAGE",  "id":22237,   "ctx":"TimestampMonitor","msg":"Completing drop for ident","attr":{"ident":"index-55--7653259994859265256","dropTimestamp":{"$timestamp":{"t":1712629583,"i":36}}}}
{"t":{"$date":"2024-04-09T02:31:33.763+00:00"},"s":"I",  "c":"STORAGE",  "id":6776600, "ctx":"TimestampMonitor","msg":"The ident was successfully dropped","attr":{"ident":"index-55--7653259994859265256","dropTimestamp":{"$timestamp":{"t":1712629583,"i":36}}}}
{"t":{"$date":"2024-04-09T02:31:33.764+00:00"},"s":"I",  "c":"STORAGE",  "id":22237,   "ctx":"TimestampMonitor","msg":"Completing drop for ident","attr":{"ident":"index-41--7653259994859265256","dropTimestamp":{"$timestamp":{"t":1712629584,"i":15}}}}
{"t":{"$date":"2024-04-09T02:31:33.769+00:00"},"s":"I",  "c":"STORAGE",  "id":6776600, "ctx":"TimestampMonitor","msg":"The ident was successfully dropped","attr":{"ident":"index-41--7653259994859265256","dropTimestamp":{"$timestamp":{"t":1712629584,"i":15}}}}
{"t":{"$date":"2024-04-09T02:31:33.770+00:00"},"s":"I",  "c":"STORAGE",  "id":22237,   "ctx":"TimestampMonitor","msg":"Completing drop for ident","attr":{"ident":"index-95--7653259994859265256","dropTimestamp":{"$timestamp":{"t":1712629587,"i":8}}}}
{"t":{"$date":"2024-04-09T02:31:33.773+00:00"},"s":"I",  "c":"STORAGE",  "id":6776600, "ctx":"TimestampMonitor","msg":"The ident was successfully dropped","attr":{"ident":"index-95--7653259994859265256","dropTimestamp":{"$timestamp":{"t":1712629587,"i":8}}}}
{"t":{"$date":"2024-04-09T02:32:03.364+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"Checkpointer","msg":"WiredTiger message","attr":{"message":"[1712629923:364767][1780:0x7f647559a700], WT_SESSION.checkpoint: [WT_VERB_CHECKPOINT_PROGRESS] saving checkpoint snapshot min: 1263, snapshot max: 1263 snapshot count: 0, oldest timestamp: (1712629623, 1) , meta checkpoint timestamp: (1712629923, 1) base write gen: 33"}}
{"t":{"$date":"2024-04-09T02:33:03.441+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"Checkpointer","msg":"WiredTiger message","attr":{"message":"[1712629983:441441][1780:0x7f647559a700], WT_SESSION.checkpoint: [WT_VERB_CHECKPOINT_PROGRESS] saving checkpoint snapshot min: 1278, snapshot max: 1278 snapshot count: 0, oldest timestamp: (1712629683, 1) , meta checkpoint timestamp: (1712629983, 1) base write gen: 33"}}
{"t":{"$date":"2024-04-09T02:34:03.474+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"Checkpointer","msg":"WiredTiger message","attr":{"message":"[1712630043:474788][1780:0x7f647559a700], WT_SESSION.checkpoint: [WT_VERB_CHECKPOINT_PROGRESS] saving checkpoint snapshot min: 1293, snapshot max: 1293 snapshot count: 0, oldest timestamp: (1712629743, 1) , meta checkpoint timestamp: (1712630043, 1) base write gen: 33"}}
{"t":{"$date":"2024-04-09T02:35:03.517+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"Checkpointer","msg":"WiredTiger message","attr":{"message":"[1712630103:517882][1780:0x7f647559a700], WT_SESSION.checkpoint: [WT_VERB_CHECKPOINT_PROGRESS] saving checkpoint snapshot min: 1308, snapshot max: 1308 snapshot count: 0, oldest timestamp: (1712629803, 1) , meta checkpoint timestamp: (1712630103, 1) base write gen: 33"}}
{"t":{"$date":"2024-04-09T02:36:03.579+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"Checkpointer","msg":"WiredTiger message","attr":{"message":"[1712630163:579635][1780:0x7f647559a700], WT_SESSION.checkpoint: [WT_VERB_CHECKPOINT_PROGRESS] saving checkpoint snapshot min: 1325, snapshot max: 1325 snapshot count: 0, oldest timestamp: (1712629862, 3) , meta checkpoint timestamp: (1712630162, 3) base write gen: 33"}}
{"t":{"$date":"2024-04-09T02:37:03.746+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"Checkpointer","msg":"WiredTiger message","attr":{"message":"[1712630223:746450][1780:0x7f647559a700], WT_SESSION.checkpoint: [WT_VERB_CHECKPOINT_PROGRESS] saving checkpoint snapshot min: 1340, snapshot max: 1340 snapshot count: 0, oldest timestamp: (1712629923, 1) , meta checkpoint timestamp: (1712630223, 1) base write gen: 33"}}
{"t":{"$date":"2024-04-09T02:38:03.807+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"Checkpointer","msg":"WiredTiger message","attr":{"message":"[1712630283:807821][1780:0x7f647559a700], WT_SESSION.checkpoint: [WT_VERB_CHECKPOINT_PROGRESS] saving checkpoint snapshot min: 1355, snapshot max: 1355 snapshot count: 0, oldest timestamp: (1712629983, 1) , meta checkpoint timestamp: (1712630283, 1) base write gen: 33"}}
{"t":{"$date":"2024-04-09T02:39:03.912+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"Checkpointer","msg":"WiredTiger message","attr":{"message":"[1712630343:910792][1780:0x7f647559a700], WT_SESSION.checkpoint: [WT_VERB_CHECKPOINT_PROGRESS] saving checkpoint snapshot min: 1370, snapshot max: 1370 snapshot count: 0, oldest timestamp: (1712630043, 1) , meta checkpoint timestamp: (1712630343, 1) base write gen: 33"}}
{"t":{"$date":"2024-04-09T02:40:03.989+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"Checkpointer","msg":"WiredTiger message","attr":{"message":"[1712630403:989607][1780:0x7f647559a700], WT_SESSION.checkpoint: [WT_VERB_CHECKPOINT_PROGRESS] saving checkpoint snapshot min: 1385, snapshot max: 1385 snapshot count: 0, oldest timestamp: (1712630103, 1) , meta checkpoint timestamp: (1712630403, 1) base write gen: 33"}}
{"t":{"$date":"2024-04-09T02:41:04.100+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"Checkpointer","msg":"WiredTiger message","attr":{"message":"[1712630464:100119][1780:0x7f647559a700], WT_SESSION.checkpoint: [WT_VERB_CHECKPOINT_PROGRESS] saving checkpoint snapshot min: 1404, snapshot max: 1404 snapshot count: 0, oldest timestamp: (1712630162, 3) , meta checkpoint timestamp: (1712630462, 3) base write gen: 33"}}
{"t":{"$date":"2024-04-09T02:42:04.298+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"Checkpointer","msg":"WiredTiger message","attr":{"message":"[1712630524:298762][1780:0x7f647559a700], WT_SESSION.checkpoint: [WT_VERB_CHECKPOINT_PROGRESS] saving checkpoint snapshot min: 1464, snapshot max: 1464 snapshot count: 0, oldest timestamp: (1712630218, 25) , meta checkpoint timestamp: (1712630518, 25) base write gen: 33"}}
{"t":{"$date":"2024-04-09T02:42:26.113+00:00"},"s":"I",  "c":"STORAGE",  "id":20320,   "ctx":"conn35","msg":"createCollection","attr":{"namespace":"appsmith.usagePulse","uuidDisposition":"generated","uuid":{"uuid":{"$uuid":"133a806b-0444-4161-a22e-10d4df48a16b"}},"options":{}}}
{"t":{"$date":"2024-04-09T02:42:26.140+00:00"},"s":"I",  "c":"INDEX",    "id":20345,   "ctx":"conn35","msg":"Index build: done building","attr":{"buildUUID":null,"namespace":"appsmith.usagePulse","index":"_id_","commitTimestamp":{"$timestamp":{"t":1712630546,"i":2}}}}
{"t":{"$date":"2024-04-09T02:43:04.392+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"Checkpointer","msg":"WiredTiger message","attr":{"message":"[1712630584:392055][1780:0x7f647559a700], WT_SESSION.checkpoint: [WT_VERB_CHECKPOINT_PROGRESS] saving checkpoint snapshot min: 1498, snapshot max: 1498 snapshot count: 0, oldest timestamp: (1712630283, 1) , meta checkpoint timestamp: (1712630583, 1) base write gen: 33"}}
{"t":{"$date":"2024-04-09T02:44:04.427+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"Checkpointer","msg":"WiredTiger message","attr":{"message":"[1712630644:427055][1780:0x7f647559a700], WT_SESSION.checkpoint: [WT_VERB_CHECKPOINT_PROGRESS] saving checkpoint snapshot min: 1513, snapshot max: 1513 snapshot count: 0, oldest timestamp: (1712630343, 1) , meta checkpoint timestamp: (1712630643, 1) base write gen: 33"}}
{"t":{"$date":"2024-04-09T02:45:04.476+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"Checkpointer","msg":"WiredTiger message","attr":{"message":"[1712630704:476899][1780:0x7f647559a700], WT_SESSION.checkpoint: [WT_VERB_CHECKPOINT_PROGRESS] saving checkpoint snapshot min: 1528, snapshot max: 1528 snapshot count: 0, oldest timestamp: (1712630403, 1) , meta checkpoint timestamp: (1712630703, 1) base write gen: 33"}}
{"t":{"$date":"2024-04-09T02:46:04.567+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"Checkpointer","msg":"WiredTiger message","attr":{"message":"[1712630764:567100][1780:0x7f647559a700], WT_SESSION.checkpoint: [WT_VERB_CHECKPOINT_PROGRESS] saving checkpoint snapshot min: 1547, snapshot max: 1547 snapshot count: 0, oldest timestamp: (1712630462, 5) , meta checkpoint timestamp: (1712630762, 5) base write gen: 33"}}
{"t":{"$date":"2024-04-09T02:47:04.629+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"Checkpointer","msg":"WiredTiger message","attr":{"message":"[1712630824:629175][1780:0x7f647559a700], WT_SESSION.checkpoint: [WT_VERB_CHECKPOINT_PROGRESS] saving checkpoint snapshot min: 1562, snapshot max: 1562 snapshot count: 0, oldest timestamp: (1712630523, 1) , meta checkpoint timestamp: (1712630823, 1) base write gen: 33"}}
{"t":{"$date":"2024-04-09T02:48:04.670+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"Checkpointer","msg":"WiredTiger message","attr":{"message":"[1712630884:670065][1780:0x7f647559a700], WT_SESSION.checkpoint: [WT_VERB_CHECKPOINT_PROGRESS] saving checkpoint snapshot min: 1577, snapshot max: 1577 snapshot count: 0, oldest timestamp: (1712630583, 1) , meta checkpoint timestamp: (1712630883, 1) base write gen: 33"}}
{"t":{"$date":"2024-04-09T02:49:04.712+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"Checkpointer","msg":"WiredTiger message","attr":{"message":"[1712630944:712817][1780:0x7f647559a700], WT_SESSION.checkpoint: [WT_VERB_CHECKPOINT_PROGRESS] saving checkpoint snapshot min: 1592, snapshot max: 1592 snapshot count: 0, oldest timestamp: (1712630643, 1) , meta checkpoint timestamp: (1712630943, 1) base write gen: 33"}}
{"t":{"$date":"2024-04-09T02:50:04.754+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"Checkpointer","msg":"WiredTiger message","attr":{"message":"[1712631004:754382][1780:0x7f647559a700], WT_SESSION.checkpoint: [WT_VERB_CHECKPOINT_PROGRESS] saving checkpoint snapshot min: 1607, snapshot max: 1607 snapshot count: 0, oldest timestamp: (1712630703, 1) , meta checkpoint timestamp: (1712631003, 1) base write gen: 33"}}
{"t":{"$date":"2024-04-09T02:51:04.844+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"Checkpointer","msg":"WiredTiger message","attr":{"message":"[1712631064:844525][1780:0x7f647559a700], WT_SESSION.checkpoint: [WT_VERB_CHECKPOINT_PROGRESS] saving checkpoint snapshot min: 1622, snapshot max: 1622 snapshot count: 0, oldest timestamp: (1712630763, 1) , meta checkpoint timestamp: (1712631063, 1) base write gen: 33"}}
{"t":{"$date":"2024-04-09T02:52:04.905+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"Checkpointer","msg":"WiredTiger message","attr":{"message":"[1712631124:905685][1780:0x7f647559a700], WT_SESSION.checkpoint: [WT_VERB_CHECKPOINT_PROGRESS] saving checkpoint snapshot min: 1637, snapshot max: 1637 snapshot count: 0, oldest timestamp: (1712630823, 1) , meta checkpoint timestamp: (1712631123, 1) base write gen: 33"}}
{"t":{"$date":"2024-04-09T02:53:04.939+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"Checkpointer","msg":"WiredTiger message","attr":{"message":"[1712631184:939579][1780:0x7f647559a700], WT_SESSION.checkpoint: [WT_VERB_CHECKPOINT_PROGRESS] saving checkpoint snapshot min: 1652, snapshot max: 1652 snapshot count: 0, oldest timestamp: (1712630883, 1) , meta checkpoint timestamp: (1712631183, 1) base write gen: 33"}}
{"t":{"$date":"2024-04-09T02:54:04.986+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"Checkpointer","msg":"WiredTiger message","attr":{"message":"[1712631244:986407][1780:0x7f647559a700], WT_SESSION.checkpoint: [WT_VERB_CHECKPOINT_PROGRESS] saving checkpoint snapshot min: 1667, snapshot max: 1667 snapshot count: 0, oldest timestamp: (1712630943, 1) , meta checkpoint timestamp: (1712631243, 1) base write gen: 33"}}
{"t":{"$date":"2024-04-09T02:55:05.040+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"Checkpointer","msg":"WiredTiger message","attr":{"message":"[1712631305:40708][1780:0x7f647559a700], WT_SESSION.checkpoint: [WT_VERB_CHECKPOINT_PROGRESS] saving checkpoint snapshot min: 1682, snapshot max: 1682 snapshot count: 0, oldest timestamp: (1712631003, 1) , meta checkpoint timestamp: (1712631303, 1) base write gen: 33"}}
{"t":{"$date":"2024-04-09T02:56:05.096+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"Checkpointer","msg":"WiredTiger message","attr":{"message":"[1712631365:96539][1780:0x7f647559a700], WT_SESSION.checkpoint: [WT_VERB_CHECKPOINT_PROGRESS] saving checkpoint snapshot min: 1697, snapshot max: 1697 snapshot count: 0, oldest timestamp: (1712631063, 1) , meta checkpoint timestamp: (1712631363, 1) base write gen: 33"}}
{"t":{"$date":"2024-04-09T02:57:05.139+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"Checkpointer","msg":"WiredTiger message","attr":{"message":"[1712631425:139099][1780:0x7f647559a700], WT_SESSION.checkpoint: [WT_VERB_CHECKPOINT_PROGRESS] saving checkpoint snapshot min: 1712, snapshot max: 1712 snapshot count: 0, oldest timestamp: (1712631123, 1) , meta checkpoint timestamp: (1712631423, 1) base write gen: 33"}}
{"t":{"$date":"2024-04-09T02:58:05.182+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"Checkpointer","msg":"WiredTiger message","attr":{"message":"[1712631485:182960][1780:0x7f647559a700], WT_SESSION.checkpoint: [WT_VERB_CHECKPOINT_PROGRESS] saving checkpoint snapshot min: 1727, snapshot max: 1727 snapshot count: 0, oldest timestamp: (1712631183, 1) , meta checkpoint timestamp: (1712631483, 1) base write gen: 33"}}
{"t":{"$date":"2024-04-09T02:59:05.230+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"Checkpointer","msg":"WiredTiger message","attr":{"message":"[1712631545:230236][1780:0x7f647559a700], WT_SESSION.checkpoint: [WT_VERB_CHECKPOINT_PROGRESS] saving checkpoint snapshot min: 1742, snapshot max: 1742 snapshot count: 0, oldest timestamp: (1712631243, 1) , meta checkpoint timestamp: (1712631543, 1) base write gen: 33"}}
{"t":{"$date":"2024-04-09T03:00:05.264+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"Checkpointer","msg":"WiredTiger message","attr":{"message":"[1712631605:264879][1780:0x7f647559a700], WT_SESSION.checkpoint: [WT_VERB_CHECKPOINT_PROGRESS] saving checkpoint snapshot min: 1757, snapshot max: 1757 snapshot count: 0, oldest timestamp: (1712631303, 1) , meta checkpoint timestamp: (1712631603, 1) base write gen: 33"}}
{"t":{"$date":"2024-04-09T03:01:05.349+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"Checkpointer","msg":"WiredTiger message","attr":{"message":"[1712631665:349175][1780:0x7f647559a700], WT_SESSION.checkpoint: [WT_VERB_CHECKPOINT_PROGRESS] saving checkpoint snapshot min: 1775, snapshot max: 1775 snapshot count: 0, oldest timestamp: (1712631362, 3) , meta checkpoint timestamp: (1712631662, 3) base write gen: 33"}}
{"t":{"$date":"2024-04-09T03:01:09.224+00:00"},"s":"I",  "c":"-",        "id":20883,   "ctx":"conn9","msg":"Interrupted operation as its client disconnected","attr":{"opId":32950}}
{"t":{"$date":"2024-04-09T03:01:09.225+00:00"},"s":"I",  "c":"NETWORK",  "id":22944,   "ctx":"conn11","msg":"Connection ended","attr":{"remote":"127.0.0.1:60858","uuid":"5b6b6b16-0715-4612-b326-a88d9d3a4e40","connectionId":11,"connectionCount":8}}
{"t":{"$date":"2024-04-09T03:01:09.228+00:00"},"s":"I",  "c":"NETWORK",  "id":22944,   "ctx":"conn10","msg":"Connection ended","attr":{"remote":"127.0.0.1:33064","uuid":"9c61b2d7-de1a-4395-9327-31ce98642164","connectionId":10,"connectionCount":7}}
{"t":{"$date":"2024-04-09T03:01:09.235+00:00"},"s":"I",  "c":"NETWORK",  "id":22944,   "ctx":"conn9","msg":"Connection ended","attr":{"remote":"127.0.0.1:33050","uuid":"e8ebaea7-d809-4a42-9199-fe038cc65374","connectionId":9,"connectionCount":6}}
{"t":{"$date":"2024-04-09T03:01:09.354+00:00"},"s":"I",  "c":"NETWORK",  "id":22944,   "ctx":"conn37","msg":"Connection ended","attr":{"remote":"127.0.0.1:39948","uuid":"78c5ca8f-b2a1-407f-8822-f1814cf11e8f","connectionId":37,"connectionCount":5}}
{"t":{"$date":"2024-04-09T03:01:09.356+00:00"},"s":"I",  "c":"NETWORK",  "id":22944,   "ctx":"conn36","msg":"Connection ended","attr":{"remote":"127.0.0.1:41354","uuid":"8f5b2f8f-cf7a-47a6-a5a3-24e6bee18ed5","connectionId":36,"connectionCount":3}}
{"t":{"$date":"2024-04-09T03:01:09.356+00:00"},"s":"I",  "c":"NETWORK",  "id":22944,   "ctx":"conn35","msg":"Connection ended","attr":{"remote":"127.0.0.1:41342","uuid":"c3b1e1b9-af5e-455a-8664-339e958a8a45","connectionId":35,"connectionCount":2}}
{"t":{"$date":"2024-04-09T03:01:09.354+00:00"},"s":"I",  "c":"NETWORK",  "id":22944,   "ctx":"conn34","msg":"Connection ended","attr":{"remote":"127.0.0.1:36416","uuid":"7084596d-16b5-4ce3-9fa8-38c80af0e2d5","connectionId":34,"connectionCount":4}}
{"t":{"$date":"2024-04-09T03:01:09.358+00:00"},"s":"I",  "c":"-",        "id":20883,   "ctx":"conn7","msg":"Interrupted operation as its client disconnected","attr":{"opId":32888}}
{"t":{"$date":"2024-04-09T03:01:09.363+00:00"},"s":"I",  "c":"NETWORK",  "id":22944,   "ctx":"conn8","msg":"Connection ended","attr":{"remote":"127.0.0.1:33034","uuid":"b0317491-5340-4ad9-b287-eb013249a4dd","connectionId":8,"connectionCount":1}}
{"t":{"$date":"2024-04-09T03:01:09.364+00:00"},"s":"I",  "c":"NETWORK",  "id":22944,   "ctx":"conn7","msg":"Connection ended","attr":{"remote":"127.0.0.1:33032","uuid":"8cef55fd-01ea-49f0-9227-3b9fc41f9b98","connectionId":7,"connectionCount":0}}
{"t":{"$date":"2024-04-09T03:01:16.961+00:00"},"s":"I",  "c":"CONTROL",  "id":23377,   "ctx":"SignalHandler","msg":"Received signal","attr":{"signal":15,"error":"Terminated"}}
{"t":{"$date":"2024-04-09T03:01:16.961+00:00"},"s":"I",  "c":"CONTROL",  "id":23378,   "ctx":"SignalHandler","msg":"Signal was sent by kill(2)","attr":{"pid":1,"uid":0}}
{"t":{"$date":"2024-04-09T03:01:16.962+00:00"},"s":"I",  "c":"CONTROL",  "id":23381,   "ctx":"SignalHandler","msg":"will terminate after current cmd ends"}
{"t":{"$date":"2024-04-09T03:01:16.963+00:00"},"s":"I",  "c":"REPL",     "id":4784900, "ctx":"SignalHandler","msg":"Stepping down the ReplicationCoordinator for shutdown","attr":{"waitTimeMillis":15000}}
{"t":{"$date":"2024-04-09T03:01:16.964+00:00"},"s":"I",  "c":"REPL",     "id":4794602, "ctx":"SignalHandler","msg":"Attempting to enter quiesce mode"}
{"t":{"$date":"2024-04-09T03:01:16.964+00:00"},"s":"I",  "c":"COMMAND",  "id":4784901, "ctx":"SignalHandler","msg":"Shutting down the MirrorMaestro"}
{"t":{"$date":"2024-04-09T03:01:16.964+00:00"},"s":"I",  "c":"REPL",     "id":40441,   "ctx":"SignalHandler","msg":"Stopping TopologyVersionObserver"}
{"t":{"$date":"2024-04-09T03:01:16.965+00:00"},"s":"I",  "c":"REPL",     "id":40447,   "ctx":"TopologyVersionObserver","msg":"Stopped TopologyVersionObserver"}
{"t":{"$date":"2024-04-09T03:01:16.966+00:00"},"s":"I",  "c":"SHARDING", "id":4784902, "ctx":"SignalHandler","msg":"Shutting down the WaitForMajorityService"}
{"t":{"$date":"2024-04-09T03:01:16.969+00:00"},"s":"I",  "c":"CONTROL",  "id":4784903, "ctx":"SignalHandler","msg":"Shutting down the LogicalSessionCache"}
{"t":{"$date":"2024-04-09T03:01:16.970+00:00"},"s":"I",  "c":"NETWORK",  "id":20562,   "ctx":"SignalHandler","msg":"Shutdown: going to close listening sockets"}
{"t":{"$date":"2024-04-09T03:01:16.971+00:00"},"s":"I",  "c":"NETWORK",  "id":23017,   "ctx":"listener","msg":"removing socket file","attr":{"path":"/tmp/mongodb-27017.sock"}}
{"t":{"$date":"2024-04-09T03:01:16.972+00:00"},"s":"I",  "c":"NETWORK",  "id":4784905, "ctx":"SignalHandler","msg":"Shutting down the global connection pool"}
{"t":{"$date":"2024-04-09T03:01:16.972+00:00"},"s":"I",  "c":"CONTROL",  "id":4784906, "ctx":"SignalHandler","msg":"Shutting down the FlowControlTicketholder"}
{"t":{"$date":"2024-04-09T03:01:16.973+00:00"},"s":"I",  "c":"-",        "id":20520,   "ctx":"SignalHandler","msg":"Stopping further Flow Control ticket acquisitions."}
{"t":{"$date":"2024-04-09T03:01:16.973+00:00"},"s":"I",  "c":"REPL",     "id":4784907, "ctx":"SignalHandler","msg":"Shutting down the replica set node executor"}
{"t":{"$date":"2024-04-09T03:01:16.974+00:00"},"s":"I",  "c":"ASIO",     "id":22582,   "ctx":"ReplNodeDbWorkerNetwork","msg":"Killing all outstanding egress activity."}
{"t":{"$date":"2024-04-09T03:01:16.975+00:00"},"s":"I",  "c":"CONTROL",  "id":4784908, "ctx":"SignalHandler","msg":"Shutting down the PeriodicThreadToAbortExpiredTransactions"}
{"t":{"$date":"2024-04-09T03:01:16.975+00:00"},"s":"I",  "c":"REPL",     "id":4784909, "ctx":"SignalHandler","msg":"Shutting down the ReplicationCoordinator"}
{"t":{"$date":"2024-04-09T03:01:16.976+00:00"},"s":"I",  "c":"REPL",     "id":5074000, "ctx":"SignalHandler","msg":"Shutting down the replica set aware services."}
{"t":{"$date":"2024-04-09T03:01:16.976+00:00"},"s":"I",  "c":"REPL",     "id":5123006, "ctx":"SignalHandler","msg":"Shutting down PrimaryOnlyService","attr":{"service":"TenantMigrationDonorService","numInstances":0,"numOperationContexts":0}}
{"t":{"$date":"2024-04-09T03:01:16.977+00:00"},"s":"I",  "c":"ASIO",     "id":22582,   "ctx":"TenantMigrationDonorServiceNetwork","msg":"Killing all outstanding egress activity."}
{"t":{"$date":"2024-04-09T03:01:16.978+00:00"},"s":"I",  "c":"REPL",     "id":5123006, "ctx":"SignalHandler","msg":"Shutting down PrimaryOnlyService","attr":{"service":"TenantMigrationRecipientService","numInstances":0,"numOperationContexts":0}}
{"t":{"$date":"2024-04-09T03:01:16.978+00:00"},"s":"I",  "c":"ASIO",     "id":22582,   "ctx":"TenantMigrationRecipientServiceNetwork","msg":"Killing all outstanding egress activity."}
{"t":{"$date":"2024-04-09T03:01:16.979+00:00"},"s":"I",  "c":"REPL",     "id":21328,   "ctx":"SignalHandler","msg":"Shutting down replication subsystems"}
{"t":{"$date":"2024-04-09T03:01:16.979+00:00"},"s":"I",  "c":"REPL",     "id":21302,   "ctx":"SignalHandler","msg":"Stopping replication reporter thread"}
{"t":{"$date":"2024-04-09T03:01:16.980+00:00"},"s":"I",  "c":"REPL",     "id":21303,   "ctx":"SignalHandler","msg":"Stopping replication fetcher thread"}
{"t":{"$date":"2024-04-09T03:01:16.981+00:00"},"s":"I",  "c":"REPL",     "id":21107,   "ctx":"BackgroundSync","msg":"Stopping replication producer"}
{"t":{"$date":"2024-04-09T03:01:16.981+00:00"},"s":"I",  "c":"REPL",     "id":21304,   "ctx":"SignalHandler","msg":"Stopping replication applier thread"}
{"t":{"$date":"2024-04-09T03:01:17.012+00:00"},"s":"I",  "c":"REPL",     "id":21225,   "ctx":"OplogApplier-0","msg":"Finished oplog application"}
{"t":{"$date":"2024-04-09T03:01:17.012+00:00"},"s":"I",  "c":"REPL",     "id":5698300, "ctx":"SignalHandler","msg":"Stopping replication applier writer pool"}
{"t":{"$date":"2024-04-09T03:01:17.013+00:00"},"s":"I",  "c":"REPL",     "id":21307,   "ctx":"SignalHandler","msg":"Stopping replication storage threads"}
{"t":{"$date":"2024-04-09T03:01:17.015+00:00"},"s":"I",  "c":"ASIO",     "id":22582,   "ctx":"OplogApplierNetwork","msg":"Killing all outstanding egress activity."}
{"t":{"$date":"2024-04-09T03:01:17.016+00:00"},"s":"I",  "c":"ASIO",     "id":22582,   "ctx":"SignalHandler","msg":"Killing all outstanding egress activity."}
{"t":{"$date":"2024-04-09T03:01:17.017+00:00"},"s":"I",  "c":"ASIO",     "id":22582,   "ctx":"ReplCoordExternNetwork","msg":"Killing all outstanding egress activity."}
{"t":{"$date":"2024-04-09T03:01:17.021+00:00"},"s":"I",  "c":"ASIO",     "id":22582,   "ctx":"ReplNetwork","msg":"Killing all outstanding egress activity."}
{"t":{"$date":"2024-04-09T03:01:17.022+00:00"},"s":"I",  "c":"SHARDING", "id":4784910, "ctx":"SignalHandler","msg":"Shutting down the ShardingInitializationMongoD"}
{"t":{"$date":"2024-04-09T03:01:17.023+00:00"},"s":"I",  "c":"REPL",     "id":4784911, "ctx":"SignalHandler","msg":"Enqueuing the ReplicationStateTransitionLock for shutdown"}
{"t":{"$date":"2024-04-09T03:01:17.023+00:00"},"s":"I",  "c":"-",        "id":4784912, "ctx":"SignalHandler","msg":"Killing all operations for shutdown"}
{"t":{"$date":"2024-04-09T03:01:17.024+00:00"},"s":"I",  "c":"-",        "id":4695300, "ctx":"SignalHandler","msg":"Interrupted all currently running operations","attr":{"opsKilled":5}}
{"t":{"$date":"2024-04-09T03:01:17.025+00:00"},"s":"I",  "c":"TENANT_M", "id":5093807, "ctx":"SignalHandler","msg":"Shutting down all TenantMigrationAccessBlockers on global shutdown"}
{"t":{"$date":"2024-04-09T03:01:17.026+00:00"},"s":"I",  "c":"COMMAND",  "id":4784913, "ctx":"SignalHandler","msg":"Shutting down all open transactions"}
{"t":{"$date":"2024-04-09T03:01:17.027+00:00"},"s":"I",  "c":"REPL",     "id":4784914, "ctx":"SignalHandler","msg":"Acquiring the ReplicationStateTransitionLock for shutdown"}
{"t":{"$date":"2024-04-09T03:01:17.027+00:00"},"s":"I",  "c":"INDEX",    "id":4784915, "ctx":"SignalHandler","msg":"Shutting down the IndexBuildsCoordinator"}
{"t":{"$date":"2024-04-09T03:01:17.028+00:00"},"s":"I",  "c":"REPL",     "id":4784916, "ctx":"SignalHandler","msg":"Reacquiring the ReplicationStateTransitionLock for shutdown"}
{"t":{"$date":"2024-04-09T03:01:17.030+00:00"},"s":"I",  "c":"REPL",     "id":4784917, "ctx":"SignalHandler","msg":"Attempting to mark clean shutdown"}
{"t":{"$date":"2024-04-09T03:01:17.030+00:00"},"s":"I",  "c":"NETWORK",  "id":4784918, "ctx":"SignalHandler","msg":"Shutting down the ReplicaSetMonitor"}
{"t":{"$date":"2024-04-09T03:01:17.031+00:00"},"s":"I",  "c":"REPL",     "id":4784920, "ctx":"SignalHandler","msg":"Shutting down the LogicalTimeValidator"}
{"t":{"$date":"2024-04-09T03:01:17.032+00:00"},"s":"I",  "c":"SHARDING", "id":4784921, "ctx":"SignalHandler","msg":"Shutting down the MigrationUtilExecutor"}
{"t":{"$date":"2024-04-09T03:01:17.033+00:00"},"s":"I",  "c":"ASIO",     "id":22582,   "ctx":"MigrationUtil-TaskExecutor","msg":"Killing all outstanding egress activity."}
{"t":{"$date":"2024-04-09T03:01:17.034+00:00"},"s":"I",  "c":"COMMAND",  "id":4784923, "ctx":"SignalHandler","msg":"Shutting down the ServiceEntryPoint"}
{"t":{"$date":"2024-04-09T03:01:17.035+00:00"},"s":"I",  "c":"CONTROL",  "id":4784927, "ctx":"SignalHandler","msg":"Shutting down the HealthLog"}
{"t":{"$date":"2024-04-09T03:01:17.036+00:00"},"s":"I",  "c":"CONTROL",  "id":4784928, "ctx":"SignalHandler","msg":"Shutting down the TTL monitor"}
{"t":{"$date":"2024-04-09T03:01:17.036+00:00"},"s":"I",  "c":"INDEX",    "id":3684100, "ctx":"SignalHandler","msg":"Shutting down TTL collection monitor thread"}
{"t":{"$date":"2024-04-09T03:01:17.037+00:00"},"s":"I",  "c":"INDEX",    "id":3684101, "ctx":"SignalHandler","msg":"Finished shutting down TTL collection monitor thread"}
{"t":{"$date":"2024-04-09T03:01:17.037+00:00"},"s":"I",  "c":"CONTROL",  "id":4784929, "ctx":"SignalHandler","msg":"Acquiring the global lock for shutdown"}
{"t":{"$date":"2024-04-09T03:01:17.038+00:00"},"s":"I",  "c":"CONTROL",  "id":4784930, "ctx":"SignalHandler","msg":"Shutting down the storage engine"}
{"t":{"$date":"2024-04-09T03:01:17.039+00:00"},"s":"I",  "c":"STORAGE",  "id":22320,   "ctx":"SignalHandler","msg":"Shutting down journal flusher thread"}
{"t":{"$date":"2024-04-09T03:01:17.040+00:00"},"s":"I",  "c":"STORAGE",  "id":22321,   "ctx":"SignalHandler","msg":"Finished shutting down journal flusher thread"}
{"t":{"$date":"2024-04-09T03:01:17.041+00:00"},"s":"I",  "c":"STORAGE",  "id":22322,   "ctx":"SignalHandler","msg":"Shutting down checkpoint thread"}
{"t":{"$date":"2024-04-09T03:01:17.041+00:00"},"s":"I",  "c":"STORAGE",  "id":22323,   "ctx":"SignalHandler","msg":"Finished shutting down checkpoint thread"}
{"t":{"$date":"2024-04-09T03:01:17.042+00:00"},"s":"I",  "c":"STORAGE",  "id":22261,   "ctx":"SignalHandler","msg":"Timestamp monitor shutting down"}
{"t":{"$date":"2024-04-09T03:01:17.043+00:00"},"s":"I",  "c":"STORAGE",  "id":20282,   "ctx":"SignalHandler","msg":"Deregistering all the collections"}
{"t":{"$date":"2024-04-09T03:01:17.044+00:00"},"s":"I",  "c":"STORAGE",  "id":22372,   "ctx":"OplogVisibilityThread","msg":"Oplog visibility thread shutting down."}
{"t":{"$date":"2024-04-09T03:01:17.046+00:00"},"s":"I",  "c":"STORAGE",  "id":22317,   "ctx":"SignalHandler","msg":"WiredTigerKVEngine shutting down"}
{"t":{"$date":"2024-04-09T03:01:17.047+00:00"},"s":"I",  "c":"STORAGE",  "id":22318,   "ctx":"SignalHandler","msg":"Shutting down session sweeper thread"}
{"t":{"$date":"2024-04-09T03:01:17.047+00:00"},"s":"I",  "c":"STORAGE",  "id":22319,   "ctx":"SignalHandler","msg":"Finished shutting down session sweeper thread"}
{"t":{"$date":"2024-04-09T03:01:17.049+00:00"},"s":"I",  "c":"STORAGE",  "id":4795902, "ctx":"SignalHandler","msg":"Closing WiredTiger","attr":{"closeConfig":"leak_memory=true,"}}
{"t":{"$date":"2024-04-09T03:01:17.054+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"SignalHandler","msg":"WiredTiger message","attr":{"message":"[1712631677:54305][1780:0x7f647e5c0700], WT_CONNECTION.close: [WT_VERB_RECOVERY_PROGRESS] shutdown rollback to stable has successfully finished and ran for 3 milliseconds"}}
{"t":{"$date":"2024-04-09T03:01:17.068+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"SignalHandler","msg":"WiredTiger message","attr":{"message":"[1712631677:68848][1780:0x7f647e5c0700], close_ckpt: [WT_VERB_CHECKPOINT_PROGRESS] saving checkpoint snapshot min: 1781, snapshot max: 1781 snapshot count: 0, oldest timestamp: (1712631373, 1) , meta checkpoint timestamp: (1712631673, 1) base write gen: 33"}}
{"t":{"$date":"2024-04-09T03:01:17.085+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"SignalHandler","msg":"WiredTiger message","attr":{"message":"[1712631677:85941][1780:0x7f647e5c0700], WT_CONNECTION.close: [WT_VERB_RECOVERY_PROGRESS] shutdown checkpoint has successfully finished and ran for 30 milliseconds"}}
{"t":{"$date":"2024-04-09T03:01:17.086+00:00"},"s":"I",  "c":"STORAGE",  "id":22430,   "ctx":"SignalHandler","msg":"WiredTiger message","attr":{"message":"[1712631677:86802][1780:0x7f647e5c0700], WT_CONNECTION.close: [WT_VERB_RECOVERY_PROGRESS] shutdown was completed successfully and took 37ms, including 3ms for the rollback to stable, and 30ms for the checkpoint."}}
{"t":{"$date":"2024-04-09T03:01:17.340+00:00"},"s":"I",  "c":"STORAGE",  "id":4795901, "ctx":"SignalHandler","msg":"WiredTiger closed","attr":{"durationMillis":291}}
{"t":{"$date":"2024-04-09T03:01:17.341+00:00"},"s":"I",  "c":"STORAGE",  "id":22279,   "ctx":"SignalHandler","msg":"shutdown: removing fs lock..."}
{"t":{"$date":"2024-04-09T03:01:17.344+00:00"},"s":"I",  "c":"-",        "id":4784931, "ctx":"SignalHandler","msg":"Dropping the scope cache for shutdown"}
{"t":{"$date":"2024-04-09T03:01:17.345+00:00"},"s":"I",  "c":"FTDC",     "id":4784926, "ctx":"SignalHandler","msg":"Shutting down full-time data capture"}
{"t":{"$date":"2024-04-09T03:01:17.345+00:00"},"s":"I",  "c":"FTDC",     "id":20626,   "ctx":"SignalHandler","msg":"Shutting down full-time diagnostic data capture"}
{"t":{"$date":"2024-04-09T03:01:17.352+00:00"},"s":"I",  "c":"CONTROL",  "id":20565,   "ctx":"SignalHandler","msg":"Now exiting"}
{"t":{"$date":"2024-04-09T03:01:17.354+00:00"},"s":"I",  "c":"CONTROL",  "id":23138,   "ctx":"SignalHandler","msg":"Shutting down","attr":{"exitCode":0}}
