2024-04-09 02:25:55,311 CRIT Supervisor is running as root.  Privileges were not dropped because no user is specified in the config file.  If you intend to run as root, you can set user=root in the config file to avoid this message.
2024-04-09 02:25:55,314 INFO Included extra file "/tmp/appsmith/supervisor-conf.d/backend.conf" during parsing
2024-04-09 02:25:55,315 INFO Included extra file "/tmp/appsmith/supervisor-conf.d/editor.conf" during parsing
2024-04-09 02:25:55,324 INFO Included extra file "/tmp/appsmith/supervisor-conf.d/mongodb.conf" during parsing
2024-04-09 02:25:55,325 INFO Included extra file "/tmp/appsmith/supervisor-conf.d/postgres.conf" during parsing
2024-04-09 02:25:55,327 INFO Included extra file "/tmp/appsmith/supervisor-conf.d/redis.conf" during parsing
2024-04-09 02:25:55,332 INFO Included extra file "/tmp/appsmith/supervisor-conf.d/rts.conf" during parsing
2024-04-09 02:25:55,362 INFO RPC interface 'supervisor' initialized
2024-04-09 02:25:55,367 INFO RPC interface 'supervisor' initialized
2024-04-09 02:25:55,369 CRIT Server 'unix_http_server' running without any HTTP authentication checking
2024-04-09 02:25:55,374 INFO supervisord started with pid 1
2024-04-09 02:25:55,882 INFO spawned: 'stdout' with pid 1775
2024-04-09 02:25:55,904 INFO spawned: 'redis' with pid 1776
2024-04-09 02:25:56,000 INFO spawned: 'mongodb' with pid 1780
2024-04-09 02:25:56,007 INFO spawned: 'rts' with pid 1781
2024-04-09 02:25:56,039 INFO spawned: 'backend' with pid 1782
2024-04-09 02:25:56,056 INFO spawned: 'editor' with pid 1784
2024-04-09 02:25:56,081 INFO spawned: 'postgres' with pid 1787
2024-04-09 02:25:56,127 INFO success: redis entered RUNNING state, process has stayed up for > than 0 seconds (startsecs)
2024-04-09 02:25:56,128 INFO success: rts entered RUNNING state, process has stayed up for > than 0 seconds (startsecs)
2024-04-09 02:25:56,130 INFO success: editor entered RUNNING state, process has stayed up for > than 0 seconds (startsecs)
2024-04-09 02:25:57,034 INFO success: stdout entered RUNNING state, process has stayed up for > than 1 seconds (startsecs)
2024-04-09 02:25:57,035 INFO reaped unknown pid 49
2024-04-09 02:25:57,063 INFO success: postgres entered RUNNING state, process has stayed up for > than 1 seconds (startsecs)
2024-04-09 02:26:03,561 INFO reaped unknown pid 92
2024-04-09 02:26:06,044 INFO success: mongodb entered RUNNING state, process has stayed up for > than 10 seconds (startsecs)
2024-04-09 02:26:16,083 INFO success: backend entered RUNNING state, process has stayed up for > than 20 seconds (startsecs)
2024-04-09 03:01:08,547 WARN received SIGTERM indicating exit request
2024-04-09 03:01:08,559 INFO waiting for stdout, redis, mongodb, rts, backend, editor, postgres to die
2024-04-09 03:01:08,832 INFO stopped: postgres (exit status 0)
2024-04-09 03:01:08,942 INFO stopped: editor (exit status 2)
2024-04-09 03:01:12,129 INFO waiting for stdout, redis, mongodb, rts, backend to die
2024-04-09 03:01:15,135 INFO waiting for stdout, redis, mongodb, rts, backend to die
2024-04-09 03:01:16,790 INFO stopped: backend (exit status 143)
2024-04-09 03:01:16,793 INFO reaped unknown pid 1870
2024-04-09 03:01:16,959 INFO stopped: rts (terminated by SIGTERM)
2024-04-09 03:01:17,522 INFO stopped: mongodb (exit status 0)
2024-04-09 03:01:17,750 INFO stopped: redis (exit status 0)
2024-04-09 03:01:17,770 INFO stopped: stdout (terminated by SIGTERM)
2024-04-09 03:11:19,455 CRIT Supervisor is running as root.  Privileges were not dropped because no user is specified in the config file.  If you intend to run as root, you can set user=root in the config file to avoid this message.
2024-04-09 03:11:19,456 INFO Included extra file "/tmp/appsmith/supervisor-conf.d/backend.conf" during parsing
2024-04-09 03:11:19,457 INFO Included extra file "/tmp/appsmith/supervisor-conf.d/editor.conf" during parsing
2024-04-09 03:11:19,458 INFO Included extra file "/tmp/appsmith/supervisor-conf.d/mongodb.conf" during parsing
2024-04-09 03:11:19,458 INFO Included extra file "/tmp/appsmith/supervisor-conf.d/postgres.conf" during parsing
2024-04-09 03:11:19,459 INFO Included extra file "/tmp/appsmith/supervisor-conf.d/redis.conf" during parsing
2024-04-09 03:11:19,461 INFO Included extra file "/tmp/appsmith/supervisor-conf.d/rts.conf" during parsing
2024-04-09 03:11:19,498 INFO RPC interface 'supervisor' initialized
2024-04-09 03:11:19,500 INFO RPC interface 'supervisor' initialized
2024-04-09 03:11:19,500 CRIT Server 'unix_http_server' running without any HTTP authentication checking
2024-04-09 03:11:19,501 INFO supervisord started with pid 1
2024-04-09 03:11:20,363 INFO spawned: 'stdout' with pid 1501
2024-04-09 03:11:20,374 INFO spawned: 'redis' with pid 1502
2024-04-09 03:11:20,383 INFO spawned: 'mongodb' with pid 1503
2024-04-09 03:11:20,392 INFO spawned: 'rts' with pid 1504
2024-04-09 03:11:20,408 INFO spawned: 'backend' with pid 1508
2024-04-09 03:11:20,421 INFO spawned: 'editor' with pid 1510
2024-04-09 03:11:20,427 INFO spawned: 'postgres' with pid 1512
2024-04-09 03:11:20,447 INFO success: redis entered RUNNING state, process has stayed up for > than 0 seconds (startsecs)
2024-04-09 03:11:20,448 INFO success: rts entered RUNNING state, process has stayed up for > than 0 seconds (startsecs)
2024-04-09 03:11:20,449 INFO success: editor entered RUNNING state, process has stayed up for > than 0 seconds (startsecs)
2024-04-09 03:11:20,859 INFO reaped unknown pid 47
2024-04-09 03:11:21,515 INFO success: stdout entered RUNNING state, process has stayed up for > than 1 seconds (startsecs)
2024-04-09 03:11:21,517 INFO success: postgres entered RUNNING state, process has stayed up for > than 1 seconds (startsecs)
2024-04-09 03:11:26,593 INFO reaped unknown pid 72
2024-04-09 03:11:30,997 INFO success: mongodb entered RUNNING state, process has stayed up for > than 10 seconds (startsecs)
2024-04-09 03:11:40,436 INFO success: backend entered RUNNING state, process has stayed up for > than 20 seconds (startsecs)
2024-04-09 03:55:01,497 INFO reaped unknown pid 3772
2024-04-09 03:57:46,727 INFO reaped unknown pid 3797
2024-04-09 04:26:55,398 INFO reaped unknown pid 3842
2024-04-09 04:27:22,231 INFO reaped unknown pid 3850
2024-04-09 04:59:47,511 INFO reaped unknown pid 3878
2024-04-09 05:00:08,271 INFO reaped unknown pid 3886
2024-04-09 05:24:24,484 WARN received SIGTERM indicating exit request
2024-04-09 05:24:24,494 INFO waiting for stdout, redis, mongodb, rts, backend, editor, postgres to die
2024-04-09 05:24:25,789 INFO stopped: postgres (exit status 0)
2024-04-09 05:24:25,888 INFO stopped: editor (exit status 2)
2024-04-09 05:24:28,100 INFO waiting for stdout, redis, mongodb, rts, backend to die
2024-04-09 05:24:31,107 INFO waiting for stdout, redis, mongodb, rts, backend to die
2024-04-09 05:24:32,869 INFO stopped: backend (exit status 143)
2024-04-09 05:24:32,871 INFO reaped unknown pid 1592
2024-04-09 05:24:34,064 INFO stopped: rts (terminated by SIGTERM)
